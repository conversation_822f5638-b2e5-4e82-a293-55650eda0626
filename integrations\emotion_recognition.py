"""
Hamilton AI Assistant - Emotion Recognition System
Sistema de reconocimiento de emociones en voz y rostro
"""

import logging
import json
import cv2
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import asyncio

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class Emotion(Enum):
    """Emociones reconocibles"""
    HAPPY = "happy"
    SAD = "sad"
    ANGRY = "angry"
    SURPRISED = "surprised"
    FEARFUL = "fearful"
    DISGUSTED = "disgusted"
    NEUTRAL = "neutral"
    EXCITED = "excited"
    CALM = "calm"
    FRUSTRATED = "frustrated"
    CONFUSED = "confused"
    TIRED = "tired"

class EmotionSource(Enum):
    """Fuente de detección de emoción"""
    FACIAL = "facial"
    VOCAL = "vocal"
    COMBINED = "combined"

@dataclass
class EmotionReading:
    """Lectura de emoción"""
    emotion: Emotion
    confidence: float
    source: EmotionSource
    timestamp: datetime
    additional_data: Dict[str, Any] = None

@dataclass
class EmotionalState:
    """Estado emocional del usuario"""
    primary_emotion: Emotion
    secondary_emotions: List[Tuple[Emotion, float]]
    overall_mood: str  # positive, negative, neutral
    energy_level: str  # high, medium, low
    stress_level: str  # high, medium, low
    last_updated: datetime
    confidence: float

class EmotionRecognitionSystem:
    """Sistema principal de reconocimiento de emociones"""
    
    def __init__(self):
        self.facial_analyzer = FacialEmotionAnalyzer()
        self.vocal_analyzer = VocalEmotionAnalyzer()
        self.emotion_history: List[EmotionReading] = []
        self.current_state: Optional[EmotionalState] = None
        self.config_file = Path("config/emotion_config.json")
        self.data_file = Path("data/emotion_history.json")
        
        self.load_configuration()
        self._setup_emotion_mapping()
    
    def load_configuration(self):
        """Carga configuración del sistema de emociones"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de emociones: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.config = {
            "facial_recognition": {
                "enabled": True,
                "confidence_threshold": 0.6,
                "update_interval_seconds": 2,
                "emotion_smoothing": True,
                "face_detection_model": "haarcascade"
            },
            "vocal_recognition": {
                "enabled": True,
                "confidence_threshold": 0.5,
                "analysis_window_seconds": 3,
                "features": ["pitch", "energy", "tempo", "spectral"]
            },
            "emotion_responses": {
                "happy": {
                    "response_style": "enthusiastic",
                    "voice_tone": "cheerful",
                    "suggestions": ["Continue with positive activities"]
                },
                "sad": {
                    "response_style": "empathetic",
                    "voice_tone": "gentle",
                    "suggestions": ["Would you like to talk about it?", "Perhaps some music?"]
                },
                "angry": {
                    "response_style": "calm",
                    "voice_tone": "soothing",
                    "suggestions": ["Take a deep breath", "Would you like some relaxing music?"]
                },
                "tired": {
                    "response_style": "understanding",
                    "voice_tone": "soft",
                    "suggestions": ["Perhaps you should rest", "Would you like me to dim the lights?"]
                },
                "stressed": {
                    "response_style": "supportive",
                    "voice_tone": "reassuring",
                    "suggestions": ["Let's take a moment to relax", "Would you like a break reminder?"]
                }
            },
            "adaptive_behavior": {
                "adjust_response_style": True,
                "adjust_voice_tone": True,
                "suggest_actions": True,
                "learn_patterns": True
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def _setup_emotion_mapping(self):
        """Configura mapeo de emociones"""
        # Mapeo de emociones a estados de ánimo
        self.emotion_to_mood = {
            Emotion.HAPPY: "positive",
            Emotion.EXCITED: "positive",
            Emotion.CALM: "positive",
            Emotion.SAD: "negative",
            Emotion.ANGRY: "negative",
            Emotion.FRUSTRATED: "negative",
            Emotion.FEARFUL: "negative",
            Emotion.DISGUSTED: "negative",
            Emotion.NEUTRAL: "neutral",
            Emotion.SURPRISED: "neutral",
            Emotion.CONFUSED: "neutral",
            Emotion.TIRED: "neutral"
        }
        
        # Mapeo de emociones a nivel de energía
        self.emotion_to_energy = {
            Emotion.EXCITED: "high",
            Emotion.ANGRY: "high",
            Emotion.SURPRISED: "high",
            Emotion.HAPPY: "medium",
            Emotion.FRUSTRATED: "medium",
            Emotion.CONFUSED: "medium",
            Emotion.NEUTRAL: "medium",
            Emotion.CALM: "low",
            Emotion.SAD: "low",
            Emotion.TIRED: "low",
            Emotion.FEARFUL: "low",
            Emotion.DISGUSTED: "low"
        }
    
    async def analyze_current_emotion(self, image_frame: np.ndarray = None, 
                                    audio_data: np.ndarray = None) -> Optional[EmotionReading]:
        """Analiza emoción actual usando imagen y/o audio"""
        readings = []
        
        # Análisis facial si hay imagen
        if image_frame is not None and self.config["facial_recognition"]["enabled"]:
            facial_emotion = await self.facial_analyzer.analyze_emotion(image_frame)
            if facial_emotion:
                readings.append(EmotionReading(
                    emotion=facial_emotion["emotion"],
                    confidence=facial_emotion["confidence"],
                    source=EmotionSource.FACIAL,
                    timestamp=datetime.now(),
                    additional_data=facial_emotion.get("details", {})
                ))
        
        # Análisis vocal si hay audio
        if audio_data is not None and self.config["vocal_recognition"]["enabled"]:
            vocal_emotion = await self.vocal_analyzer.analyze_emotion(audio_data)
            if vocal_emotion:
                readings.append(EmotionReading(
                    emotion=vocal_emotion["emotion"],
                    confidence=vocal_emotion["confidence"],
                    source=EmotionSource.VOCAL,
                    timestamp=datetime.now(),
                    additional_data=vocal_emotion.get("details", {})
                ))
        
        # Combinar lecturas si hay múltiples fuentes
        if len(readings) > 1:
            combined_emotion = self._combine_emotion_readings(readings)
            if combined_emotion:
                combined_reading = EmotionReading(
                    emotion=combined_emotion["emotion"],
                    confidence=combined_emotion["confidence"],
                    source=EmotionSource.COMBINED,
                    timestamp=datetime.now(),
                    additional_data={"component_readings": readings}
                )
                readings.append(combined_reading)
                return combined_reading
        elif len(readings) == 1:
            return readings[0]
        
        return None
    
    def _combine_emotion_readings(self, readings: List[EmotionReading]) -> Optional[Dict[str, Any]]:
        """Combina múltiples lecturas de emoción"""
        if not readings:
            return None
        
        # Ponderar por confianza y fuente
        weights = {
            EmotionSource.FACIAL: 0.6,
            EmotionSource.VOCAL: 0.4
        }
        
        emotion_scores = {}
        total_weight = 0
        
        for reading in readings:
            weight = weights.get(reading.source, 0.5) * reading.confidence
            emotion = reading.emotion
            
            if emotion not in emotion_scores:
                emotion_scores[emotion] = 0
            
            emotion_scores[emotion] += weight
            total_weight += weight
        
        if total_weight == 0:
            return None
        
        # Normalizar puntuaciones
        for emotion in emotion_scores:
            emotion_scores[emotion] /= total_weight
        
        # Seleccionar emoción con mayor puntuación
        best_emotion = max(emotion_scores, key=emotion_scores.get)
        confidence = emotion_scores[best_emotion]
        
        return {
            "emotion": best_emotion,
            "confidence": confidence,
            "all_scores": emotion_scores
        }
    
    def update_emotional_state(self, reading: EmotionReading):
        """Actualiza estado emocional basado en nueva lectura"""
        # Agregar a historial
        self.emotion_history.append(reading)
        
        # Mantener solo últimas 100 lecturas
        if len(self.emotion_history) > 100:
            self.emotion_history = self.emotion_history[-100:]
        
        # Calcular estado emocional actual
        recent_readings = self._get_recent_readings(minutes=5)
        if recent_readings:
            self.current_state = self._calculate_emotional_state(recent_readings)
            logger.info(f"Estado emocional actualizado: {self.current_state.primary_emotion.value} (confianza: {self.current_state.confidence:.2f})")
    
    def _get_recent_readings(self, minutes: int = 5) -> List[EmotionReading]:
        """Obtiene lecturas recientes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [r for r in self.emotion_history if r.timestamp >= cutoff_time]
    
    def _calculate_emotional_state(self, readings: List[EmotionReading]) -> EmotionalState:
        """Calcula estado emocional basado en lecturas"""
        if not readings:
            return EmotionalState(
                primary_emotion=Emotion.NEUTRAL,
                secondary_emotions=[],
                overall_mood="neutral",
                energy_level="medium",
                stress_level="low",
                last_updated=datetime.now(),
                confidence=0.0
            )
        
        # Contar emociones con ponderación por tiempo y confianza
        emotion_weights = {}
        total_weight = 0
        
        now = datetime.now()
        for reading in readings:
            # Peso basado en recencia (más reciente = más peso)
            time_diff = (now - reading.timestamp).total_seconds()
            time_weight = max(0.1, 1.0 - (time_diff / 300))  # 5 minutos = peso mínimo
            
            # Peso total
            weight = reading.confidence * time_weight
            
            if reading.emotion not in emotion_weights:
                emotion_weights[reading.emotion] = 0
            
            emotion_weights[reading.emotion] += weight
            total_weight += weight
        
        # Normalizar
        if total_weight > 0:
            for emotion in emotion_weights:
                emotion_weights[emotion] /= total_weight
        
        # Emoción primaria
        primary_emotion = max(emotion_weights, key=emotion_weights.get)
        primary_confidence = emotion_weights[primary_emotion]
        
        # Emociones secundarias
        secondary_emotions = [(emotion, weight) for emotion, weight in emotion_weights.items() 
                            if emotion != primary_emotion and weight > 0.1]
        secondary_emotions.sort(key=lambda x: x[1], reverse=True)
        
        # Estado de ánimo general
        overall_mood = self.emotion_to_mood.get(primary_emotion, "neutral")
        
        # Nivel de energía
        energy_level = self.emotion_to_energy.get(primary_emotion, "medium")
        
        # Nivel de estrés (basado en emociones negativas)
        stress_emotions = [Emotion.ANGRY, Emotion.FRUSTRATED, Emotion.FEARFUL, Emotion.DISGUSTED]
        stress_weight = sum(emotion_weights.get(emotion, 0) for emotion in stress_emotions)
        
        if stress_weight > 0.6:
            stress_level = "high"
        elif stress_weight > 0.3:
            stress_level = "medium"
        else:
            stress_level = "low"
        
        return EmotionalState(
            primary_emotion=primary_emotion,
            secondary_emotions=secondary_emotions,
            overall_mood=overall_mood,
            energy_level=energy_level,
            stress_level=stress_level,
            last_updated=datetime.now(),
            confidence=primary_confidence
        )
    
    def get_adaptive_response_style(self) -> Dict[str, Any]:
        """Obtiene estilo de respuesta adaptado a la emoción actual"""
        if not self.current_state:
            return {"style": "neutral", "tone": "normal", "suggestions": []}
        
        emotion_config = self.config["emotion_responses"].get(
            self.current_state.primary_emotion.value, 
            {"response_style": "neutral", "voice_tone": "normal", "suggestions": []}
        )
        
        return {
            "style": emotion_config.get("response_style", "neutral"),
            "tone": emotion_config.get("voice_tone", "normal"),
            "suggestions": emotion_config.get("suggestions", []),
            "emotion": self.current_state.primary_emotion.value,
            "mood": self.current_state.overall_mood,
            "energy": self.current_state.energy_level,
            "stress": self.current_state.stress_level
        }
    
    def get_emotion_summary(self) -> Dict[str, Any]:
        """Obtiene resumen del estado emocional"""
        if not self.current_state:
            return {"status": "no_data"}
        
        return {
            "current_emotion": self.current_state.primary_emotion.value,
            "confidence": self.current_state.confidence,
            "mood": self.current_state.overall_mood,
            "energy_level": self.current_state.energy_level,
            "stress_level": self.current_state.stress_level,
            "secondary_emotions": [(e.value, c) for e, c in self.current_state.secondary_emotions],
            "last_updated": self.current_state.last_updated.isoformat(),
            "total_readings": len(self.emotion_history)
        }

class FacialEmotionAnalyzer:
    """Analizador de emociones faciales"""
    
    def __init__(self):
        self.face_cascade = None
        self._load_models()
    
    def _load_models(self):
        """Carga modelos de detección facial"""
        try:
            # Cargar clasificador Haar para detección de rostros
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            logger.info("✅ Modelos de detección facial cargados")
        except Exception as e:
            logger.error(f"Error cargando modelos faciales: {e}")
    
    async def analyze_emotion(self, image_frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Analiza emoción en imagen facial"""
        try:
            if self.face_cascade is None:
                return None
            
            # Convertir a escala de grises
            gray = cv2.cvtColor(image_frame, cv2.COLOR_BGR2GRAY)
            
            # Detectar rostros
            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                return None
            
            # Usar el rostro más grande
            face = max(faces, key=lambda f: f[2] * f[3])
            x, y, w, h = face
            
            # Extraer región facial
            face_roi = gray[y:y+h, x:x+w]
            
            # Análisis básico de emoción (simulado)
            emotion_analysis = self._analyze_facial_features(face_roi)
            
            return {
                "emotion": emotion_analysis["emotion"],
                "confidence": emotion_analysis["confidence"],
                "details": {
                    "face_region": face.tolist(),
                    "features": emotion_analysis.get("features", {})
                }
            }
            
        except Exception as e:
            logger.error(f"Error analizando emoción facial: {e}")
            return None
    
    def _analyze_facial_features(self, face_roi: np.ndarray) -> Dict[str, Any]:
        """Analiza características faciales para determinar emoción"""
        # Análisis simplificado basado en características básicas
        # En implementación real, usar modelos de deep learning
        
        # Calcular métricas básicas
        height, width = face_roi.shape
        
        # Análisis de intensidad (brillo promedio)
        avg_intensity = np.mean(face_roi)
        
        # Análisis de contraste
        contrast = np.std(face_roi)
        
        # Análisis de simetría
        left_half = face_roi[:, :width//2]
        right_half = cv2.flip(face_roi[:, width//2:], 1)
        symmetry = cv2.matchTemplate(left_half, right_half, cv2.TM_CCOEFF_NORMED)[0][0]
        
        # Heurísticas simples para determinar emoción
        if avg_intensity > 120 and contrast > 30:
            emotion = Emotion.HAPPY
            confidence = 0.7
        elif avg_intensity < 80:
            emotion = Emotion.SAD
            confidence = 0.6
        elif contrast > 50:
            emotion = Emotion.SURPRISED
            confidence = 0.65
        else:
            emotion = Emotion.NEUTRAL
            confidence = 0.5
        
        return {
            "emotion": emotion,
            "confidence": confidence,
            "features": {
                "avg_intensity": avg_intensity,
                "contrast": contrast,
                "symmetry": symmetry
            }
        }

class VocalEmotionAnalyzer:
    """Analizador de emociones vocales"""
    
    def __init__(self):
        self.sample_rate = 16000
        self.window_size = 1024
    
    async def analyze_emotion(self, audio_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """Analiza emoción en datos de audio"""
        try:
            # Análisis básico de características de audio
            features = self._extract_audio_features(audio_data)
            emotion_analysis = self._classify_vocal_emotion(features)
            
            return {
                "emotion": emotion_analysis["emotion"],
                "confidence": emotion_analysis["confidence"],
                "details": {
                    "features": features
                }
            }
            
        except Exception as e:
            logger.error(f"Error analizando emoción vocal: {e}")
            return None
    
    def _extract_audio_features(self, audio_data: np.ndarray) -> Dict[str, float]:
        """Extrae características del audio"""
        # Características básicas
        features = {}
        
        # Energía (RMS)
        features["energy"] = np.sqrt(np.mean(audio_data**2))
        
        # Pitch fundamental (estimación simple)
        features["pitch"] = self._estimate_pitch(audio_data)
        
        # Tasa de cruces por cero
        features["zero_crossing_rate"] = np.mean(np.abs(np.diff(np.sign(audio_data))))
        
        # Centroide espectral
        features["spectral_centroid"] = self._spectral_centroid(audio_data)
        
        return features
    
    def _estimate_pitch(self, audio_data: np.ndarray) -> float:
        """Estima pitch fundamental"""
        # Autocorrelación simple para estimar pitch
        correlation = np.correlate(audio_data, audio_data, mode='full')
        correlation = correlation[len(correlation)//2:]
        
        # Encontrar primer pico después del origen
        peaks = []
        for i in range(1, len(correlation)-1):
            if correlation[i] > correlation[i-1] and correlation[i] > correlation[i+1]:
                peaks.append((i, correlation[i]))
        
        if peaks:
            # Usar el pico más prominente
            best_peak = max(peaks, key=lambda x: x[1])
            pitch_period = best_peak[0]
            return self.sample_rate / pitch_period if pitch_period > 0 else 0
        
        return 0
    
    def _spectral_centroid(self, audio_data: np.ndarray) -> float:
        """Calcula centroide espectral"""
        # FFT
        fft = np.abs(np.fft.fft(audio_data))
        freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
        
        # Centroide espectral
        magnitude = fft[:len(fft)//2]
        freqs = freqs[:len(freqs)//2]
        
        if np.sum(magnitude) > 0:
            centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
            return centroid
        
        return 0
    
    def _classify_vocal_emotion(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Clasifica emoción basada en características vocales"""
        # Clasificación heurística simple
        energy = features.get("energy", 0)
        pitch = features.get("pitch", 0)
        zcr = features.get("zero_crossing_rate", 0)
        
        # Reglas heurísticas
        if energy > 0.1 and pitch > 200:
            emotion = Emotion.EXCITED
            confidence = 0.7
        elif energy < 0.05:
            emotion = Emotion.SAD
            confidence = 0.6
        elif pitch > 300:
            emotion = Emotion.SURPRISED
            confidence = 0.65
        elif zcr > 0.1:
            emotion = Emotion.ANGRY
            confidence = 0.6
        else:
            emotion = Emotion.NEUTRAL
            confidence = 0.5
        
        return {
            "emotion": emotion,
            "confidence": confidence
        }

# Instancia global
hamilton_emotion = EmotionRecognitionSystem()
