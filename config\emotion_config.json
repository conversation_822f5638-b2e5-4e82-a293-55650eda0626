{"facial_recognition": {"enabled": true, "confidence_threshold": 0.6, "update_interval_seconds": 2, "emotion_smoothing": true, "face_detection_model": "haarcascade"}, "vocal_recognition": {"enabled": true, "confidence_threshold": 0.5, "analysis_window_seconds": 3, "features": ["pitch", "energy", "tempo", "spectral"]}, "emotion_responses": {"happy": {"response_style": "enthusiastic", "voice_tone": "cheerful", "suggestions": ["Continue with positive activities"]}, "sad": {"response_style": "empathetic", "voice_tone": "gentle", "suggestions": ["Would you like to talk about it?", "Perhaps some music?"]}, "angry": {"response_style": "calm", "voice_tone": "soothing", "suggestions": ["Take a deep breath", "Would you like some relaxing music?"]}, "tired": {"response_style": "understanding", "voice_tone": "soft", "suggestions": ["Perhaps you should rest", "Would you like me to dim the lights?"]}, "stressed": {"response_style": "supportive", "voice_tone": "reassuring", "suggestions": ["Let's take a moment to relax", "Would you like a break reminder?"]}}, "adaptive_behavior": {"adjust_response_style": true, "adjust_voice_tone": true, "suggest_actions": true, "learn_patterns": true}}