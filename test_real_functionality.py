#!/usr/bin/env python3
"""
Hamilton AI Assistant - Test Real Functionality
Prueba REAL de funcionalidades implementadas
"""

import sys
import asyncio
import logging
import time
import numpy as np
from pathlib import Path

# Agregar directorio actual al path
sys.path.insert(0, str(Path(__file__).parent))

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_real_iot_functionality():
    """Prueba funcionalidad REAL de IoT"""
    print("\n🏠 PROBANDO FUNCIONALIDAD REAL DE IOT")
    print("-" * 50)
    
    try:
        from integrations.iot_controller import hamilton_iot
        
        # Descubrir dispositivos (reales o simulados)
        print("🔍 Descubriendo dispositivos...")
        devices = await hamilton_iot.discover_devices()
        print(f"✅ {len(devices)} dispositivos encontrados")
        
        for device in devices[:3]:  # Mostrar primeros 3
            print(f"  - {device.name} ({device.type}) - Estado: {device.status}")
        
        # Probar control de dispositivos
        if devices:
            device = devices[0]
            print(f"\n🎛️ Probando control de: {device.name}")
            
            # Encender dispositivo
            success = await hamilton_iot.control_device(device.id, "turn_on", {"brightness": 80})
            print(f"  Encender: {'✅ Éxito' if success else '❌ Error'}")
            
            time.sleep(1)
            
            # Cambiar brillo
            success = await hamilton_iot.control_device(device.id, "set_brightness", {"value": 50})
            print(f"  Cambiar brillo: {'✅ Éxito' if success else '❌ Error'}")
            
            time.sleep(1)
            
            # Apagar dispositivo
            success = await hamilton_iot.control_device(device.id, "turn_off", {})
            print(f"  Apagar: {'✅ Éxito' if success else '❌ Error'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba IoT: {e}")
        return False

async def test_real_automation_functionality():
    """Prueba funcionalidad REAL de automatización"""
    print("\n🏡 PROBANDO FUNCIONALIDAD REAL DE AUTOMATIZACIÓN")
    print("-" * 50)
    
    try:
        from integrations.home_automation import hamilton_automation
        
        # Probar comandos de voz inteligentes
        test_commands = [
            "modo noche",
            "enciende las luces de la sala",
            "sube la temperatura a 24 grados",
            "llegué a casa",
            "estado de la casa",
            "apaga todas las luces"
        ]
        
        for command in test_commands:
            print(f"\n🗣️ Comando: '{command}'")
            response = await hamilton_automation.process_voice_command(command)
            if response:
                print(f"  Respuesta: {response}")
            else:
                print("  ❌ Comando no reconocido")
        
        # Probar activación de escenas
        print(f"\n🎬 Probando activación de escenas...")
        scenes = ["modo_trabajo", "modo_noche", "llegada_casa"]
        
        for scene in scenes:
            success = await hamilton_automation.activate_scene(scene)
            print(f"  {scene}: {'✅ Activada' if success else '❌ Error'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de automatización: {e}")
        return False

async def test_real_multilingual_functionality():
    """Prueba funcionalidad REAL multiidioma"""
    print("\n🌍 PROBANDO FUNCIONALIDAD REAL MULTIIDIOMA")
    print("-" * 50)
    
    try:
        from integrations.multilingual_system import hamilton_multilingual, Language
        
        # Probar detección de idioma
        test_texts = [
            ("Hola Hamilton, ¿cómo estás?", "Español"),
            ("Hello Hamilton, how are you?", "Inglés"),
            ("Bonjour Hamilton, comment allez-vous?", "Francés"),
            ("Olá Hamilton, como está?", "Portugués")
        ]
        
        print("🔍 Probando detección de idioma:")
        for text, expected in test_texts:
            detected = hamilton_multilingual.detect_language(text)
            print(f"  '{text[:30]}...' -> {detected.value} (esperado: {expected})")
        
        # Probar traducción real
        print("\n🔄 Probando traducción:")
        translations = [
            ("Hola", Language.SPANISH, Language.ENGLISH),
            ("Good morning", Language.ENGLISH, Language.SPANISH),
            ("Gracias", Language.SPANISH, Language.FRENCH),
            ("Hamilton ayuda", Language.SPANISH, Language.ENGLISH)
        ]
        
        for text, source, target in translations:
            translated = hamilton_multilingual.translate_text(text, target, source)
            print(f"  {text} ({source.value}) -> {translated} ({target.value})")
        
        # Probar respuestas localizadas
        print("\n📝 Probando respuestas localizadas:")
        responses = ["hamilton_intro", "understood", "good_morning", "help"]
        
        for response_key in responses:
            spanish = hamilton_multilingual.get_localized_response(response_key, Language.SPANISH)
            english = hamilton_multilingual.get_localized_response(response_key, Language.ENGLISH)
            print(f"  {response_key}: ES='{spanish}' | EN='{english}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba multiidioma: {e}")
        return False

async def test_real_emotion_functionality():
    """Prueba funcionalidad REAL de reconocimiento de emociones"""
    print("\n😊 PROBANDO FUNCIONALIDAD REAL DE EMOCIONES")
    print("-" * 50)
    
    try:
        from integrations.emotion_recognition import hamilton_emotion
        import cv2
        
        # Crear imagen de prueba simulada
        print("📸 Creando imagen de prueba para análisis facial...")
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Simular rostro en el centro
        center_y, center_x = 240, 320
        face_size = 100
        
        # Crear un "rostro" básico (rectángulo con características)
        cv2.rectangle(test_image, 
                     (center_x - face_size, center_y - face_size),
                     (center_x + face_size, center_y + face_size),
                     (200, 180, 160), -1)  # Color piel
        
        # Ojos
        cv2.circle(test_image, (center_x - 30, center_y - 20), 10, (0, 0, 0), -1)
        cv2.circle(test_image, (center_x + 30, center_y - 20), 10, (0, 0, 0), -1)
        
        # Boca (sonrisa)
        cv2.ellipse(test_image, (center_x, center_y + 30), (40, 20), 0, 0, 180, (0, 0, 0), 2)
        
        # Analizar emoción
        emotion_reading = await hamilton_emotion.analyze_current_emotion(test_image)
        
        if emotion_reading:
            print(f"✅ Emoción detectada: {emotion_reading.emotion.value}")
            print(f"  Confianza: {emotion_reading.confidence:.2f}")
            print(f"  Fuente: {emotion_reading.source.value}")
            
            # Actualizar estado emocional
            hamilton_emotion.update_emotional_state(emotion_reading)
            
            # Obtener estilo de respuesta adaptativo
            adaptive_style = hamilton_emotion.get_adaptive_response_style()
            print(f"  Estilo adaptativo: {adaptive_style}")
        else:
            print("❌ No se pudo analizar la emoción")
        
        # Probar análisis de audio simulado
        print("\n🎤 Probando análisis de audio simulado...")
        # Crear audio de prueba (señal sinusoidal)
        sample_rate = 16000
        duration = 2  # segundos
        frequency = 440  # Hz (La musical)
        
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.5
        
        # Agregar algo de ruido para simular voz
        noise = np.random.normal(0, 0.1, audio_data.shape)
        audio_data += noise
        
        vocal_emotion = await hamilton_emotion.analyze_current_emotion(audio_data=audio_data)
        
        if vocal_emotion:
            print(f"✅ Emoción vocal detectada: {vocal_emotion.emotion.value}")
            print(f"  Confianza: {vocal_emotion.confidence:.2f}")
        
        # Obtener resumen emocional
        summary = hamilton_emotion.get_emotion_summary()
        print(f"\n📊 Resumen emocional: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de emociones: {e}")
        return False

async def test_real_calendar_functionality():
    """Prueba funcionalidad REAL de calendario"""
    print("\n📅 PROBANDO FUNCIONALIDAD REAL DE CALENDARIO")
    print("-" * 50)
    
    try:
        from integrations.calendar_manager import hamilton_calendar
        from datetime import datetime, timedelta
        
        # Crear eventos de prueba
        print("📝 Creando eventos de prueba...")
        
        tomorrow = datetime.now() + timedelta(days=1)
        
        events_to_create = [
            ("Reunión con equipo", tomorrow.replace(hour=10, minute=0), tomorrow.replace(hour=11, minute=0)),
            ("Almuerzo", tomorrow.replace(hour=13, minute=0), tomorrow.replace(hour=14, minute=0)),
            ("Revisión de proyecto", tomorrow.replace(hour=16, minute=0), tomorrow.replace(hour=17, minute=0))
        ]
        
        created_events = []
        for title, start, end in events_to_create:
            event_id = await hamilton_calendar.create_event(title, start, end, f"Evento de prueba: {title}")
            if event_id:
                created_events.append(event_id)
                print(f"  ✅ {title} creado")
            else:
                print(f"  ❌ Error creando {title}")
        
        # Crear recordatorios
        print("\n⏰ Creando recordatorios...")
        reminder_time = datetime.now() + timedelta(hours=2)
        reminder_id = await hamilton_calendar.create_reminder(
            "Revisar correos", 
            reminder_time, 
            "Recordatorio de prueba"
        )
        
        if reminder_id:
            print(f"  ✅ Recordatorio creado para {reminder_time.strftime('%H:%M')}")
        
        # Probar comandos de voz
        print("\n🗣️ Probando comandos de voz:")
        voice_commands = [
            "agenda de hoy",
            "eventos de mañana", 
            "próximos recordatorios",
            "qué tengo programado"
        ]
        
        for command in voice_commands:
            response = await hamilton_calendar.process_voice_command(command)
            if response:
                print(f"  '{command}' -> {response}")
            else:
                print(f"  '{command}' -> Sin respuesta")
        
        # Obtener agenda de hoy
        today_schedule = hamilton_calendar.get_today_schedule()
        print(f"\n📋 Agenda de hoy: {today_schedule['total_events']} eventos, {today_schedule['total_reminders']} recordatorios")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de calendario: {e}")
        return False

async def test_real_core_functionality():
    """Prueba funcionalidad REAL del núcleo"""
    print("\n🧠 PROBANDO FUNCIONALIDAD REAL DEL NÚCLEO")
    print("-" * 50)
    
    try:
        from core.hamilton_core import hamilton
        
        # Probar procesamiento de texto
        print("💬 Probando procesamiento de texto:")
        test_inputs = [
            "Hola Hamilton",
            "¿Qué hora es?",
            "Enciende las luces",
            "Modo noche",
            "¿Cómo está el clima?",
            "Hamilton información del sistema"
        ]
        
        for text_input in test_inputs:
            print(f"\n  Entrada: '{text_input}'")
            response = hamilton.process_text_input(text_input)
            print(f"  Respuesta: {response}")
        
        # Obtener estado del sistema
        print(f"\n📊 Estado del sistema:")
        status = hamilton.get_status()
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba del núcleo: {e}")
        return False

async def main():
    """Función principal de pruebas reales"""
    print("🔬 HAMILTON AI ASSISTANT - PRUEBAS DE FUNCIONALIDAD REAL")
    print("=" * 70)
    
    test_results = {}
    
    # Ejecutar todas las pruebas
    tests = [
        ("IoT Real", test_real_iot_functionality),
        ("Automatización Real", test_real_automation_functionality),
        ("Multiidioma Real", test_real_multilingual_functionality),
        ("Emociones Real", test_real_emotion_functionality),
        ("Calendario Real", test_real_calendar_functionality),
        ("Núcleo Real", test_real_core_functionality)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            test_results[test_name] = result
        except Exception as e:
            print(f"❌ Error inesperado en {test_name}: {e}")
            test_results[test_name] = False
    
    # Resumen final
    print("\n" + "=" * 70)
    print("📊 RESUMEN DE PRUEBAS REALES")
    print("=" * 70)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ FUNCIONAL" if result else "❌ CON ERRORES"
        print(f"{test_name:.<40} {status}")
    
    percentage = (passed / total) * 100 if total > 0 else 0
    print(f"\nTotal: {passed}/{total} pruebas exitosas ({percentage:.1f}%)")
    
    if percentage >= 80:
        print("\n🎉 ¡HAMILTON ESTÁ MAYORMENTE FUNCIONAL!")
        print("Las funcionalidades principales están operativas.")
    elif percentage >= 60:
        print("\n✅ HAMILTON ESTÁ PARCIALMENTE FUNCIONAL")
        print("Algunas funcionalidades necesitan ajustes.")
    else:
        print("\n⚠️ HAMILTON NECESITA MÁS TRABAJO")
        print("Varias funcionalidades requieren implementación adicional.")
    
    return test_results

if __name__ == "__main__":
    try:
        results = asyncio.run(main())
        
        print("\n🔧 RECOMENDACIONES:")
        print("1. Las funcionalidades marcadas como 'FUNCIONAL' están listas para uso")
        print("2. Las marcadas como 'CON ERRORES' necesitan configuración adicional")
        print("3. Ejecute setup_iot_devices.py para configurar dispositivos reales")
        print("4. Configure APIs externas en config/api_keys.json para funcionalidad completa")
        
    except KeyboardInterrupt:
        print("\n\n👋 Pruebas interrumpidas por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
