# Hamilton - Personal AI Assistant - Complete Dependencies
# Dependencias completas para todas las funcionalidades

# Core Framework
fastapi>=0.100.0
uvicorn>=0.23.0
python-multipart>=0.0.6
python-jose>=3.3.0
passlib>=1.7.4
bcrypt>=4.0.1
python-dotenv>=1.0.0
pydantic>=2.0.0

# Computer Vision and Face Recognition
opencv-python>=4.8.0
face-recognition>=1.3.0
numpy>=1.24.0

# Speech Recognition and TTS
SpeechRecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.13

# Security and Encryption
cryptography>=41.0.0
pyotp>=2.8.0
qrcode>=7.4.0
redis>=4.6.0
ratelimit>=2.2.1

# Database
sqlalchemy>=2.0.0
aiosqlite>=0.19.0

# Settings Management
pydantic-settings>=2.0.0

# Machine Learning
scikit-learn>=1.3.0
torch>=2.0.0
pandas>=2.0.0
matplotlib>=3.7.0
transformers>=4.30.0
datasets>=2.12.0
accelerate>=0.20.0
anthropic>=0.5.0

# Web and UI
jinja2>=3.1.0
aiofiles>=23.0.0
websockets>=11.0.0
requests>=2.31.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# Utilities
python-dateutil>=2.8.2
tqdm>=4.65.0
colorama>=0.4.6
pillow>=10.0.0
seaborn>=0.12.0

# Development and Testing
pytest==6.2.5
pytest-asyncio==0.15.1
pytest-cov==2.12.1
pytest-mock==3.6.1

# Optional AI/ML Dependencies (comentadas por defecto)
openai>=1.0.0
python-dotenv>=0.19.0
# transformers>=4.35.0
# deepface>=0.0.79

# Dependencias de monitoreo
psutil>=5.9.0

# Dependencias de desarrollo
black>=23.7.0
flake8>=6.1.0
mypy>=1.5.0
isort>=5.12.0

# Dependencias adicionales
pyjwt>=2.8.0
alembic>=1.12.0
pymongo>=4.5.0

# Dependencias web
dash>=2.14.0
plotly>=5.18.0
dash-bootstrap-components>=1.5.0

# Dependencias de análisis
statsmodels>=0.14.0
prophet>=1.1.4
tensorflow>=2.15.0
keras>=2.15.0

# Dependencias de monitoreo
prometheus-client>=0.19.0
