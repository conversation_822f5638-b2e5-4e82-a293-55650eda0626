# Hamilton - Personal AI Assistant - Core Dependencies
# Dependencias básicas para funcionalidad principal

# Core Framework
fastapi>=0.100.0
uvicorn>=0.20.0
python-multipart>=0.0.6
websockets>=12.0

# Computer Vision and Face Recognition
opencv-python>=4.8.0
face-recognition>=1.3.0
numpy>=1.24.0

# Speech Recognition and TTS
speechrecognition>=3.10.0
pyttsx3>=2.90

# Security and Encryption
cryptography>=41.0.0

# Database
# sqlite3 is built-in with Python

# Settings Management
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Prolog Integration (opcional)
pyswip>=0.2.10

# Utilities
python-dotenv>=1.0.0
requests>=2.31.0
python-dateutil>=2.8.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Optional AI/ML Dependencies (comentadas por defecto)
# openai>=1.3.0
# transformers>=4.35.0
# torch>=2.0.0
# deepface>=0.0.79
# scikit-learn>=1.3.0
# pandas>=2.0.0

# Dependencias principales
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
torch>=2.0.0
transformers>=4.30.0
tensorflow>=2.13.0

# Procesamiento de lenguaje natural
spacy>=3.6.0
nltk>=3.8.1
googletrans>=4.0.0

# Reconocimiento de voz y audio
SpeechRecognition>=3.10.0
pyaudio>=0.2.13
librosa>=0.10.0

# Reconocimiento facial y emociones
face-recognition>=1.3.0
opencv-python>=4.8.0
dlib>=19.24.0

# IoT y automatización
paho-mqtt>=1.6.1
python-dotenv>=1.0.0
requests>=2.31.0

# Calendario y recordatorios
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
google-api-python-client>=2.95.0

# Aprendizaje por refuerzo
gym>=0.26.0
stable-baselines3>=2.1.0

# Web y API
fastapi>=0.100.0
uvicorn>=0.23.0
websockets>=11.0.3

# Base de datos
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.6
redis>=4.6.0

# Utilidades
python-dateutil>=2.8.2
pytz>=2023.3
tqdm>=4.65.0
pillow>=10.0.0
qrcode>=7.4.2

# Testing y desarrollo
pytest>=7.4.0
black>=23.7.0
flake8>=6.1.0
mypy>=1.5.0

# Monitoreo y logging
prometheus-client>=0.17.0
python-json-logger>=2.0.7
