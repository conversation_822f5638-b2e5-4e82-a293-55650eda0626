# Hamilton - Personal AI Assistant - Core Dependencies
# Dependencias básicas para funcionalidad principal

# Core Framework
fastapi>=0.100.0
uvicorn>=0.20.0
python-multipart>=0.0.6
websockets>=12.0

# Computer Vision and Face Recognition
opencv-python>=4.8.0
face-recognition>=1.3.0
numpy>=1.24.0

# Speech Recognition and TTS
speechrecognition>=3.10.0
pyttsx3>=2.90

# Security and Encryption
cryptography>=41.0.0

# Database
# sqlite3 is built-in with Python

# Settings Management
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Prolog Integration (opcional)
pyswip>=0.2.10

# Utilities
python-dotenv>=1.0.0
requests>=2.31.0
python-dateutil>=2.8.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Optional AI/ML Dependencies (comentadas por defecto)
# openai>=1.3.0
# transformers>=4.35.0
# torch>=2.0.0
# deepface>=0.0.79
# scikit-learn>=1.3.0
# pandas>=2.0.0
