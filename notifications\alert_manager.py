import os
import smtplib
import logging
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import json

class AlertManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()
        self.alert_history = []
        self.alert_thresholds = {
            'critical': 3,  # Número de alertas críticas antes de notificar
            'warning': 5,   # Número de alertas de advertencia antes de notificar
            'cooldown': 300  # Tiempo de espera entre notificaciones (segundos)
        }
        self.last_notification = {}
        
    def _load_config(self) -> Dict:
        """Carga la configuración de notificaciones"""
        config_path = Path('config/notifications.json')
        if not config_path.exists():
            return {
                'email': {
                    'enabled': False,
                    'smtp_server': '',
                    'smtp_port': 587,
                    'username': '',
                    'password': '',
                    'from_address': '',
                    'to_addresses': []
                },
                'webhooks': {
                    'enabled': False,
                    'endpoints': []
                },
                'prometheus': {
                    'enabled': False,
                    'endpoint': '',
                    'job_name': 'hamilton'
                }
            }
            
        with open(config_path) as f:
            return json.load(f)
            
    def _format_alert_message(self, alert: Dict) -> str:
        """Formatea el mensaje de alerta"""
        return f"""
        Alerta del Sistema Hamilton
        -------------------------
        Tipo: {alert['type']}
        Severidad: {alert['severity']}
        Mensaje: {alert['message']}
        Timestamp: {alert['timestamp']}
        Métricas:
        {json.dumps(alert['metrics'], indent=2)}
        """
        
    def _send_email(self, subject: str, message: str) -> bool:
        """Envía una notificación por correo electrónico"""
        if not self.config['email']['enabled']:
            return False
            
        try:
            msg = MIMEMultipart()
            msg['From'] = self.config['email']['from_address']
            msg['To'] = ', '.join(self.config['email']['to_addresses'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(message, 'plain'))
            
            with smtplib.SMTP(
                self.config['email']['smtp_server'],
                self.config['email']['smtp_port']
            ) as server:
                server.starttls()
                server.login(
                    self.config['email']['username'],
                    self.config['email']['password']
                )
                server.send_message(msg)
                
            return True
        except Exception as e:
            self.logger.error(f"Error enviando email: {e}")
            return False
            
    def _send_webhook(self, alert: Dict) -> bool:
        """Envía la alerta a los webhooks configurados"""
        if not self.config['webhooks']['enabled']:
            return False
            
        success = True
        for endpoint in self.config['webhooks']['endpoints']:
            try:
                response = requests.post(
                    endpoint,
                    json=alert,
                    timeout=5
                )
                if response.status_code != 200:
                    self.logger.warning(
                        f"Webhook {endpoint} respondió con código {response.status_code}"
                    )
                    success = False
            except Exception as e:
                self.logger.error(f"Error enviando webhook a {endpoint}: {e}")
                success = False
                
        return success
        
    def _send_prometheus_alert(self, alert: Dict) -> bool:
        """Envía la alerta a Prometheus"""
        if not self.config['prometheus']['enabled']:
            return False
            
        try:
            # Crear métricas en formato Prometheus
            metrics = []
            for key, value in alert['metrics'].items():
                if isinstance(value, (int, float)):
                    metrics.append(
                        f'hamilton_alert_{key}{{severity="{alert["severity"]}"}} {value}'
                    )
                    
            # Enviar a Pushgateway
            response = requests.post(
                f"{self.config['prometheus']['endpoint']}/metrics/job/{self.config['prometheus']['job_name']}",
                data='\n'.join(metrics),
                timeout=5
            )
            
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Error enviando alerta a Prometheus: {e}")
            return False
            
    def process_alert(self, alert: Dict) -> bool:
        """Procesa una nueva alerta"""
        # Añadir timestamp si no existe
        if 'timestamp' not in alert:
            alert['timestamp'] = datetime.now().isoformat()
            
        # Añadir a historial
        self.alert_history.append(alert)
        
        # Verificar si debemos notificar
        if self._should_notify(alert):
            return self._send_notifications(alert)
            
        return True
        
    def _should_notify(self, alert: Dict) -> bool:
        """Determina si debemos enviar una notificación"""
        severity = alert['severity']
        current_time = datetime.now()
        
        # Verificar tiempo de espera
        if severity in self.last_notification:
            last_time = datetime.fromisoformat(self.last_notification[severity])
            if (current_time - last_time).total_seconds() < self.alert_thresholds['cooldown']:
                return False
                
        # Contar alertas recientes del mismo tipo
        recent_alerts = [
            a for a in self.alert_history[-self.alert_thresholds[severity]:]
            if a['severity'] == severity
        ]
        
        return len(recent_alerts) >= self.alert_thresholds[severity]
        
    def _send_notifications(self, alert: Dict) -> bool:
        """Envía todas las notificaciones configuradas"""
        message = self._format_alert_message(alert)
        subject = f"[Hamilton] Alerta {alert['severity'].upper()}: {alert['type']}"
        
        # Enviar notificaciones
        email_sent = self._send_email(subject, message)
        webhook_sent = self._send_webhook(alert)
        prometheus_sent = self._send_prometheus_alert(alert)
        
        # Actualizar último envío
        self.last_notification[alert['severity']] = datetime.now().isoformat()
        
        return any([email_sent, webhook_sent, prometheus_sent])
        
    def get_alert_history(self, 
                         severity: Optional[str] = None,
                         alert_type: Optional[str] = None,
                         limit: int = 100) -> List[Dict]:
        """Obtiene el historial de alertas filtrado"""
        alerts = self.alert_history
        
        if severity:
            alerts = [a for a in alerts if a['severity'] == severity]
            
        if alert_type:
            alerts = [a for a in alerts if a['type'] == alert_type]
            
        return alerts[-limit:]
        
    def clear_history(self):
        """Limpia el historial de alertas"""
        self.alert_history = []
        self.last_notification = {} 