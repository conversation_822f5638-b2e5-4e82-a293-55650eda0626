import os
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from typing import Dict, List, Tuple, Optional

class PredictiveAnalyzer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics_file = 'logs/metrics.json'
        self.models = {}
        self.scalers = {}
        self.anomaly_detector = IsolationForest(contamination=0.1)
        self.prediction_window = 24  # horas
        
    def load_metrics(self, hours: int = 168) -> pd.DataFrame:
        """Carga las métricas históricas"""
        try:
            metrics = []
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            with open(self.metrics_file, 'r') as f:
                for line in f:
                    data = json.loads(line)
                    timestamp = datetime.fromisoformat(data['timestamp'])
                    
                    if timestamp >= cutoff_time:
                        metrics.append(data)
                        
            return pd.DataFrame(metrics)
        except Exception as e:
            self.logger.error(f"Error cargando métricas: {e}")
            return pd.DataFrame()
            
    def prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, List[str]]:
        """Prepara las características para el análisis"""
        features = []
        feature_names = []
        
        # Métricas del sistema
        if 'system' in df.columns:
            system_metrics = pd.json_normalize(df['system'])
            features.extend([
                system_metrics['cpu_percent'].values,
                system_metrics['memory_percent'].values,
                system_metrics['disk_percent'].values
            ])
            feature_names.extend(['cpu', 'memory', 'disk'])
            
        # Métricas de IA
        if 'ai' in df.columns:
            for engine in df['ai'].iloc[0].keys():
                engine_metrics = pd.json_normalize(
                    df['ai'].apply(lambda x: x.get(engine, {}))
                )
                if 'quality_metrics' in engine_metrics.columns:
                    quality = pd.json_normalize(engine_metrics['quality_metrics'])
                    features.extend([
                        quality['relevance'].values,
                        quality['coherence'].values,
                        quality['completeness'].values,
                        quality['clarity'].values,
                        quality['context_usage'].values
                    ])
                    feature_names.extend([
                        f'{engine}_relevance',
                        f'{engine}_coherence',
                        f'{engine}_completeness',
                        f'{engine}_clarity',
                        f'{engine}_context_usage'
                    ])
                    
        return np.column_stack(features), feature_names
        
    def train_models(self):
        """Entrena los modelos predictivos"""
        try:
            # Cargar datos
            df = self.load_metrics()
            if df.empty:
                return
                
            # Preparar características
            X, feature_names = self.prepare_features(df)
            
            # Normalizar datos
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            self.scalers['main'] = scaler
            
            # Entrenar detector de anomalías
            self.anomaly_detector.fit(X_scaled)
            
            # Entrenar modelos de predicción para cada característica
            for i, feature in enumerate(feature_names):
                model = LinearRegression()
                model.fit(X_scaled[:-self.prediction_window], X_scaled[self.prediction_window:, i])
                self.models[feature] = model
                
            self.logger.info("Modelos entrenados exitosamente")
            
        except Exception as e:
            self.logger.error(f"Error entrenando modelos: {e}")
            
    def detect_anomalies(self, metrics: Dict) -> List[Dict]:
        """Detecta anomalías en las métricas actuales"""
        try:
            # Preparar características
            df = pd.DataFrame([metrics])
            X, _ = self.prepare_features(df)
            
            # Normalizar
            X_scaled = self.scalers['main'].transform(X)
            
            # Detectar anomalías
            predictions = self.anomaly_detector.predict(X_scaled)
            scores = self.anomaly_detector.score_samples(X_scaled)
            
            anomalies = []
            if predictions[0] == -1:  # Anomalía detectada
                anomalies.append({
                    'type': 'anomaly',
                    'severity': 'warning',
                    'message': f"Se detectó un comportamiento anómalo (score: {scores[0]:.2f})",
                    'metrics': metrics
                })
                
            return anomalies
            
        except Exception as e:
            self.logger.error(f"Error detectando anomalías: {e}")
            return []
            
    def predict_metrics(self, current_metrics: Dict) -> Dict:
        """Predice métricas futuras"""
        try:
            # Preparar características
            df = pd.DataFrame([current_metrics])
            X, feature_names = self.prepare_features(df)
            
            # Normalizar
            X_scaled = self.scalers['main'].transform(X)
            
            # Realizar predicciones
            predictions = {}
            for feature, model in self.models.items():
                pred = model.predict(X_scaled)
                predictions[feature] = float(pred[0])
                
            return predictions
            
        except Exception as e:
            self.logger.error(f"Error realizando predicciones: {e}")
            return {}
            
    def generate_recommendations(self, 
                               current_metrics: Dict,
                               predictions: Dict) -> List[Dict]:
        """Genera recomendaciones basadas en las predicciones"""
        recommendations = []
        
        # Verificar uso de CPU
        if predictions.get('cpu', 0) > 80:
            recommendations.append({
                'type': 'optimization',
                'severity': 'warning',
                'message': "Se predice un alto uso de CPU. Considere escalar recursos.",
                'action': "Aumentar recursos de CPU o optimizar procesos"
            })
            
        # Verificar uso de memoria
        if predictions.get('memory', 0) > 80:
            recommendations.append({
                'type': 'optimization',
                'severity': 'warning',
                'message': "Se predice un alto uso de memoria. Considere liberar memoria.",
                'action': "Limpiar caché o aumentar memoria disponible"
            })
            
        # Verificar calidad de IA
        for engine in current_metrics.get('ai', {}).keys():
            for metric in ['relevance', 'coherence', 'completeness', 'clarity']:
                key = f'{engine}_{metric}'
                if predictions.get(key, 1) < 0.7:
                    recommendations.append({
                        'type': 'quality',
                        'severity': 'info',
                        'message': f"Se predice una baja {metric} para {engine}",
                        'action': f"Revisar y ajustar parámetros de {engine}"
                    })
                    
        return recommendations
        
    def analyze(self, current_metrics: Dict) -> Dict:
        """Realiza un análisis completo"""
        try:
            # Detectar anomalías
            anomalies = self.detect_anomalies(current_metrics)
            
            # Realizar predicciones
            predictions = self.predict_metrics(current_metrics)
            
            # Generar recomendaciones
            recommendations = self.generate_recommendations(
                current_metrics,
                predictions
            )
            
            return {
                'timestamp': datetime.now().isoformat(),
                'anomalies': anomalies,
                'predictions': predictions,
                'recommendations': recommendations
            }
            
        except Exception as e:
            self.logger.error(f"Error en análisis predictivo: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
            
    def update_models(self):
        """Actualiza los modelos con nuevos datos"""
        self.train_models() 