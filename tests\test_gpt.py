"""
Hamilton AI Assistant - GPT Integration Tests
Pruebas para la integración con OpenAI GPT
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from ai.gpt_engine import Hamilton<PERSON>TEngine, hamilton_gpt

class TestGPTIntegration:
    """Pruebas de integración GPT"""
    
    def setup_method(self):
        """Configuración antes de cada prueba"""
        self.gpt = HamiltonGPTEngine()
    
    def test_initialization_without_key(self):
        """Prueba inicialización sin API key"""
        assert not self.gpt.api_key
        assert self.gpt.model == "gpt-4"
    
    @patch('openai.models.list')
    def test_initialization_with_key(self, mock_list):
        """Prueba inicialización con API key válida"""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            gpt = HamiltonGPTEngine()
            assert gpt.api_key == 'test-key'
            mock_list.assert_called_once()
    
    @patch('openai.ChatCompletion.create')
    def test_process_query_success(self, mock_create):
        """Prueba procesamiento exitoso de consulta"""
        # Configurar mock
        mock_response = MagicMock()
        mock_response.choices = [MagicMock(message=MagicMock(content="Test response"))]
        mock_create.return_value = mock_response
        
        # Procesar consulta
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            response = self.gpt.process_query("Test query")
            assert response == "Test response"
            mock_create.assert_called_once()
    
    def test_process_query_no_key(self):
        """Prueba procesamiento sin API key"""
        response = self.gpt.process_query("Test query")
        assert "no está configurada" in response
    
    def test_conversation_history(self):
        """Prueba manejo del historial de conversación"""
        # Agregar mensajes
        self.gpt._update_conversation_history("Test query", "Test response")
        assert len(self.gpt.conversation_history) == 2
        
        # Limpiar historial
        self.gpt.clear_history()
        assert len(self.gpt.conversation_history) == 0
    
    def test_prepare_messages(self):
        """Prueba preparación de mensajes"""
        # Preparar mensajes sin contexto
        messages = self.gpt._prepare_messages("Test query")
        assert len(messages) == 2
        assert messages[0]["role"] == "system"
        assert messages[1]["role"] == "user"
        
        # Preparar mensajes con contexto
        context = {"test": "context"}
        messages = self.gpt._prepare_messages("Test query", context)
        assert len(messages) == 3
        assert "context" in messages[1]["content"] 