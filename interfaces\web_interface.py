"""
Hamilton AI Assistant - Web Interface
Interfaz web para monitoreo y control de Hamilton
"""

from fastapi import FastAP<PERSON>, WebSocket, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import asyncio
from datetime import datetime
import logging
from typing import Dict, List, Any
import os
from pathlib import Path

from config.settings import settings
from security.encryption import security_log
from ai.learning_engine_simple import hamilton_learning

# Configurar logging
logger = logging.getLogger(__name__)

# Crear aplicación FastAPI
app = FastAPI(title="Hamilton AI Dashboard")

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Montar archivos estáticos
app.mount("/static", StaticFiles(directory="interfaces/static"), name="static")

# Configurar templates
templates = Jinja2Templates(directory="interfaces/templates")

# Almacenar conexiones WebSocket activas
active_connections: List[WebSocket] = []

# Almacenar notificaciones pendientes
pending_notifications: List[Dict[str, Any]] = []

class NotificationManager:
    """Maneja notificaciones en tiempo real"""
    
    def __init__(self):
        self.notifications = []
        self.max_notifications = 100
    
    def add_notification(self, title: str, message: str, level: str = "info"):
        """Añade una nueva notificación"""
        notification = {
            "id": len(self.notifications),
            "title": title,
            "message": message,
            "level": level,
            "timestamp": datetime.now().isoformat()
        }
        
        self.notifications.append(notification)
        
        # Mantener límite de notificaciones
        if len(self.notifications) > self.max_notifications:
            self.notifications.pop(0)
        
        # Notificar a todos los clientes conectados
        asyncio.create_task(self.broadcast_notification(notification))
    
    async def broadcast_notification(self, notification: Dict[str, Any]):
        """Envía notificación a todos los clientes conectados"""
        for connection in active_connections:
            try:
                await connection.send_json({
                    "type": "notification",
                    "data": notification
                })
            except:
                continue
    
    def get_notifications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Obtiene notificaciones recientes"""
        return self.notifications[-limit:]

# Instancia global del manejador de notificaciones
notification_manager = NotificationManager()

@app.get("/", response_class=HTMLResponse)
async def get_dashboard(request):
    """Renderiza el dashboard principal"""
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request}
    )

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Maneja conexiones WebSocket"""
    await websocket.accept()
    active_connections.append(websocket)
    
    try:
        while True:
            data = await websocket.receive_text()
            # Procesar mensajes del cliente si es necesario
    except:
        active_connections.remove(websocket)

@app.get("/api/logs")
async def get_logs(limit: int = 100, level: str = None):
    """Obtiene logs del sistema"""
    try:
        log_file = Path("./logs/security.log")
        if not log_file.exists():
            return []
        
        logs = []
        with open(log_file, "r") as f:
            for line in f.readlines()[-limit:]:
                try:
                    # Parsear línea de log
                    parts = line.split(" - ")
                    if len(parts) >= 4:
                        timestamp = parts[0]
                        logger_name = parts[1]
                        log_level = parts[2]
                        message = " - ".join(parts[3:]).strip()
                        
                        if level and log_level.lower() != level.lower():
                            continue
                        
                        logs.append({
                            "timestamp": timestamp,
                            "level": log_level,
                            "message": message
                        })
                except:
                    continue
        
        return logs
    except Exception as e:
        logger.error(f"Error obteniendo logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/metrics")
async def get_metrics():
    """Obtiene métricas del sistema"""
    try:
        return {
            "learning": hamilton_learning.get_personalized_response_style("senor_ibero"),
            "security": {
                "auth_attempts": len(security_log.get_recent_auth_attempts()),
                "failed_attempts": len(security_log.get_failed_auth_attempts())
            }
        }
    except Exception as e:
        logger.error(f"Error obteniendo métricas: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/notifications")
async def get_notifications(limit: int = 10):
    """Obtiene notificaciones recientes"""
    return notification_manager.get_notifications(limit)

@app.post("/api/notifications")
async def create_notification(title: str, message: str, level: str = "info"):
    """Crea una nueva notificación"""
    notification_manager.add_notification(title, message, level)
    return {"status": "success"}

@app.get("/api/ai-status")
async def get_ai_status():
    """Obtiene el estado de los motores de IA"""
    try:
        from main import hamilton
        return hamilton.get_ai_status()
    except Exception as e:
        logger.error(f"Error obteniendo estado de IA: {e}")
        return {
            "error": "Error obteniendo estado de IA",
            "details": str(e)
        }

def start_dashboard():
    """Inicia el servidor del dashboard"""
    uvicorn.run(
        "interfaces.web_interface:app",
        host=settings.DASHBOARD_HOST,
        port=settings.DASHBOARD_PORT,
        reload=True
    )

if __name__ == "__main__":
    start_dashboard()
