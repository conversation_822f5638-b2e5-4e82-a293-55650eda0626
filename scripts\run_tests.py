#!/usr/bin/env python3
"""
Script para ejecutar las pruebas del sistema.
"""

import os
import sys
import json
import logging
import subprocess
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/tests.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'tests']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivos necesarios
        required_files = [
            'requirements.txt',
            'config/settings.py'
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                logger.error(f"Archivo no encontrado: {file_path}")
                return False

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def run_unit_tests():
    """Ejecuta las pruebas unitarias."""
    try:
        logger.info("Ejecutando pruebas unitarias...")
        result = subprocess.run(
            ['pytest', 'tests/', '-v', '--cov=ai'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Pruebas unitarias completadas exitosamente")
            return True
        else:
            logger.error("Error en las pruebas unitarias")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Error al ejecutar las pruebas unitarias: {str(e)}")
        return False

def run_integration_tests():
    """Ejecuta las pruebas de integración."""
    try:
        logger.info("Ejecutando pruebas de integración...")
        result = subprocess.run(
            ['pytest', 'tests/integration/', '-v'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Pruebas de integración completadas exitosamente")
            return True
        else:
            logger.error("Error en las pruebas de integración")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Error al ejecutar las pruebas de integración: {str(e)}")
        return False

def run_performance_tests():
    """Ejecuta las pruebas de rendimiento."""
    try:
        logger.info("Ejecutando pruebas de rendimiento...")
        result = subprocess.run(
            ['pytest', 'tests/performance/', '-v'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Pruebas de rendimiento completadas exitosamente")
            return True
        else:
            logger.error("Error en las pruebas de rendimiento")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Error al ejecutar las pruebas de rendimiento: {str(e)}")
        return False

def save_test_results(results):
    """Guarda los resultados de las pruebas."""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = Path(f'logs/test_results_{timestamp}.json')
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Resultados guardados en {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error al guardar resultados: {str(e)}")
        return False

def run_tests():
    """Ejecuta todas las pruebas."""
    try:
        results = {
            'timestamp': datetime.now().isoformat(),
            'unit_tests': run_unit_tests(),
            'integration_tests': run_integration_tests(),
            'performance_tests': run_performance_tests()
        }
        
        # Guardar resultados
        if save_test_results(results):
            logger.info("Pruebas completadas")
            
            # Mostrar resumen
            print("\nResumen de Pruebas:")
            print("-" * 50)
            for test_type, status in results.items():
                if test_type != 'timestamp':
                    print(f"- {test_type}: {'✅' if status else '❌'}")
                    
        else:
            logger.error("Error al guardar los resultados")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error al ejecutar las pruebas: {str(e)}")
        sys.exit(1)

def main():
    """Función principal."""
    try:
        logger.info("Iniciando pruebas del sistema...")
        
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            sys.exit(1)
            
        run_tests()
        
    except KeyboardInterrupt:
        logger.info("Pruebas detenidas por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 