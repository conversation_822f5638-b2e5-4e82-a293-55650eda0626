"""
Configuración de puertos y servicios del sistema.
Este archivo define los puertos permitidos y sus descripciones para el análisis de seguridad.
"""

# Puertos del sistema
SYSTEM_PORTS = {
    # Puertos de la base de datos
    5432: {
        'service': 'PostgreSQL',
        'description': 'Base de datos principal del sistema',
        'required': True,
        'protocol': 'TCP',
        'restricted': True  # Solo permitir conexiones locales
    },
    
    # Puertos de servicios Windows
    445: {
        'service': 'SMB',
        'description': 'Compartir archivos y recursos de red',
        'required': False,  # Solo necesario si se requiere compartir archivos
        'protocol': 'TCP',
        'restricted': True
    },
    135: {
        'service': 'RPC',
        'description': 'Llamadas a procedimientos remotos de Windows',
        'required': False,  # Solo necesario para ciertos servicios Windows
        'protocol': 'TCP',
        'restricted': True
    },
    
    # Puertos de la aplicación
    7070: {
        'service': 'Hamilton Web',
        'description': 'Interfaz web principal de Hamilton',
        'required': True,
        'protocol': 'TCP',
        'restricted': False
    },
    8090: {
        'service': 'Hamilton API',
        'description': 'API REST de <PERSON>',
        'required': True,
        'protocol': 'TCP',
        'restricted': False
    }
}

# Puertos que deben estar cerrados por seguridad
BLOCKED_PORTS = [
    22,    # SSH
    23,    # Telnet
    3389,  # RDP
    1433,  # MSSQL
    3306,  # MySQL
    27017  # MongoDB
]

# Configuración de firewall
FIREWALL_CONFIG = {
    'default_policy': 'deny',  # Política por defecto: denegar todo
    'allow_local': True,       # Permitir conexiones locales
    'log_blocked': True,       # Registrar intentos bloqueados
    'notify_admin': True       # Notificar al administrador de intentos bloqueados
}

def get_required_ports():
    """Retorna los puertos que son necesarios para el funcionamiento del sistema."""
    return {port: info for port, info in SYSTEM_PORTS.items() if info['required']}

def get_restricted_ports():
    """Retorna los puertos que deben estar restringidos a conexiones locales."""
    return {port: info for port, info in SYSTEM_PORTS.items() if info['restricted']}

def is_port_allowed(port):
    """Verifica si un puerto está permitido en la configuración."""
    return port in SYSTEM_PORTS

def is_port_required(port):
    """Verifica si un puerto es necesario para el funcionamiento del sistema."""
    return port in SYSTEM_PORTS and SYSTEM_PORTS[port]['required']

def get_port_info(port):
    """Retorna la información de un puerto específico."""
    return SYSTEM_PORTS.get(port, None) 