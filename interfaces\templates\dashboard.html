<!DOCTYPE html>
<html>
<head>
    <title>Hamilton AI Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .dashboard-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 2px solid #007acc;
            font-weight: bold;
        }
        .log-entry {
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-info { background-color: #e3f2fd; }
        .log-warning { background-color: #fff3e0; }
        .log-error { background-color: #ffebee; }
        .notification {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .notification-info { border-left-color: #2196f3; }
        .notification-warning { border-left-color: #ff9800; }
        .notification-error { border-left-color: #f44336; }
        .metric-card {
            text-align: center;
            padding: 15px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007acc;
        }
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .ai-engine-status {
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
            margin-bottom: 15px;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
            background-color: #ccc;
        }
        .status-dot.active {
            background-color: #28a745;
        }
        .status-dot.inactive {
            background-color: #dc3545;
        }
        .model-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .quality-metric {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .progress {
            height: 10px;
            margin: 5px 0;
        }
        .progress-bar {
            background-color: #28a745;
            transition: width 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="row mb-4">
            <div class="col">
                <h1>🤖 Hamilton AI Dashboard</h1>
                <p class="text-muted">Monitoreo y Control en Tiempo Real</p>
            </div>
        </div>

        <!-- Métricas Principales -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="auth-attempts">0</div>
                    <div class="metric-label">Intentos de Autenticación</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="failed-attempts">0</div>
                    <div class="metric-label">Intentos Fallidos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="accuracy">0%</div>
                    <div class="metric-label">Precisión de Aprendizaje</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value" id="satisfaction">0%</div>
                    <div class="metric-label">Satisfacción del Usuario</div>
                </div>
            </div>
        </div>

        <!-- Gráficos y Logs -->
        <div class="row">
            <!-- Gráficos -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        Métricas de Aprendizaje
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="learning-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notificaciones -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        Notificaciones
                        <button class="btn btn-sm btn-outline-primary float-end" onclick="clearNotifications()">
                            Limpiar
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="notifications-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Logs del Sistema
                        <div class="float-end">
                            <select class="form-select form-select-sm" id="log-level" onchange="filterLogs()">
                                <option value="">Todos los niveles</option>
                                <option value="INFO">Info</option>
                                <option value="WARNING">Warning</option>
                                <option value="ERROR">Error</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="logs-container" style="max-height: 400px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Estado de los Motores de IA</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="ai-engine-status" id="gpt-status">
                                    <h6>GPT</h6>
                                    <div class="status-indicator">
                                        <span class="status-dot"></span>
                                        <span class="status-text">Cargando...</span>
                                    </div>
                                    <div class="model-info"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ai-engine-status" id="anthropic-status">
                                    <h6>Anthropic</h6>
                                    <div class="status-indicator">
                                        <span class="status-dot"></span>
                                        <span class="status-text">Cargando...</span>
                                    </div>
                                    <div class="model-info"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ai-engine-status" id="huggingface-status">
                                    <h6>Hugging Face</h6>
                                    <div class="status-indicator">
                                        <span class="status-dot"></span>
                                        <span class="status-text">Cargando...</span>
                                    </div>
                                    <div class="model-info"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ai-engine-status" id="local-status">
                                    <h6>Modelo Local</h6>
                                    <div class="status-indicator">
                                        <span class="status-dot"></span>
                                        <span class="status-text">Cargando...</span>
                                    </div>
                                    <div class="model-info"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Métricas de Calidad</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="quality-metric">
                                    <h6>Relevancia</h6>
                                    <div class="progress">
                                        <div id="relevance-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="relevance-value">0%</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quality-metric">
                                    <h6>Coherencia</h6>
                                    <div class="progress">
                                        <div id="coherence-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="coherence-value">0%</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quality-metric">
                                    <h6>Completitud</h6>
                                    <div class="progress">
                                        <div id="completeness-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="completeness-value">0%</small>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="quality-metric">
                                    <h6>Claridad</h6>
                                    <div class="progress">
                                        <div id="clarity-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="clarity-value">0%</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quality-metric">
                                    <h6>Uso de Contexto</h6>
                                    <div class="progress">
                                        <div id="context-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="context-value">0%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let ws = null;
        let learningChart = null;

        // Conectar WebSocket
        function connectWebSocket() {
            ws = new WebSocket(`ws://${window.location.host}/ws`);
            
            ws.onopen = () => {
                console.log('Conectado al WebSocket');
                addNotification('Sistema', 'Conectado al dashboard', 'info');
            };
            
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = () => {
                console.log('Conexión WebSocket cerrada');
                addNotification('Sistema', 'Conexión perdida - Reintentando...', 'warning');
                setTimeout(connectWebSocket, 3000);
            };
        }

        // Manejar mensajes WebSocket
        function handleWebSocketMessage(data) {
            if (data.type === 'notification') {
                addNotification(data.data.title, data.data.message, data.data.level);
            } else if (data.type === 'metrics') {
                updateMetrics(data.data);
            }
        }

        // Añadir notificación
        function addNotification(title, message, level) {
            const container = document.getElementById('notifications-container');
            const notification = document.createElement('div');
            notification.className = `notification notification-${level}`;
            notification.innerHTML = `
                <strong>${title}</strong>
                <p class="mb-0">${message}</p>
                <small class="text-muted">${new Date().toLocaleTimeString()}</small>
            `;
            container.insertBefore(notification, container.firstChild);
        }

        // Limpiar notificaciones
        function clearNotifications() {
            document.getElementById('notifications-container').innerHTML = '';
        }

        // Actualizar métricas
        function updateMetrics(metrics) {
            document.getElementById('auth-attempts').textContent = metrics.security.auth_attempts;
            document.getElementById('failed-attempts').textContent = metrics.security.failed_attempts;
            document.getElementById('accuracy').textContent = `${(metrics.learning.accuracy * 100).toFixed(1)}%`;
            document.getElementById('satisfaction').textContent = `${(metrics.learning.user_satisfaction * 100).toFixed(1)}%`;
            
            updateLearningChart(metrics.learning);
        }

        // Actualizar gráfico de aprendizaje
        function updateLearningChart(learningData) {
            if (!learningChart) {
                const ctx = document.getElementById('learning-chart').getContext('2d');
                learningChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'Precisión',
                                data: [],
                                borderColor: '#2196f3',
                                tension: 0.1
                            },
                            {
                                label: 'Satisfacción',
                                data: [],
                                borderColor: '#4caf50',
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 1
                            }
                        }
                    }
                });
            }

            const timestamps = learningData.timestamps || [];
            learningChart.data.labels = timestamps;
            learningChart.data.datasets[0].data = learningData.accuracy;
            learningChart.data.datasets[1].data = learningData.user_satisfaction;
            learningChart.update();
        }

        // Cargar logs
        async function loadLogs(level = '') {
            try {
                const response = await fetch(`/api/logs?level=${level}`);
                const logs = await response.json();
                
                const container = document.getElementById('logs-container');
                container.innerHTML = '';
                
                logs.forEach(log => {
                    const entry = document.createElement('div');
                    entry.className = `log-entry log-${log.level.toLowerCase()}`;
                    entry.innerHTML = `
                        <strong>[${log.timestamp}]</strong> ${log.message}
                    `;
                    container.appendChild(entry);
                });
            } catch (error) {
                console.error('Error cargando logs:', error);
            }
        }

        // Filtrar logs
        function filterLogs() {
            const level = document.getElementById('log-level').value;
            loadLogs(level);
        }

        function updateAIStatus() {
            fetch('/api/ai-status')
                .then(response => response.json())
                .then(data => {
                    // Actualizar estado de GPT
                    const gptStatus = document.getElementById('gpt-status');
                    const gptDot = gptStatus.querySelector('.status-dot');
                    const gptText = gptStatus.querySelector('.status-text');
                    const gptInfo = gptStatus.querySelector('.model-info');
                    
                    gptDot.className = `status-dot ${data.gpt.enabled && data.gpt.initialized ? 'active' : 'inactive'}`;
                    gptText.textContent = data.gpt.enabled && data.gpt.initialized ? 'Activo' : 'Inactivo';
                    gptInfo.textContent = data.gpt.model || 'No disponible';
                    
                    // Actualizar estado de Anthropic
                    const anthropicStatus = document.getElementById('anthropic-status');
                    const anthropicDot = anthropicStatus.querySelector('.status-dot');
                    const anthropicText = anthropicStatus.querySelector('.status-text');
                    const anthropicInfo = anthropicStatus.querySelector('.model-info');
                    
                    anthropicDot.className = `status-dot ${data.anthropic.enabled && data.anthropic.initialized ? 'active' : 'inactive'}`;
                    anthropicText.textContent = data.anthropic.enabled && data.anthropic.initialized ? 'Activo' : 'Inactivo';
                    anthropicInfo.textContent = data.anthropic.model || 'No disponible';
                    
                    // Actualizar estado de Hugging Face
                    const hfStatus = document.getElementById('huggingface-status');
                    const hfDot = hfStatus.querySelector('.status-dot');
                    const hfText = hfStatus.querySelector('.status-text');
                    const hfInfo = hfStatus.querySelector('.model-info');
                    
                    hfDot.className = `status-dot ${data.huggingface.enabled && data.huggingface.initialized ? 'active' : 'inactive'}`;
                    hfText.textContent = data.huggingface.enabled && data.huggingface.initialized ? 'Activo' : 'Inactivo';
                    hfInfo.textContent = data.huggingface.model || 'No disponible';
                    
                    // Actualizar estado del modelo local
                    const localStatus = document.getElementById('local-status');
                    const localDot = localStatus.querySelector('.status-dot');
                    const localText = localStatus.querySelector('.status-text');
                    const localInfo = localStatus.querySelector('.model-info');
                    
                    localDot.className = `status-dot ${data.local.enabled ? 'active' : 'inactive'}`;
                    localText.textContent = data.local.enabled ? 'Activo' : 'Inactivo';
                    localInfo.textContent = `Aprendizaje: ${data.local.learning_stats.accuracy.toFixed(2)}%`;

                    // Actualizar métricas de calidad
                    if (data.quality_metrics) {
                        updateQualityMetrics(data);
                    }
                })
                .catch(error => console.error('Error actualizando estado de IA:', error));
        }

        function updateQualityMetrics(data) {
            const metrics = data.quality_metrics;
            
            // Actualizar barras de progreso y valores
            updateMetric('relevance', metrics.relevance * 100);
            updateMetric('coherence', metrics.coherence * 100);
            updateMetric('completeness', metrics.completeness * 100);
            updateMetric('clarity', metrics.clarity * 100);
            updateMetric('context', metrics.context_usage * 100);
        }

        function updateMetric(name, value) {
            const bar = document.getElementById(`${name}-bar`);
            const valueElement = document.getElementById(`${name}-value`);
            
            if (bar && valueElement) {
                bar.style.width = `${value}%`;
                valueElement.textContent = `${value.toFixed(1)}%`;
                
                // Actualizar color según el valor
                if (value >= 80) {
                    bar.className = 'progress-bar bg-success';
                } else if (value >= 60) {
                    bar.className = 'progress-bar bg-warning';
                } else {
                    bar.className = 'progress-bar bg-danger';
                }
            }
        }

        // Inicializar
        connectWebSocket();
        loadLogs();
        
        // Actualizar logs cada 5 segundos
        setInterval(() => loadLogs(document.getElementById('log-level').value), 5000);

        // Actualizar estado de IA cada 30 segundos
        setInterval(updateAIStatus, 30000);
        updateAIStatus(); // Actualizar inmediatamente al cargar
    </script>
</body>
</html> 