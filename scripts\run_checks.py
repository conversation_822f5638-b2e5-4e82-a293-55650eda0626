#!/usr/bin/env python3
"""
Script para ejecutar todas las verificaciones del sistema.
"""

import os
import sys
import json
import logging
import subprocess
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/checks.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'tests', 'analysis']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivos necesarios
        required_files = [
            'requirements.txt',
            'config/settings.py',
            'logs/metrics.json'
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                logger.error(f"Archivo no encontrado: {file_path}")
                return False

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def run_tests():
    """Ejecuta las pruebas unitarias."""
    try:
        logger.info("Ejecutando pruebas unitarias...")
        result = subprocess.run(
            ['pytest', 'tests/', '-v', '--cov=ai'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Pruebas completadas exitosamente")
            return True
        else:
            logger.error("Error en las pruebas unitarias")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Error al ejecutar las pruebas: {str(e)}")
        return False

def check_dependencies():
    """Verifica las dependencias del proyecto."""
    try:
        logger.info("Verificando dependencias...")
        result = subprocess.run(
            ['pip', 'check'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Dependencias verificadas correctamente")
            return True
        else:
            logger.error("Problemas con las dependencias:")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"Error al verificar dependencias: {str(e)}")
        return False

def check_metrics():
    """Verifica el archivo de métricas."""
    try:
        metrics_file = Path('logs/metrics.json')
        if not metrics_file.exists():
            logger.error("Archivo de métricas no encontrado")
            return False
            
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
            
        if not isinstance(metrics, list):
            logger.error("Formato de métricas inválido")
            return False
            
        logger.info(f"Archivo de métricas verificado: {len(metrics)} registros")
        return True
        
    except Exception as e:
        logger.error(f"Error al verificar métricas: {str(e)}")
        return False

def save_check_results(results):
    """Guarda los resultados de las verificaciones."""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = Path(f'logs/checks_{timestamp}.json')
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Resultados guardados en {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error al guardar resultados: {str(e)}")
        return False

def run_checks():
    """Ejecuta todas las verificaciones."""
    try:
        results = {
            'timestamp': datetime.now().isoformat(),
            'environment': verify_environment(),
            'tests': run_tests(),
            'dependencies': check_dependencies(),
            'metrics': check_metrics()
        }
        
        # Guardar resultados
        if save_check_results(results):
            logger.info("Verificaciones completadas")
            
            # Mostrar resumen
            print("\nResumen de Verificaciones:")
            print("-" * 50)
            for check, status in results.items():
                if check != 'timestamp':
                    print(f"- {check}: {'✅' if status else '❌'}")
                    
        else:
            logger.error("Error al guardar los resultados")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error al ejecutar verificaciones: {str(e)}")
        sys.exit(1)

def main():
    """Función principal."""
    try:
        logger.info("Iniciando verificaciones del sistema...")
        run_checks()
        
    except KeyboardInterrupt:
        logger.info("Verificaciones detenidas por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 