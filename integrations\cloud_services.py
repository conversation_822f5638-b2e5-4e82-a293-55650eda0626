"""
Hamilton AI Assistant - Cloud Services Integration
Integración con servicios de nube (AWS, Azure, Google Cloud)
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import requests
import base64

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class CloudServicesManager:
    """Gestor de servicios de nube"""
    
    def __init__(self):
        self.config_file = Path("config/cloud_services.json")
        self.services = {}
        self.load_configuration()
    
    def load_configuration(self):
        """Carga configuración de servicios de nube"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.services = json.load(f)
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de nube: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.services = {
            "aws": {
                "enabled": False,
                "region": "us-east-1",
                "access_key_id": "YOUR_AWS_ACCESS_KEY",
                "secret_access_key": "YOUR_AWS_SECRET_KEY",
                "services": {
                    "s3": {"enabled": False, "bucket": "hamilton-data"},
                    "polly": {"enabled": False, "voice_id": "Miguel"},
                    "transcribe": {"enabled": False},
                    "comprehend": {"enabled": False},
                    "lambda": {"enabled": False}
                }
            },
            "azure": {
                "enabled": False,
                "subscription_id": "YOUR_AZURE_SUBSCRIPTION_ID",
                "resource_group": "hamilton-resources",
                "services": {
                    "cognitive_services": {
                        "enabled": False,
                        "key": "YOUR_COGNITIVE_SERVICES_KEY",
                        "endpoint": "YOUR_ENDPOINT"
                    },
                    "speech": {"enabled": False},
                    "storage": {"enabled": False, "account_name": "hamiltondata"}
                }
            },
            "google_cloud": {
                "enabled": False,
                "project_id": "hamilton-ai-project",
                "credentials_path": "path/to/credentials.json",
                "services": {
                    "text_to_speech": {"enabled": False},
                    "speech_to_text": {"enabled": False},
                    "natural_language": {"enabled": False},
                    "cloud_storage": {"enabled": False, "bucket": "hamilton-storage"}
                }
            },
            "openai": {
                "enabled": False,
                "api_key": "YOUR_OPENAI_API_KEY",
                "model": "gpt-4",
                "max_tokens": 1000,
                "temperature": 0.7
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.services, f, indent=2, ensure_ascii=False)

class AWSIntegration:
    """Integración con Amazon Web Services"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", False)
    
    async def text_to_speech_polly(self, text: str, voice_id: str = None) -> Optional[bytes]:
        """Convierte texto a voz usando Amazon Polly"""
        if not self.enabled or not self.config["services"]["polly"]["enabled"]:
            return None
        
        try:
            import boto3
            
            polly = boto3.client(
                'polly',
                aws_access_key_id=self.config["access_key_id"],
                aws_secret_access_key=self.config["secret_access_key"],
                region_name=self.config["region"]
            )
            
            response = polly.synthesize_speech(
                Text=text,
                OutputFormat='mp3',
                VoiceId=voice_id or self.config["services"]["polly"]["voice_id"],
                LanguageCode='es-ES'
            )
            
            return response['AudioStream'].read()
            
        except ImportError:
            logger.warning("boto3 no instalado. Instalar con: pip install boto3")
            return None
        except Exception as e:
            logger.error(f"Error con Amazon Polly: {e}")
            return None
    
    async def analyze_sentiment(self, text: str) -> Optional[Dict[str, Any]]:
        """Analiza sentimiento usando Amazon Comprehend"""
        if not self.enabled or not self.config["services"]["comprehend"]["enabled"]:
            return None
        
        try:
            import boto3
            
            comprehend = boto3.client(
                'comprehend',
                aws_access_key_id=self.config["access_key_id"],
                aws_secret_access_key=self.config["secret_access_key"],
                region_name=self.config["region"]
            )
            
            response = comprehend.detect_sentiment(
                Text=text,
                LanguageCode='es'
            )
            
            return {
                'sentiment': response['Sentiment'],
                'confidence': response['SentimentScore']
            }
            
        except ImportError:
            logger.warning("boto3 no instalado")
            return None
        except Exception as e:
            logger.error(f"Error con Amazon Comprehend: {e}")
            return None
    
    async def store_data_s3(self, data: bytes, key: str) -> bool:
        """Almacena datos en Amazon S3"""
        if not self.enabled or not self.config["services"]["s3"]["enabled"]:
            return False
        
        try:
            import boto3
            
            s3 = boto3.client(
                's3',
                aws_access_key_id=self.config["access_key_id"],
                aws_secret_access_key=self.config["secret_access_key"],
                region_name=self.config["region"]
            )
            
            bucket = self.config["services"]["s3"]["bucket"]
            s3.put_object(Bucket=bucket, Key=key, Body=data)
            
            logger.info(f"Datos almacenados en S3: {key}")
            return True
            
        except ImportError:
            logger.warning("boto3 no instalado")
            return False
        except Exception as e:
            logger.error(f"Error almacenando en S3: {e}")
            return False

class AzureIntegration:
    """Integración con Microsoft Azure"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", False)
    
    async def text_to_speech_azure(self, text: str) -> Optional[bytes]:
        """Convierte texto a voz usando Azure Speech"""
        if not self.enabled or not self.config["services"]["speech"]["enabled"]:
            return None
        
        try:
            import azure.cognitiveservices.speech as speechsdk
            
            speech_config = speechsdk.SpeechConfig(
                subscription=self.config["services"]["cognitive_services"]["key"],
                region=self.config["region"]
            )
            
            speech_config.speech_synthesis_voice_name = "es-ES-AlvaroNeural"
            
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config)
            result = synthesizer.speak_text_async(text).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return result.audio_data
            
            return None
            
        except ImportError:
            logger.warning("Azure Speech SDK no instalado")
            return None
        except Exception as e:
            logger.error(f"Error con Azure Speech: {e}")
            return None
    
    async def analyze_text_azure(self, text: str) -> Optional[Dict[str, Any]]:
        """Analiza texto usando Azure Cognitive Services"""
        if not self.enabled:
            return None
        
        try:
            from azure.ai.textanalytics import TextAnalyticsClient
            from azure.core.credentials import AzureKeyCredential
            
            endpoint = self.config["services"]["cognitive_services"]["endpoint"]
            key = self.config["services"]["cognitive_services"]["key"]
            
            client = TextAnalyticsClient(endpoint=endpoint, credential=AzureKeyCredential(key))
            
            # Análisis de sentimiento
            sentiment_result = client.analyze_sentiment(documents=[text], language="es")
            
            # Extracción de entidades
            entities_result = client.recognize_entities(documents=[text], language="es")
            
            return {
                'sentiment': sentiment_result[0].sentiment,
                'confidence': sentiment_result[0].confidence_scores,
                'entities': [entity.text for entity in entities_result[0].entities]
            }
            
        except ImportError:
            logger.warning("Azure Text Analytics no instalado")
            return None
        except Exception as e:
            logger.error(f"Error con Azure Text Analytics: {e}")
            return None

class GoogleCloudIntegration:
    """Integración con Google Cloud Platform"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", False)
    
    async def text_to_speech_gcp(self, text: str) -> Optional[bytes]:
        """Convierte texto a voz usando Google Cloud Text-to-Speech"""
        if not self.enabled or not self.config["services"]["text_to_speech"]["enabled"]:
            return None
        
        try:
            from google.cloud import texttospeech
            import os
            
            # Configurar credenciales
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.config["credentials_path"]
            
            client = texttospeech.TextToSpeechClient()
            
            synthesis_input = texttospeech.SynthesisInput(text=text)
            voice = texttospeech.VoiceSelectionParams(
                language_code="es-ES",
                name="es-ES-Standard-B",  # Voz masculina
                ssml_gender=texttospeech.SsmlVoiceGender.MALE
            )
            audio_config = texttospeech.AudioConfig(
                audio_encoding=texttospeech.AudioEncoding.MP3
            )
            
            response = client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=audio_config
            )
            
            return response.audio_content
            
        except ImportError:
            logger.warning("Google Cloud Text-to-Speech no instalado")
            return None
        except Exception as e:
            logger.error(f"Error con Google Cloud TTS: {e}")
            return None
    
    async def analyze_text_gcp(self, text: str) -> Optional[Dict[str, Any]]:
        """Analiza texto usando Google Cloud Natural Language"""
        if not self.enabled or not self.config["services"]["natural_language"]["enabled"]:
            return None
        
        try:
            from google.cloud import language_v1
            import os
            
            # Configurar credenciales
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.config["credentials_path"]
            
            client = language_v1.LanguageServiceClient()
            
            document = language_v1.Document(
                content=text,
                type_=language_v1.Document.Type.PLAIN_TEXT,
                language="es"
            )
            
            # Análisis de sentimiento
            sentiment_response = client.analyze_sentiment(
                request={'document': document}
            )
            
            # Análisis de entidades
            entities_response = client.analyze_entities(
                request={'document': document}
            )
            
            return {
                'sentiment': {
                    'score': sentiment_response.document_sentiment.score,
                    'magnitude': sentiment_response.document_sentiment.magnitude
                },
                'entities': [
                    {
                        'name': entity.name,
                        'type': entity.type_.name,
                        'salience': entity.salience
                    }
                    for entity in entities_response.entities
                ]
            }
            
        except ImportError:
            logger.warning("Google Cloud Natural Language no instalado")
            return None
        except Exception as e:
            logger.error(f"Error con Google Cloud NL: {e}")
            return None

class OpenAIIntegration:
    """Integración con OpenAI"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", False)
    
    async def generate_response(self, prompt: str, context: List[Dict] = None) -> Optional[str]:
        """Genera respuesta usando OpenAI GPT"""
        if not self.enabled:
            return None
        
        try:
            import openai
            
            openai.api_key = self.config["api_key"]
            
            messages = [
                {"role": "system", "content": "Eres Hamilton, un asistente personal de IA masculino, formal pero amigable, diseñado específicamente para ayudar al señor Ibero."}
            ]
            
            if context:
                messages.extend(context[-5:])  # Últimos 5 mensajes de contexto
            
            messages.append({"role": "user", "content": prompt})
            
            response = openai.ChatCompletion.create(
                model=self.config["model"],
                messages=messages,
                max_tokens=self.config["max_tokens"],
                temperature=self.config["temperature"]
            )
            
            return response.choices[0].message.content
            
        except ImportError:
            logger.warning("OpenAI no instalado. Instalar con: pip install openai")
            return None
        except Exception as e:
            logger.error(f"Error con OpenAI: {e}")
            return None

class CloudServicesIntegration:
    """Integración principal de servicios de nube"""
    
    def __init__(self):
        self.manager = CloudServicesManager()
        self.aws = AWSIntegration(self.manager.services.get("aws", {}))
        self.azure = AzureIntegration(self.manager.services.get("azure", {}))
        self.gcp = GoogleCloudIntegration(self.manager.services.get("google_cloud", {}))
        self.openai = OpenAIIntegration(self.manager.services.get("openai", {}))
    
    async def generate_voice(self, text: str, preferred_service: str = "auto") -> Optional[bytes]:
        """Genera voz usando el mejor servicio disponible"""
        services = []
        
        if preferred_service == "aws" or preferred_service == "auto":
            services.append(self.aws.text_to_speech_polly)
        if preferred_service == "azure" or preferred_service == "auto":
            services.append(self.azure.text_to_speech_azure)
        if preferred_service == "gcp" or preferred_service == "auto":
            services.append(self.gcp.text_to_speech_gcp)
        
        for service in services:
            try:
                result = await service(text)
                if result:
                    return result
            except Exception as e:
                logger.warning(f"Error en servicio de voz: {e}")
                continue
        
        return None
    
    async def analyze_text(self, text: str) -> Dict[str, Any]:
        """Analiza texto usando múltiples servicios"""
        results = {}
        
        # Intentar con AWS
        aws_result = await self.aws.analyze_sentiment(text)
        if aws_result:
            results["aws"] = aws_result
        
        # Intentar con Azure
        azure_result = await self.azure.analyze_text_azure(text)
        if azure_result:
            results["azure"] = azure_result
        
        # Intentar con Google Cloud
        gcp_result = await self.gcp.analyze_text_gcp(text)
        if gcp_result:
            results["gcp"] = gcp_result
        
        return results
    
    async def get_ai_response(self, prompt: str, context: List[Dict] = None) -> Optional[str]:
        """Obtiene respuesta de IA usando OpenAI"""
        return await self.openai.generate_response(prompt, context)
    
    def get_available_services(self) -> Dict[str, bool]:
        """Obtiene servicios disponibles"""
        return {
            "aws": self.aws.enabled,
            "azure": self.azure.enabled,
            "google_cloud": self.gcp.enabled,
            "openai": self.openai.enabled
        }

# Instancia global
hamilton_cloud = CloudServicesIntegration()
