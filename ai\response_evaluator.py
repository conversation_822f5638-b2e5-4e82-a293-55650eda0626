import logging
from typing import Dict, Any, List
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class ResponseEvaluator:
    """Evaluador de calidad de respuestas de Hamilton"""
    
    def __init__(self):
        """Inicializa el evaluador de respuestas"""
        self.vectorizer = TfidfVectorizer()
        self.quality_metrics = {
            "relevance": 0.0,
            "coherence": 0.0,
            "completeness": 0.0,
            "clarity": 0.0,
            "context_usage": 0.0
        }
        self.history = []
        self.logger = logging.getLogger(__name__)

    def evaluate_response(self, query: str, response: str, context: Dict[str, Any] = None) -> Dict[str, float]:
        """Evalúa la calidad de una respuesta"""
        try:
            # Calcular métricas
            metrics = {
                "relevance": self._calculate_relevance(query, response),
                "coherence": self._calculate_coherence(response),
                "completeness": self._calculate_completeness(response),
                "clarity": self._calculate_clarity(response),
                "context_usage": self._calculate_context_usage(response, context)
            }
            
            # Actualizar métricas globales
            self._update_metrics(metrics)
            
            # Guardar en historial
            self._update_history(query, response, metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error evaluando respuesta: {e}")
            return self.quality_metrics

    def _calculate_relevance(self, query: str, response: str) -> float:
        """Calcula la relevancia de la respuesta respecto a la consulta"""
        try:
            # Vectorizar consulta y respuesta
            vectors = self.vectorizer.fit_transform([query, response])
            
            # Calcular similitud coseno
            similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
            
            return float(similarity)
        except Exception as e:
            self.logger.error(f"Error calculando relevancia: {e}")
            return 0.0

    def _calculate_coherence(self, response: str) -> float:
        """Calcula la coherencia de la respuesta"""
        try:
            # Dividir respuesta en oraciones
            sentences = response.split('.')
            if len(sentences) < 2:
                return 1.0
            
            # Vectorizar oraciones
            vectors = self.vectorizer.fit_transform(sentences)
            
            # Calcular similitud promedio entre oraciones consecutivas
            similarities = []
            for i in range(len(sentences) - 1):
                similarity = cosine_similarity(vectors[i:i+1], vectors[i+1:i+2])[0][0]
                similarities.append(similarity)
            
            return float(np.mean(similarities))
        except Exception as e:
            self.logger.error(f"Error calculando coherencia: {e}")
            return 0.0

    def _calculate_completeness(self, response: str) -> float:
        """Calcula la completitud de la respuesta"""
        try:
            # Factores que indican completitud
            factors = {
                "length": min(len(response.split()) / 50, 1.0),  # Longitud óptima ~50 palabras
                "structure": 1.0 if any(marker in response.lower() for marker in ["primero", "segundo", "finalmente"]) else 0.5,
                "detail": 1.0 if len(response.split('.')) > 2 else 0.5
            }
            
            return float(np.mean(list(factors.values())))
        except Exception as e:
            self.logger.error(f"Error calculando completitud: {e}")
            return 0.0

    def _calculate_clarity(self, response: str) -> float:
        """Calcula la claridad de la respuesta"""
        try:
            # Factores que indican claridad
            factors = {
                "sentence_length": 1.0 if np.mean([len(s.split()) for s in response.split('.')]) < 20 else 0.5,
                "vocabulary": 1.0 if len(set(response.lower().split())) / len(response.split()) > 0.7 else 0.5,
                "structure": 1.0 if any(marker in response.lower() for marker in ["por ejemplo", "es decir", "en resumen"]) else 0.5
            }
            
            return float(np.mean(list(factors.values())))
        except Exception as e:
            self.logger.error(f"Error calculando claridad: {e}")
            return 0.0

    def _calculate_context_usage(self, response: str, context: Dict[str, Any] = None) -> float:
        """Calcula el uso del contexto en la respuesta"""
        if not context:
            return 0.0
            
        try:
            # Contar referencias al contexto
            context_terms = set()
            for key, value in context.items():
                if isinstance(value, str):
                    context_terms.update(value.lower().split())
            
            # Calcular proporción de términos del contexto usados
            response_terms = set(response.lower().split())
            used_terms = context_terms.intersection(response_terms)
            
            return float(len(used_terms) / len(context_terms)) if context_terms else 0.0
        except Exception as e:
            self.logger.error(f"Error calculando uso de contexto: {e}")
            return 0.0

    def _update_metrics(self, new_metrics: Dict[str, float]):
        """Actualiza las métricas globales"""
        for key, value in new_metrics.items():
            self.quality_metrics[key] = (
                self.quality_metrics[key] * 0.9 + value * 0.1
            )

    def _update_history(self, query: str, response: str, metrics: Dict[str, float]):
        """Actualiza el historial de evaluaciones"""
        self.history.append({
            "query": query,
            "response": response,
            "metrics": metrics,
            "timestamp": time.time()
        })
        
        # Mantener solo las últimas 100 evaluaciones
        if len(self.history) > 100:
            self.history.pop(0)

    def get_quality_metrics(self) -> Dict[str, float]:
        """Obtiene las métricas de calidad actuales"""
        return self.quality_metrics

    def get_evaluation_history(self) -> List[Dict[str, Any]]:
        """Obtiene el historial de evaluaciones"""
        return self.history

# Instancia global
response_evaluator = ResponseEvaluator() 