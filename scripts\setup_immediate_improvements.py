#!/usr/bin/env python3
"""
Hamilton AI Assistant - Immediate Improvements Setup
Script maestro para implementar todas las mejoras inmediatas (Prioridad Alta)
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import Dict, List

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImmediateImprovementsSetup:
    """Configurador de mejoras inmediatas para Hamilton"""
    
    def __init__(self):
        self.scripts_dir = Path(__file__).parent
        self.improvements = [
            {
                'name': 'SWI-Prolog Installation',
                'description': 'Instalar SWI-Prolog para razonamiento completo',
                'script': 'install_swi_prolog.py',
                'priority': 1,
                'estimated_time': '5-10 minutos'
            },
            {
                'name': 'OpenAI API Configuration',
                'description': 'Configurar OpenAI API para capacidades GPT avanzadas',
                'script': 'setup_openai_api.py',
                'priority': 2,
                'estimated_time': '3-5 minutos'
            },
            {
                'name': 'Voice Recognition Optimization',
                'description': 'Optimizar reconocimiento de voz con mejor hardware',
                'script': 'optimize_voice_recognition.py',
                'priority': 3,
                'estimated_time': '5-8 minutos'
            },
            {
                'name': 'Face Recognition Enhancement',
                'description': 'Agregar más encodings faciales para mejor precisión',
                'script': 'enhance_face_recognition.py',
                'priority': 4,
                'estimated_time': '10-15 minutos'
            }
        ]
    
    def show_improvements_menu(self):
        """Muestra el menú de mejoras disponibles"""
        print("🚀 HAMILTON - MEJORAS INMEDIATAS (PRIORIDAD ALTA)")
        print("=" * 60)
        print("\n📋 Mejoras disponibles:")
        print("-" * 40)
        
        for i, improvement in enumerate(self.improvements, 1):
            print(f"{i}. {improvement['name']}")
            print(f"   📝 {improvement['description']}")
            print(f"   ⏱️ Tiempo estimado: {improvement['estimated_time']}")
            print()
        
        print("0. Ejecutar todas las mejoras automáticamente")
        print("q. Salir")
    
    def run_improvement(self, improvement: Dict) -> bool:
        """Ejecuta una mejora específica"""
        script_path = self.scripts_dir / improvement['script']
        
        if not script_path.exists():
            logger.error(f"Script no encontrado: {script_path}")
            return False
        
        print(f"\n🔧 Ejecutando: {improvement['name']}")
        print(f"📝 {improvement['description']}")
        print(f"⏱️ Tiempo estimado: {improvement['estimated_time']}")
        print("-" * 50)
        
        try:
            # Ejecutar script
            result = subprocess.run([
                sys.executable, str(script_path)
            ], capture_output=False, text=True)
            
            if result.returncode == 0:
                print(f"✅ {improvement['name']} completado exitosamente")
                return True
            else:
                print(f"❌ Error en {improvement['name']}")
                return False
                
        except Exception as e:
            logger.error(f"Error ejecutando {improvement['name']}: {e}")
            return False
    
    def run_all_improvements(self) -> Dict[str, bool]:
        """Ejecuta todas las mejoras automáticamente"""
        print("\n🚀 EJECUTANDO TODAS LAS MEJORAS INMEDIATAS")
        print("=" * 50)
        
        results = {}
        total_time = sum([
            10, 5, 8, 15  # Tiempos estimados en minutos
        ])
        
        print(f"⏱️ Tiempo total estimado: {total_time} minutos")
        print("💡 Puedes interrumpir en cualquier momento con Ctrl+C")
        
        input("\nPresiona ENTER para comenzar...")
        
        for improvement in self.improvements:
            try:
                success = self.run_improvement(improvement)
                results[improvement['name']] = success
                
                if not success:
                    print(f"\n⚠️ {improvement['name']} falló")
                    continue_anyway = input("¿Continuar con las siguientes mejoras? (s/n): ").lower()
                    if continue_anyway != 's':
                        break
                
            except KeyboardInterrupt:
                print(f"\n⏹️ Proceso interrumpido por el usuario")
                break
            except Exception as e:
                logger.error(f"Error inesperado: {e}")
                results[improvement['name']] = False
        
        return results
    
    def show_results_summary(self, results: Dict[str, bool]):
        """Muestra resumen de resultados"""
        print("\n" + "=" * 60)
        print("📊 RESUMEN DE MEJORAS IMPLEMENTADAS")
        print("=" * 60)
        
        successful = 0
        total = len(results)
        
        for improvement_name, success in results.items():
            status = "✅ EXITOSO" if success else "❌ FALLIDO"
            print(f"{improvement_name:.<40} {status}")
            if success:
                successful += 1
        
        print(f"\nTotal: {successful}/{total} mejoras implementadas exitosamente")
        
        if successful == total:
            print("\n🎉 ¡TODAS LAS MEJORAS INMEDIATAS COMPLETADAS!")
            print("\n🚀 Hamilton ahora tiene:")
            print("✅ Razonamiento Prolog completo")
            print("✅ Capacidades GPT avanzadas")
            print("✅ Reconocimiento de voz optimizado")
            print("✅ Reconocimiento facial mejorado")
            
            print("\n📋 Próximos pasos recomendados:")
            print("1. Reinicia Hamilton para cargar todas las mejoras")
            print("2. Prueba las nuevas capacidades")
            print("3. Considera implementar mejoras de Prioridad Media")
            
        elif successful > 0:
            print(f"\n⚠️ {total - successful} mejora(s) necesitan atención")
            print("\n💡 Puedes:")
            print("1. Ejecutar mejoras individuales que fallaron")
            print("2. Revisar logs de error para diagnóstico")
            print("3. Contactar soporte si persisten problemas")
            
        else:
            print("\n❌ No se completaron mejoras exitosamente")
            print("\n🔧 Recomendaciones:")
            print("1. Verifica dependencias del sistema")
            print("2. Ejecuta como administrador si es necesario")
            print("3. Revisa conectividad a internet")
    
    def run_interactive_mode(self):
        """Ejecuta modo interactivo"""
        while True:
            self.show_improvements_menu()
            
            try:
                choice = input("\n🎯 Selecciona una opción: ").strip().lower()
                
                if choice == 'q':
                    print("👋 Saliendo del configurador de mejoras")
                    break
                elif choice == '0':
                    results = self.run_all_improvements()
                    self.show_results_summary(results)
                    break
                else:
                    try:
                        improvement_index = int(choice) - 1
                        if 0 <= improvement_index < len(self.improvements):
                            improvement = self.improvements[improvement_index]
                            success = self.run_improvement(improvement)
                            
                            if success:
                                print(f"\n✅ {improvement['name']} completado")
                            else:
                                print(f"\n❌ {improvement['name']} falló")
                            
                            input("\nPresiona ENTER para continuar...")
                        else:
                            print("❌ Opción inválida")
                    except ValueError:
                        print("❌ Ingresa un número válido")
                        
            except KeyboardInterrupt:
                print("\n\n👋 Saliendo del configurador")
                break
            except Exception as e:
                logger.error(f"Error en modo interactivo: {e}")

def main():
    """Función principal"""
    print("🚀 HAMILTON AI ASSISTANT")
    print("🔧 CONFIGURADOR DE MEJORAS INMEDIATAS")
    print("=" * 50)
    
    setup = ImmediateImprovementsSetup()
    
    # Verificar que los scripts existen
    missing_scripts = []
    for improvement in setup.improvements:
        script_path = setup.scripts_dir / improvement['script']
        if not script_path.exists():
            missing_scripts.append(improvement['script'])
    
    if missing_scripts:
        print("❌ Scripts faltantes:")
        for script in missing_scripts:
            print(f"   • {script}")
        print("\nAsegúrate de que todos los scripts estén en el directorio 'scripts/'")
        return False
    
    print("✅ Todos los scripts de mejora están disponibles")
    
    # Mostrar información inicial
    print("\n📋 Este configurador implementará las siguientes mejoras:")
    print("1. 🧠 Razonamiento Prolog completo con SWI-Prolog")
    print("2. 🤖 Capacidades GPT avanzadas con OpenAI API")
    print("3. 🎤 Reconocimiento de voz optimizado para hardware")
    print("4. 👁️ Reconocimiento facial mejorado con más encodings")
    
    print("\n💡 Estas mejoras transformarán Hamilton en un asistente")
    print("   mucho más inteligente y preciso.")
    
    # Ejecutar modo interactivo
    setup.run_interactive_mode()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Configuración cancelada por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {e}")
        sys.exit(1)
