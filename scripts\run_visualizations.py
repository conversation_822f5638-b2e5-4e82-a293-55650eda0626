#!/usr/bin/env python3
"""
Script para ejecutar las visualizaciones del sistema.
"""

import os
import sys
import json
import logging
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/visualizations.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'visualizations']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivo de métricas
        metrics_file = Path('logs/metrics.json')
        if not metrics_file.exists():
            logger.error("Archivo de métricas no encontrado")
            return False

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def load_metrics(hours=24):
    """Carga las métricas del archivo."""
    try:
        metrics_file = Path('logs/metrics.json')
        
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
            
        # Filtrar por tiempo
        cutoff_time = datetime.now() - timedelta(hours=hours)
        filtered_metrics = [
            m for m in metrics
            if datetime.fromisoformat(m['timestamp']) > cutoff_time
        ]
        
        return filtered_metrics
    except Exception as e:
        logger.error(f"Error al cargar métricas: {str(e)}")
        return None

def prepare_data(metrics):
    """Prepara los datos para visualización."""
    try:
        # Convertir a DataFrame
        df = pd.DataFrame(metrics)
        
        # Convertir timestamp a datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Extraer métricas del sistema
        system_metrics = pd.DataFrame([
            {
                'timestamp': row['timestamp'],
                'cpu_percent': row['cpu']['percent'],
                'memory_percent': row['memory']['percent'],
                'disk_percent': row['disk']['percent'],
                'swap_percent': row['swap']['percent'],
                'processes': row['processes']
            }
            for row in metrics
        ])
        
        # Extraer métricas de procesos
        process_metrics = []
        for row in metrics:
            for proc in row['processes']:
                process_metrics.append({
                    'timestamp': row['timestamp'],
                    'pid': proc['pid'],
                    'name': proc['name'],
                    'cpu_percent': proc['cpu_percent'],
                    'memory_percent': proc['memory_percent']
                })
                
        process_df = pd.DataFrame(process_metrics)
        
        return system_metrics, process_df
    except Exception as e:
        logger.error(f"Error al preparar datos: {str(e)}")
        return None, None

def plot_system_metrics(system_metrics, output_dir):
    """Genera gráficos de métricas del sistema."""
    try:
        # Configurar estilo
        plt.style.use('seaborn')
        
        # CPU y Memoria
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        system_metrics.plot(
            x='timestamp',
            y='cpu_percent',
            ax=ax1,
            label='CPU %',
            color='blue'
        )
        system_metrics.plot(
            x='timestamp',
            y='memory_percent',
            ax=ax1,
            label='Memoria %',
            color='red'
        )
        
        ax1.set_title('Uso de CPU y Memoria')
        ax1.set_xlabel('Tiempo')
        ax1.set_ylabel('Porcentaje')
        ax1.legend()
        ax1.grid(True)
        
        # Disco y Swap
        system_metrics.plot(
            x='timestamp',
            y='disk_percent',
            ax=ax2,
            label='Disco %',
            color='green'
        )
        system_metrics.plot(
            x='timestamp',
            y='swap_percent',
            ax=ax2,
            label='Swap %',
            color='purple'
        )
        
        ax2.set_title('Uso de Disco y Swap')
        ax2.set_xlabel('Tiempo')
        ax2.set_ylabel('Porcentaje')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(output_dir / 'system_metrics.png')
        plt.close()
        
        # Procesos
        plt.figure(figsize=(12, 6))
        system_metrics.plot(
            x='timestamp',
            y='processes',
            label='Número de Procesos',
            color='orange'
        )
        
        plt.title('Número de Procesos')
        plt.xlabel('Tiempo')
        plt.ylabel('Cantidad')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(output_dir / 'process_count.png')
        plt.close()
        
        return True
    except Exception as e:
        logger.error(f"Error al generar gráficos del sistema: {str(e)}")
        return False

def plot_process_metrics(process_df, output_dir):
    """Genera gráficos de métricas de procesos."""
    try:
        # Configurar estilo
        plt.style.use('seaborn')
        
        # Top 10 procesos por CPU
        top_cpu = process_df.nlargest(10, 'cpu_percent')
        plt.figure(figsize=(12, 6))
        sns.barplot(
            data=top_cpu,
            x='name',
            y='cpu_percent'
        )
        
        plt.title('Top 10 Procesos por Uso de CPU')
        plt.xlabel('Proceso')
        plt.ylabel('CPU %')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_dir / 'top_cpu_processes.png')
        plt.close()
        
        # Top 10 procesos por Memoria
        top_memory = process_df.nlargest(10, 'memory_percent')
        plt.figure(figsize=(12, 6))
        sns.barplot(
            data=top_memory,
            x='name',
            y='memory_percent'
        )
        
        plt.title('Top 10 Procesos por Uso de Memoria')
        plt.xlabel('Proceso')
        plt.ylabel('Memoria %')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_dir / 'top_memory_processes.png')
        plt.close()
        
        return True
    except Exception as e:
        logger.error(f"Error al generar gráficos de procesos: {str(e)}")
        return False

def generate_visualizations(hours=24):
    """Genera todas las visualizaciones."""
    try:
        # Crear directorio de salida
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('visualizations') / timestamp
        output_dir.mkdir(parents=True)
        
        # Cargar métricas
        metrics = load_metrics(hours)
        if not metrics:
            logger.error("No se pudieron cargar las métricas")
            return False
            
        # Preparar datos
        system_metrics, process_df = prepare_data(metrics)
        if system_metrics is None or process_df is None:
            logger.error("No se pudieron preparar los datos")
            return False
            
        # Generar visualizaciones
        if not plot_system_metrics(system_metrics, output_dir):
            logger.error("Error al generar gráficos del sistema")
            return False
            
        if not plot_process_metrics(process_df, output_dir):
            logger.error("Error al generar gráficos de procesos")
            return False
            
        logger.info(f"Visualizaciones generadas en {output_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Error al generar visualizaciones: {str(e)}")
        return False

def main():
    """Función principal."""
    try:
        logger.info("Iniciando generación de visualizaciones...")
        
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            sys.exit(1)
            
        # Obtener horas de los argumentos
        hours = 24  # 24 horas por defecto
        if len(sys.argv) > 1:
            try:
                hours = int(sys.argv[1])
            except ValueError:
                print("Error: Las horas deben ser un número entero")
                sys.exit(1)
                
        if generate_visualizations(hours):
            logger.info("Visualizaciones generadas exitosamente")
            sys.exit(0)
        else:
            logger.error("Error al generar visualizaciones")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Generación de visualizaciones detenida por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 