#!/usr/bin/env python3
"""
Script para analizar las capacidades y mejoras de Hamilton.
"""

import os
import sys
import json
import logging
import platform
import subprocess
import importlib
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/capabilities.log')
    ]
)

logger = logging.getLogger(__name__)

class CapabilityAnalyzer:
    def __init__(self):
        self.root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.capabilities = {
            'mejoras_inmediatas': {
                'swi_prolog': False,
                'openai_api': False,
                'voice_recognition': False,
                'facial_encoding': False
            },
            'expansion_funcionalidades': {
                'iot_integration': False,
                'home_automation': False,
                'calendar_integration': False,
                'multilanguage': False,
                'emotion_recognition': False
            },
            'mejoras_ia': {
                'local_models': False,
                'advanced_nlp': False,
                'natural_responses': False,
                'reinforcement_learning': False,
                'predictive_analysis': False
            }
        }

    def check_swi_prolog(self):
        """Verifica la instalación y configuración de SWI-Prolog."""
        try:
            # Verificar si SWI-Prolog está instalado
            result = subprocess.run(['swipl', '--version'], 
                                 capture_output=True, 
                                 text=True)
            if result.returncode == 0:
                self.capabilities['mejoras_inmediatas']['swi_prolog'] = True
                return True
        except FileNotFoundError:
            logger.warning("SWI-Prolog no está instalado")
        return False

    def check_openai_api(self):
        """Verifica la configuración de OpenAI API."""
        try:
            from config.settings import OPENAI_API_KEY
            if OPENAI_API_KEY and len(OPENAI_API_KEY) > 0:
                self.capabilities['mejoras_inmediatas']['openai_api'] = True
                return True
        except ImportError:
            logger.warning("No se encontró la configuración de OpenAI API")
        return False

    def check_voice_recognition(self):
        """Verifica el estado del reconocimiento de voz."""
        try:
            # Verificar módulos necesarios
            import speech_recognition
            import pyaudio
            # Verificar hardware de audio
            audio = pyaudio.PyAudio()
            if audio.get_device_count() > 0:
                self.capabilities['mejoras_inmediatas']['voice_recognition'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para reconocimiento de voz")
        return False

    def check_facial_encoding(self):
        """Verifica el estado del reconocimiento facial."""
        try:
            import face_recognition
            # Verificar si hay modelos de encoding disponibles
            if os.path.exists('models/facial_encodings'):
                self.capabilities['mejoras_inmediatas']['facial_encoding'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para reconocimiento facial")
        return False

    def check_iot_integration(self):
        """Verifica la integración con dispositivos IoT."""
        try:
            # Verificar módulos de IoT
            import paho.mqtt.client
            if os.path.exists('config/iot_config.py'):
                self.capabilities['expansion_funcionalidades']['iot_integration'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para integración IoT")
        return False

    def check_home_automation(self):
        """Verifica las capacidades de automatización del hogar."""
        try:
            if os.path.exists('automation/home_automation.py'):
                self.capabilities['expansion_funcionalidades']['home_automation'] = True
                return True
        except Exception:
            logger.warning("No se encontró el módulo de automatización del hogar")
        return False

    def check_calendar_integration(self):
        """Verifica la integración con calendarios."""
        try:
            import google.oauth2.credentials
            if os.path.exists('config/calendar_config.py'):
                self.capabilities['expansion_funcionalidades']['calendar_integration'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para integración con calendarios")
        return False

    def check_multilanguage(self):
        """Verifica las capacidades multiidioma."""
        try:
            import googletrans
            if os.path.exists('config/language_config.py'):
                self.capabilities['expansion_funcionalidades']['multilanguage'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para soporte multiidioma")
        return False

    def check_emotion_recognition(self):
        """Verifica el reconocimiento de emociones."""
        try:
            import tensorflow
            if os.path.exists('models/emotion_models'):
                self.capabilities['expansion_funcionalidades']['emotion_recognition'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para reconocimiento de emociones")
        return False

    def check_local_models(self):
        """Verifica la integración con modelos locales."""
        try:
            import torch
            if os.path.exists('models/local_models'):
                self.capabilities['mejoras_ia']['local_models'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para modelos locales")
        return False

    def check_advanced_nlp(self):
        """Verifica el procesamiento de lenguaje natural avanzado."""
        try:
            import spacy
            import nltk
            if os.path.exists('models/nlp_models'):
                self.capabilities['mejoras_ia']['advanced_nlp'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para NLP avanzado")
        return False

    def check_natural_responses(self):
        """Verifica la generación de respuestas naturales."""
        try:
            import transformers
            if os.path.exists('models/response_models'):
                self.capabilities['mejoras_ia']['natural_responses'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para generación de respuestas")
        return False

    def check_reinforcement_learning(self):
        """Verifica el aprendizaje por refuerzo."""
        try:
            import gym
            import stable_baselines3
            if os.path.exists('models/rl_models'):
                self.capabilities['mejoras_ia']['reinforcement_learning'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para aprendizaje por refuerzo")
        return False

    def check_predictive_analysis(self):
        """Verifica el análisis predictivo."""
        try:
            import sklearn
            import pandas
            if os.path.exists('models/predictive_models'):
                self.capabilities['mejoras_ia']['predictive_analysis'] = True
                return True
        except ImportError:
            logger.warning("Faltan módulos para análisis predictivo")
        return False

    def run_analysis(self):
        """Ejecuta el análisis completo de capacidades."""
        logger.info("Iniciando análisis de capacidades...")

        # Verificar mejoras inmediatas
        self.check_swi_prolog()
        self.check_openai_api()
        self.check_voice_recognition()
        self.check_facial_encoding()

        # Verificar expansión de funcionalidades
        self.check_iot_integration()
        self.check_home_automation()
        self.check_calendar_integration()
        self.check_multilanguage()
        self.check_emotion_recognition()

        # Verificar mejoras de IA
        self.check_local_models()
        self.check_advanced_nlp()
        self.check_natural_responses()
        self.check_reinforcement_learning()
        self.check_predictive_analysis()

        return self.capabilities

    def generate_report(self):
        """Genera un reporte detallado del análisis."""
        capabilities = self.run_analysis()
        
        print("\nAnálisis de Capacidades de Hamilton")
        print("=" * 50)

        print("\n1. Mejoras Inmediatas (Prioridad Alta):")
        print("-" * 40)
        for feature, status in capabilities['mejoras_inmediatas'].items():
            print(f"- {feature.replace('_', ' ').title()}: {'✅' if status else '❌'}")

        print("\n2. Expansión de Funcionalidades (Prioridad Media):")
        print("-" * 40)
        for feature, status in capabilities['expansion_funcionalidades'].items():
            print(f"- {feature.replace('_', ' ').title()}: {'✅' if status else '❌'}")

        print("\n3. Mejoras de IA (Prioridad Media):")
        print("-" * 40)
        for feature, status in capabilities['mejoras_ia'].items():
            print(f"- {feature.replace('_', ' ').title()}: {'✅' if status else '❌'}")

        # Guardar reporte
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(self.root_dir, 'analysis', f'capabilities_{timestamp}.json')
        
        with open(report_file, 'w') as f:
            json.dump(capabilities, f, indent=2)
            
        logger.info(f"Reporte guardado en {report_file}")

def main():
    """Función principal."""
    try:
        analyzer = CapabilityAnalyzer()
        analyzer.generate_report()
    except Exception as e:
        logger.error(f"Error durante el análisis: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 