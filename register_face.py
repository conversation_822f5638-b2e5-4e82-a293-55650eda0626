#!/usr/bin/env python3
"""
Hamilton AI Assistant - Face Registration
Script para registrar la cara del señor Ibero
"""

import sys
import logging
from pathlib import Path

# Agregar el directorio actual al path
sys.path.append(str(Path(__file__).parent))

from recognition.face_recognition import hamilton_face
from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Función principal para registro facial"""
    print("="*60)
    print("🤖 HAMILTON - REGISTRO FACIAL")
    print("="*60)
    print(f"Registrando cara para: {settings.AUTHORIZED_USER}")
    print()
    
    while True:
        print("Opciones de registro:")
        print("1. Registrar desde cámara (recomendado)")
        print("2. Registrar desde archivo de imagen")
        print("3. Ver caras registradas")
        print("4. Salir")
        print()
        
        choice = input("Seleccione una opción (1-4): ").strip()
        
        if choice == "1":
            register_from_camera()
        elif choice == "2":
            register_from_file()
        elif choice == "3":
            show_registered_faces()
        elif choice == "4":
            print("Saliendo del registro facial...")
            break
        else:
            print("Opción inválida. Por favor, seleccione 1-4.")
        
        print("\n" + "-"*40 + "\n")

def register_from_camera():
    """Registra cara desde la cámara"""
    print("\n📷 REGISTRO DESDE CÁMARA")
    print("-" * 30)
    
    num_samples = input("Número de muestras a tomar (recomendado: 5): ").strip()
    try:
        num_samples = int(num_samples) if num_samples else 5
    except ValueError:
        num_samples = 5
    
    print(f"\nIniciando registro con {num_samples} muestras...")
    print("INSTRUCCIONES:")
    print("- Mire directamente a la cámara")
    print("- Mantenga buena iluminación")
    print("- Presione ESPACIO para capturar cada muestra")
    print("- Presione ESC para cancelar")
    print("\nPresione ENTER para continuar...")
    input()
    
    success = hamilton_face.register_user_from_camera(
        user_name=settings.AUTHORIZED_USER,
        num_samples=num_samples
    )
    
    if success:
        print("✅ Registro facial completado exitosamente!")
        print(f"Se registraron {num_samples} muestras para {settings.AUTHORIZED_USER}")
    else:
        print("❌ Error en el registro facial")

def register_from_file():
    """Registra cara desde archivo de imagen"""
    print("\n📁 REGISTRO DESDE ARCHIVO")
    print("-" * 30)
    
    image_path = input("Ruta del archivo de imagen: ").strip()
    
    if not image_path:
        print("❌ Debe proporcionar una ruta de imagen")
        return
    
    if not Path(image_path).exists():
        print(f"❌ El archivo no existe: {image_path}")
        return
    
    print(f"Registrando cara desde: {image_path}")
    
    success = hamilton_face.register_user_face(
        image_path=image_path,
        user_name=settings.AUTHORIZED_USER
    )
    
    if success:
        print("✅ Registro facial completado exitosamente!")
        print(f"Cara registrada para {settings.AUTHORIZED_USER}")
    else:
        print("❌ Error en el registro facial")
        print("Verifique que la imagen contenga una cara clara y bien iluminada")

def show_registered_faces():
    """Muestra información sobre caras registradas"""
    print("\n👥 CARAS REGISTRADAS")
    print("-" * 30)
    
    num_faces = len(hamilton_face.known_face_encodings)
    num_names = len(hamilton_face.known_face_names)
    
    print(f"Total de encodings faciales: {num_faces}")
    print(f"Total de nombres: {num_names}")
    
    if num_names > 0:
        print("\nUsuarios registrados:")
        unique_names = set(hamilton_face.known_face_names)
        for name in unique_names:
            count = hamilton_face.known_face_names.count(name)
            print(f"  - {name}: {count} muestra(s)")
    else:
        print("No hay caras registradas")
    
    if num_faces > 0:
        print(f"\nArchivo de encodings: {hamilton_face.encodings_file}")
        
        # Opción para probar autenticación
        test_auth = input("\n¿Desea probar la autenticación? (s/n): ").strip().lower()
        if test_auth in ['s', 'si', 'sí', 'y', 'yes']:
            test_authentication()

def test_authentication():
    """Prueba la autenticación facial"""
    print("\n🔍 PRUEBA DE AUTENTICACIÓN")
    print("-" * 30)
    print("Preparando cámara para autenticación...")
    print("Mire a la cámara cuando esté listo")
    print("Presione ENTER para continuar...")
    input()
    
    success, user_name, confidence = hamilton_face.authenticate_user()
    
    print(f"\nResultado de autenticación:")
    print(f"  Éxito: {'✅ Sí' if success else '❌ No'}")
    print(f"  Usuario: {user_name}")
    print(f"  Confianza: {confidence:.2f}")
    
    if success and user_name == settings.AUTHORIZED_USER:
        print(f"\n🎉 ¡Autenticación exitosa para {settings.AUTHORIZED_USER}!")
    elif success:
        print(f"\n⚠️ Usuario reconocido pero no autorizado: {user_name}")
    else:
        print(f"\n❌ Fallo en autenticación: {user_name}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nRegistro cancelado por el usuario")
    except Exception as e:
        logger.error(f"Error en registro facial: {e}")
        print(f"❌ Error inesperado: {e}")
    finally:
        print("\nCerrando registro facial...")
