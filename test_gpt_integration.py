"""
Hamilton AI Assistant - GPT Integration Test
Prueba directa de la integración con OpenAI GPT
"""

import os
from ai.gpt_engine import HamiltonGPTEngine

def test_gpt_integration():
    """Prueba la integración con GPT"""
    # Configurar API key
    os.environ['OPENAI_API_KEY'] = 'sk-705e1a2e1a3241bbbf8f1c29a64e8b93'
    
    # Crear instancia de GPT
    gpt = HamiltonGPTEngine()
    
    # Probar consulta simple
    print("\nProbando consulta simple...")
    response = gpt.process_query("¿Cuál es la capital de Francia?")
    print(f"Respuesta GPT: {response}")
    
    # Probar consulta con contexto
    print("\nProbando consulta con contexto...")
    context = {
        "user_id": "test_user",
        "timestamp": "2024-03-20T12:00:00",
        "session_id": "test_session"
    }
    response = gpt.process_query("¿Qué tiempo hace en París?", context)
    print(f"Respuesta GPT con contexto: {response}")
    
    # Probar historial de conversación
    print("\nProbando historial de conversación...")
    gpt._update_conversation_history("Hola", "¡Hola! ¿En qué puedo ayudarte?")
    print(f"Historial de conversación: {gpt.conversation_history}")
    
    # Probar consulta con historial
    print("\nProbando consulta con historial...")
    response = gpt.process_query("¿Cómo estás?")
    print(f"Respuesta GPT con historial: {response}")

if __name__ == "__main__":
    test_gpt_integration() 