"""
Hamilton AI Assistant - Local AI Models Integration
Integración con modelos de IA locales (Llama, Mistral, etc.)
"""

import logging
import json
import asyncio
import subprocess
import requests
from typing import Dict, List, Any, Optional, Generator
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import threading
import time

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class ModelType(Enum):
    """Tipos de modelos soportados"""
    LLAMA2_7B = "llama2_7b"
    LLAMA2_13B = "llama2_13b"
    MISTRAL_7B = "mistral_7b"
    CODELLAMA_7B = "codellama_7b"
    WHISPER_BASE = "whisper_base"
    WHISPER_LARGE = "whisper_large"

@dataclass
class ModelConfig:
    """Configuración de modelo"""
    name: str
    model_type: ModelType
    model_path: str
    context_length: int
    temperature: float
    max_tokens: int
    system_prompt: str
    enabled: bool = True
    gpu_layers: int = 0
    memory_usage_mb: int = 4096

class LocalModelManager:
    """Gestor de modelos de IA locales"""
    
    def __init__(self):
        self.models: Dict[str, ModelConfig] = {}
        self.active_models: Dict[str, Any] = {}
        self.ollama_running = False
        self.config_file = Path("config/local_models_config.json")
        self.models_dir = Path("models")
        
        self.config = {}  # Inicializar config antes de cargar
        self.load_configuration()
        self._setup_default_models()
    
    def load_configuration(self):
        """Carga configuración de modelos locales"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # self._load_models_from_config(config)  # Comentado temporalmente
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de modelos locales: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.config = {
            "ollama": {
                "enabled": True,
                "host": "localhost",
                "port": 11434,
                "auto_start": True,
                "models_to_pull": ["llama2:7b", "mistral:7b", "codellama:7b"]
            },
            "llamacpp": {
                "enabled": True,
                "executable_path": "",
                "default_threads": 4,
                "use_gpu": False
            },
            "whisper": {
                "enabled": True,
                "model_size": "base",
                "language": "es",
                "device": "cpu"
            },
            "models": {
                "hamilton_chat": {
                    "type": "llama2_7b",
                    "context_length": 4096,
                    "temperature": 0.7,
                    "max_tokens": 512,
                    "system_prompt": "Eres Hamilton, un asistente personal de IA masculino, formal pero amigable, diseñado específicamente para ayudar al señor Ibero. Respondes de manera precisa, útil y con un tono profesional pero cálido en español.",
                    "enabled": True
                },
                "hamilton_code": {
                    "type": "codellama_7b",
                    "context_length": 2048,
                    "temperature": 0.3,
                    "max_tokens": 1024,
                    "system_prompt": "Eres Hamilton, un asistente de programación experto. Ayudas con código, debugging y explicaciones técnicas de manera clara y precisa.",
                    "enabled": False
                },
                "hamilton_reasoning": {
                    "type": "mistral_7b",
                    "context_length": 8192,
                    "temperature": 0.5,
                    "max_tokens": 800,
                    "system_prompt": "Eres Hamilton, especializado en razonamiento lógico y análisis. Proporcionas explicaciones detalladas y análisis paso a paso.",
                    "enabled": False
                }
            },
            "performance": {
                "max_concurrent_requests": 2,
                "request_timeout_seconds": 30,
                "memory_limit_mb": 8192,
                "auto_unload_inactive_minutes": 15
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def _setup_default_models(self):
        """Configura modelos por defecto"""
        models_config = self.config.get("models", {})
        
        for model_name, model_data in models_config.items():
            model_config = ModelConfig(
                name=model_name,
                model_type=ModelType(model_data["type"]),
                model_path="",  # Se determina dinámicamente
                context_length=model_data["context_length"],
                temperature=model_data["temperature"],
                max_tokens=model_data["max_tokens"],
                system_prompt=model_data["system_prompt"],
                enabled=model_data["enabled"]
            )
            self.models[model_name] = model_config
    
    async def initialize_models(self) -> bool:
        """Inicializa modelos locales"""
        logger.info("🚀 Inicializando modelos de IA locales...")
        
        success = True
        
        # Verificar e iniciar Ollama si está habilitado
        if self.config["ollama"]["enabled"]:
            ollama_success = await self._setup_ollama()
            if not ollama_success:
                logger.warning("⚠️ Ollama no disponible, continuando sin él")
                success = False
        
        # Verificar Whisper para transcripción local
        if self.config["whisper"]["enabled"]:
            whisper_success = await self._setup_whisper()
            if not whisper_success:
                logger.warning("⚠️ Whisper no disponible, usando transcripción en línea")
        
        # Cargar modelos habilitados
        for model_name, model_config in self.models.items():
            if model_config.enabled:
                try:
                    await self._load_model(model_name)
                    logger.info(f"✅ Modelo {model_name} cargado")
                except Exception as e:
                    logger.error(f"❌ Error cargando modelo {model_name}: {e}")
                    success = False
        
        if success:
            logger.info("🎉 Modelos locales inicializados exitosamente")
        else:
            logger.warning("⚠️ Algunos modelos no se pudieron inicializar")
        
        return success
    
    async def _setup_ollama(self) -> bool:
        """Configura Ollama"""
        try:
            # Verificar si Ollama está instalado
            result = subprocess.run(["ollama", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode != 0:
                logger.error("Ollama no está instalado")
                return False
            
            # Verificar si Ollama está corriendo
            ollama_host = self.config["ollama"]["host"]
            ollama_port = self.config["ollama"]["port"]
            
            try:
                response = requests.get(f"http://{ollama_host}:{ollama_port}/api/tags", timeout=5)
                if response.status_code == 200:
                    self.ollama_running = True
                    logger.info("✅ Ollama está corriendo")
                else:
                    raise requests.RequestException("Ollama no responde")
            except requests.RequestException:
                # Intentar iniciar Ollama
                if self.config["ollama"]["auto_start"]:
                    logger.info("🔄 Iniciando Ollama...")
                    subprocess.Popen(["ollama", "serve"], 
                                   stdout=subprocess.DEVNULL, 
                                   stderr=subprocess.DEVNULL)
                    
                    # Esperar a que inicie
                    for _ in range(10):
                        await asyncio.sleep(1)
                        try:
                            response = requests.get(f"http://{ollama_host}:{ollama_port}/api/tags", timeout=2)
                            if response.status_code == 200:
                                self.ollama_running = True
                                logger.info("✅ Ollama iniciado exitosamente")
                                break
                        except:
                            continue
            
            # Verificar modelos disponibles
            if self.ollama_running:
                await self._check_ollama_models()
            
            return self.ollama_running
            
        except Exception as e:
            logger.error(f"Error configurando Ollama: {e}")
            return False
    
    async def _check_ollama_models(self):
        """Verifica modelos disponibles en Ollama"""
        try:
            ollama_host = self.config["ollama"]["host"]
            ollama_port = self.config["ollama"]["port"]
            
            response = requests.get(f"http://{ollama_host}:{ollama_port}/api/tags")
            if response.status_code == 200:
                data = response.json()
                available_models = [model["name"] for model in data.get("models", [])]
                
                logger.info(f"Modelos Ollama disponibles: {available_models}")
                
                # Descargar modelos faltantes
                models_to_pull = self.config["ollama"]["models_to_pull"]
                for model_name in models_to_pull:
                    if model_name not in available_models:
                        logger.info(f"📥 Descargando modelo {model_name}...")
                        await self._pull_ollama_model(model_name)
        
        except Exception as e:
            logger.error(f"Error verificando modelos Ollama: {e}")
    
    async def _pull_ollama_model(self, model_name: str):
        """Descarga modelo de Ollama"""
        try:
            process = subprocess.Popen(
                ["ollama", "pull", model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Monitorear progreso
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    logger.info(f"Ollama: {output.strip()}")
            
            if process.returncode == 0:
                logger.info(f"✅ Modelo {model_name} descargado exitosamente")
            else:
                logger.error(f"❌ Error descargando modelo {model_name}")
                
        except Exception as e:
            logger.error(f"Error descargando modelo {model_name}: {e}")
    
    async def _setup_whisper(self) -> bool:
        """Configura Whisper para transcripción local"""
        try:
            # Verificar si OpenAI Whisper está instalado
            import whisper
            
            model_size = self.config["whisper"]["model_size"]
            logger.info(f"🎤 Cargando Whisper modelo {model_size}...")
            
            # Cargar modelo Whisper
            whisper_model = whisper.load_model(model_size)
            self.active_models["whisper"] = whisper_model
            
            logger.info("✅ Whisper cargado exitosamente")
            return True
            
        except ImportError:
            logger.warning("Whisper no está instalado. Instalar con: pip install openai-whisper")
            return False
        except Exception as e:
            logger.error(f"Error configurando Whisper: {e}")
            return False
    
    async def _load_model(self, model_name: str):
        """Carga un modelo específico"""
        model_config = self.models.get(model_name)
        if not model_config:
            raise ValueError(f"Modelo no encontrado: {model_name}")
        
        if model_config.model_type in [ModelType.LLAMA2_7B, ModelType.LLAMA2_13B, 
                                      ModelType.MISTRAL_7B, ModelType.CODELLAMA_7B]:
            # Usar Ollama para estos modelos
            if self.ollama_running:
                self.active_models[model_name] = OllamaModel(model_config, self.config["ollama"])
            else:
                raise RuntimeError("Ollama no está disponible")
        
        logger.info(f"Modelo {model_name} cargado en memoria")
    
    async def generate_response(self, model_name: str, prompt: str, 
                              context: List[Dict[str, str]] = None) -> str:
        """Genera respuesta usando modelo local"""
        if model_name not in self.active_models:
            await self._load_model(model_name)
        
        model = self.active_models[model_name]
        model_config = self.models[model_name]
        
        try:
            # Construir prompt completo
            full_prompt = self._build_prompt(prompt, context, model_config.system_prompt)
            
            # Generar respuesta
            response = await model.generate(
                prompt=full_prompt,
                max_tokens=model_config.max_tokens,
                temperature=model_config.temperature
            )
            
            logger.info(f"Respuesta generada por {model_name}: {len(response)} caracteres")
            return response
            
        except Exception as e:
            logger.error(f"Error generando respuesta con {model_name}: {e}")
            return f"Error generando respuesta: {str(e)}"
    
    def _build_prompt(self, user_prompt: str, context: List[Dict[str, str]], 
                     system_prompt: str) -> str:
        """Construye prompt completo con contexto"""
        parts = [system_prompt]
        
        if context:
            parts.append("\nContexto de conversación:")
            for msg in context[-5:]:  # Últimos 5 mensajes
                role = msg.get("role", "user")
                content = msg.get("content", "")
                parts.append(f"{role}: {content}")
        
        parts.append(f"\nUsuario: {user_prompt}")
        parts.append("Hamilton:")
        
        return "\n".join(parts)
    
    async def transcribe_audio(self, audio_file_path: str) -> str:
        """Transcribe audio usando Whisper local"""
        if "whisper" not in self.active_models:
            raise RuntimeError("Whisper no está disponible")
        
        try:
            whisper_model = self.active_models["whisper"]
            result = whisper_model.transcribe(audio_file_path)
            
            transcription = result["text"].strip()
            logger.info(f"Audio transcrito: {len(transcription)} caracteres")
            
            return transcription
            
        except Exception as e:
            logger.error(f"Error transcribiendo audio: {e}")
            return ""
    
    def get_model_status(self) -> Dict[str, Any]:
        """Obtiene estado de los modelos"""
        status = {
            "ollama_running": self.ollama_running,
            "active_models": list(self.active_models.keys()),
            "available_models": list(self.models.keys()),
            "memory_usage": self._get_memory_usage()
        }
        
        return status
    
    def _get_memory_usage(self) -> Dict[str, int]:
        """Obtiene uso de memoria de los modelos"""
        # Implementación simplificada
        memory_usage = {}
        for model_name in self.active_models:
            # Estimación básica basada en tipo de modelo
            model_config = self.models.get(model_name)
            if model_config:
                if "7b" in model_config.model_type.value:
                    memory_usage[model_name] = 4096  # MB
                elif "13b" in model_config.model_type.value:
                    memory_usage[model_name] = 8192  # MB
                else:
                    memory_usage[model_name] = 2048  # MB
        
        return memory_usage

class OllamaModel:
    """Wrapper para modelos de Ollama"""
    
    def __init__(self, model_config: ModelConfig, ollama_config: Dict):
        self.model_config = model_config
        self.ollama_config = ollama_config
        self.base_url = f"http://{ollama_config['host']}:{ollama_config['port']}"
    
    async def generate(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Genera respuesta usando Ollama"""
        try:
            # Mapear tipo de modelo a nombre Ollama
            model_map = {
                ModelType.LLAMA2_7B: "llama2:7b",
                ModelType.LLAMA2_13B: "llama2:13b",
                ModelType.MISTRAL_7B: "mistral:7b",
                ModelType.CODELLAMA_7B: "codellama:7b"
            }
            
            ollama_model = model_map.get(self.model_config.model_type, "llama2:7b")
            
            payload = {
                "model": ollama_model,
                "prompt": prompt,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                },
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("response", "")
            else:
                raise RuntimeError(f"Error Ollama: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error en OllamaModel.generate: {e}")
            raise

# Instancia global
hamilton_local_models = LocalModelManager()
