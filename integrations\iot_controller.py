"""
Hamilton AI Assistant - IoT Controller
Sistema de control de dispositivos IoT del hogar inteligente
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, time
from pathlib import Path
import requests
from dataclasses import dataclass

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

@dataclass
class IoTDevice:
    """Representación de un dispositivo IoT"""
    id: str
    name: str
    type: str  # light, thermostat, switch, sensor, camera, lock
    brand: str  # philips_hue, lifx, nest, tplink, etc.
    status: str  # online, offline, unknown
    capabilities: List[str]  # brightness, color, temperature, etc.
    current_state: Dict[str, Any]
    room: str
    last_updated: datetime

class IoTController:
    """Controlador principal de dispositivos IoT"""
    
    def __init__(self):
        self.devices: Dict[str, IoTDevice] = {}
        self.device_handlers = {
            'philips_hue': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
            'lifx': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
            'tplink': <PERSON><PERSON>ink<PERSON><PERSON><PERSON>(),
            'nest': <PERSON>estHandler(),
            'generic_zigbee': <PERSON>igbee<PERSON>andler(),
            'generic_wifi': WiFiHandler()
        }
        self.config_file = Path("config/iot_config.json")
        self.load_configuration()
    
    def load_configuration(self):
        """Carga configuración de dispositivos IoT"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # self._load_devices_from_config(config)  # Comentado temporalmente
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración IoT: {e}")
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        default_config = {
            "discovery": {
                "auto_discovery": True,
                "scan_interval_minutes": 30,
                "protocols": ["upnp", "mdns", "zigbee", "wifi"]
            },
            "hubs": {
                "philips_hue": {
                    "enabled": False,
                    "bridge_ip": "",
                    "username": ""
                },
                "lifx": {
                    "enabled": False,
                    "api_token": ""
                },
                "nest": {
                    "enabled": False,
                    "client_id": "",
                    "client_secret": ""
                }
            },
            "rooms": [
                "sala", "cocina", "dormitorio", "baño", "estudio", "garage"
            ],
            "automation_rules": []
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
    
    async def discover_devices(self) -> List[IoTDevice]:
        """Descubre dispositivos IoT en la red"""
        logger.info("🔍 Iniciando descubrimiento de dispositivos IoT...")
        
        discovered_devices = []
        
        # Descubrimiento paralelo por protocolo
        discovery_tasks = []
        for handler_name, handler in self.device_handlers.items():
            if hasattr(handler, 'discover'):
                task = asyncio.create_task(handler.discover())
                discovery_tasks.append((handler_name, task))
        
        # Esperar resultados
        for handler_name, task in discovery_tasks:
            try:
                devices = await task
                discovered_devices.extend(devices)
                logger.info(f"✅ {handler_name}: {len(devices)} dispositivos encontrados")
            except Exception as e:
                logger.warning(f"⚠️ Error en {handler_name}: {e}")
        
        # Actualizar registro de dispositivos
        for device in discovered_devices:
            self.devices[device.id] = device
        
        logger.info(f"🎉 Total: {len(discovered_devices)} dispositivos descubiertos")
        return discovered_devices
    
    async def control_device(self, device_id: str, command: str, parameters: Dict = None) -> bool:
        """Controla un dispositivo específico"""
        if device_id not in self.devices:
            logger.error(f"Dispositivo no encontrado: {device_id}")
            return False
        
        device = self.devices[device_id]
        handler = self.device_handlers.get(device.brand)
        
        if not handler:
            logger.error(f"Handler no disponible para {device.brand}")
            return False
        
        try:
            success = await handler.control_device(device, command, parameters or {})
            if success:
                device.last_updated = datetime.now()
                logger.info(f"✅ Comando ejecutado: {device.name} - {command}")
            return success
        except Exception as e:
            logger.error(f"Error controlando dispositivo {device.name}: {e}")
            return False
    
    async def control_room(self, room: str, command: str, parameters: Dict = None) -> Dict[str, bool]:
        """Controla todos los dispositivos de una habitación"""
        room_devices = [d for d in self.devices.values() if d.room.lower() == room.lower()]
        
        if not room_devices:
            logger.warning(f"No hay dispositivos en la habitación: {room}")
            return {}
        
        results = {}
        control_tasks = []
        
        for device in room_devices:
            task = asyncio.create_task(
                self.control_device(device.id, command, parameters)
            )
            control_tasks.append((device.id, task))
        
        for device_id, task in control_tasks:
            try:
                results[device_id] = await task
            except Exception as e:
                logger.error(f"Error controlando {device_id}: {e}")
                results[device_id] = False
        
        return results
    
    def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene el estado actual de un dispositivo"""
        if device_id not in self.devices:
            return None
        
        device = self.devices[device_id]
        return {
            'id': device.id,
            'name': device.name,
            'type': device.type,
            'status': device.status,
            'state': device.current_state,
            'room': device.room,
            'last_updated': device.last_updated.isoformat()
        }
    
    def get_room_status(self, room: str) -> Dict[str, Any]:
        """Obtiene el estado de todos los dispositivos de una habitación"""
        room_devices = [d for d in self.devices.values() if d.room.lower() == room.lower()]
        
        return {
            'room': room,
            'device_count': len(room_devices),
            'devices': [self.get_device_status(d.id) for d in room_devices],
            'summary': self._get_room_summary(room_devices)
        }
    
    def _get_room_summary(self, devices: List[IoTDevice]) -> Dict[str, Any]:
        """Genera resumen del estado de la habitación"""
        summary = {
            'lights_on': 0,
            'total_lights': 0,
            'temperature': None,
            'security_status': 'unknown'
        }
        
        for device in devices:
            if device.type == 'light':
                summary['total_lights'] += 1
                if device.current_state.get('power', False):
                    summary['lights_on'] += 1
            elif device.type == 'thermostat':
                summary['temperature'] = device.current_state.get('temperature')
            elif device.type == 'sensor' and 'security' in device.capabilities:
                summary['security_status'] = device.current_state.get('status', 'unknown')
        
        return summary

class PhilipsHueHandler:
    """Handler REAL para dispositivos Philips Hue"""

    def __init__(self):
        self.bridge_ip = None
        self.username = None
        self.load_config()

    def load_config(self):
        """Carga configuración real de Hue"""
        try:
            config_file = Path("config/iot_real_devices.json")
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    hue_config = config.get("integrations", {}).get("philips_hue", {})
                    self.bridge_ip = hue_config.get("bridge_ip")
                    self.username = hue_config.get("username")
        except Exception as e:
            logger.warning(f"No se pudo cargar configuración Hue: {e}")

    async def discover(self) -> List[IoTDevice]:
        """Descubre dispositivos Philips Hue REALES"""
        devices = []

        # Si no hay configuración, usar dispositivos simulados para demo
        if not self.bridge_ip or not self.username:
            logger.info("Usando dispositivos Hue simulados (configurar dispositivos reales en setup_iot_devices.py)")
            return await self._get_simulated_devices()

        try:
            # Descubrimiento REAL de dispositivos Hue
            logger.info(f"Conectando a Philips Hue Bridge: {self.bridge_ip}")

            # Obtener luces reales
            url = f"http://{self.bridge_ip}/api/{self.username}/lights"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                lights_data = response.json()

                for light_id, light_info in lights_data.items():
                    # Obtener estado actual
                    state = light_info.get('state', {})

                    device = IoTDevice(
                        id=f"hue_real_{light_id}",
                        name=light_info.get('name', f'Hue Light {light_id}'),
                        type='light',
                        brand='philips_hue',
                        status='online' if state.get('reachable', False) else 'offline',
                        capabilities=['brightness', 'color', 'power'],
                        current_state={
                            'power': state.get('on', False),
                            'brightness': int(state.get('bri', 0) / 254 * 100),
                            'color': self._xy_to_hex(state.get('xy', [0.3, 0.3]))
                        },
                        room=self._determine_room(light_info.get('name', '')),
                        last_updated=datetime.now()
                    )
                    devices.append(device)

                logger.info(f"✅ {len(devices)} dispositivos Hue reales encontrados")
            else:
                logger.error(f"Error conectando a Hue Bridge: {response.status_code}")
                return await self._get_simulated_devices()

        except Exception as e:
            logger.error(f"Error descubriendo Philips Hue real: {e}")
            return await self._get_simulated_devices()

        return devices

    async def _get_simulated_devices(self) -> List[IoTDevice]:
        """Dispositivos simulados para demo"""
        simulated_devices = [
            {
                'id': 'hue_sim_1',
                'name': 'Luz Principal Sala (Simulada)',
                'type': 'light',
                'capabilities': ['brightness', 'color', 'power'],
                'room': 'sala',
                'state': {'power': False, 'brightness': 100, 'color': '#FFFFFF'}
            },
            {
                'id': 'hue_sim_2',
                'name': 'Luz Ambiente Dormitorio (Simulada)',
                'type': 'light',
                'capabilities': ['brightness', 'color', 'power'],
                'room': 'dormitorio',
                'state': {'power': False, 'brightness': 50, 'color': '#FF8000'}
            }
        ]

        devices = []
        for device_data in simulated_devices:
            device = IoTDevice(
                id=device_data['id'],
                name=device_data['name'],
                type=device_data['type'],
                brand='philips_hue',
                status='online',
                capabilities=device_data['capabilities'],
                current_state=device_data['state'],
                room=device_data['room'],
                last_updated=datetime.now()
            )
            devices.append(device)

        return devices

    def _xy_to_hex(self, xy_color):
        """Convierte color XY de Hue a HEX"""
        try:
            # Conversión simplificada XY a RGB
            x, y = xy_color
            z = 1.0 - x - y

            # Convertir a RGB
            r = x * 255
            g = y * 255
            b = z * 255

            return f"#{int(r):02x}{int(g):02x}{int(b):02x}"
        except:
            return "#FFFFFF"

    def _determine_room(self, light_name):
        """Determina habitación basada en nombre"""
        name_lower = light_name.lower()
        if 'sala' in name_lower or 'living' in name_lower:
            return 'sala'
        elif 'dormitorio' in name_lower or 'bedroom' in name_lower:
            return 'dormitorio'
        elif 'cocina' in name_lower or 'kitchen' in name_lower:
            return 'cocina'
        elif 'baño' in name_lower or 'bathroom' in name_lower:
            return 'baño'
        else:
            return 'general'
    
    async def control_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo Philips Hue REAL"""
        try:
            # Si es dispositivo real, usar API de Hue
            if device.id.startswith('hue_real_') and self.bridge_ip and self.username:
                light_id = device.id.replace('hue_real_', '')
                return await self._control_real_hue_device(light_id, command, parameters, device)

            # Si es simulado, actualizar estado local
            return await self._control_simulated_device(device, command, parameters)

        except Exception as e:
            logger.error(f"Error controlando Philips Hue {device.name}: {e}")
            return False

    async def _control_real_hue_device(self, light_id: str, command: str, parameters: Dict, device: IoTDevice) -> bool:
        """Controla dispositivo Hue REAL usando API"""
        try:
            url = f"http://{self.bridge_ip}/api/{self.username}/lights/{light_id}/state"

            # Preparar comando para API de Hue
            hue_command = {}

            if command == 'turn_on':
                hue_command['on'] = True
                if 'brightness' in parameters:
                    # Convertir 0-100 a 0-254 (rango de Hue)
                    hue_command['bri'] = int(parameters['brightness'] * 254 / 100)
                if 'color' in parameters:
                    # Convertir HEX a XY (simplificado)
                    hue_command['xy'] = self._hex_to_xy(parameters['color'])

            elif command == 'turn_off':
                hue_command['on'] = False

            elif command == 'set_brightness':
                brightness = parameters.get('value', 100)
                hue_command['bri'] = int(brightness * 254 / 100)

            elif command == 'set_color':
                color = parameters.get('value', '#FFFFFF')
                hue_command['xy'] = self._hex_to_xy(color)

            # Enviar comando a dispositivo real
            response = requests.put(url, json=hue_command, timeout=5)

            if response.status_code == 200:
                result = response.json()
                if any('success' in item for item in result):
                    # Actualizar estado local
                    await self._update_device_state(device, command, parameters)
                    logger.info(f"✅ Hue REAL: {device.name} - {command} ejecutado")
                    return True
                else:
                    logger.error(f"❌ Error en respuesta Hue: {result}")
                    return False
            else:
                logger.error(f"❌ Error HTTP controlando Hue: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Error controlando Hue real: {e}")
            return False

    async def _control_simulated_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo simulado"""
        try:
            await self._update_device_state(device, command, parameters)
            logger.info(f"✅ Hue SIMULADO: {device.name} - {command} ejecutado")
            return True
        except Exception as e:
            logger.error(f"❌ Error controlando Hue simulado: {e}")
            return False

    async def _update_device_state(self, device: IoTDevice, command: str, parameters: Dict):
        """Actualiza estado del dispositivo"""
        if command == 'turn_on':
            device.current_state['power'] = True
            if 'brightness' in parameters:
                device.current_state['brightness'] = parameters['brightness']
            if 'color' in parameters:
                device.current_state['color'] = parameters['color']

        elif command == 'turn_off':
            device.current_state['power'] = False

        elif command == 'set_brightness':
            device.current_state['brightness'] = parameters.get('value', 100)

        elif command == 'set_color':
            device.current_state['color'] = parameters.get('value', '#FFFFFF')

        device.last_updated = datetime.now()

    def _hex_to_xy(self, hex_color: str):
        """Convierte color HEX a XY para Hue"""
        try:
            # Remover # si existe
            hex_color = hex_color.lstrip('#')

            # Convertir a RGB
            r = int(hex_color[0:2], 16) / 255.0
            g = int(hex_color[2:4], 16) / 255.0
            b = int(hex_color[4:6], 16) / 255.0

            # Conversión simplificada RGB a XY
            # (En implementación real usar matriz de conversión completa)
            x = r * 0.4124 + g * 0.3576 + b * 0.1805
            y = r * 0.2126 + g * 0.7152 + b * 0.0722

            # Normalizar
            total = x + y + (r * 0.0193 + g * 0.1192 + b * 0.9505)
            if total > 0:
                x = x / total
                y = y / total

            return [round(x, 4), round(y, 4)]

        except Exception:
            return [0.3, 0.3]  # Blanco por defecto

class LIFXHandler:
    """Handler para dispositivos LIFX"""
    
    async def discover(self) -> List[IoTDevice]:
        """Descubre dispositivos LIFX"""
        devices = []
        try:
            logger.info("Buscando dispositivos LIFX...")
            
            # Simulación de dispositivos LIFX
            lifx_devices = [
                {
                    'id': 'lifx_bulb_1',
                    'name': 'LIFX Cocina',
                    'type': 'light',
                    'capabilities': ['brightness', 'color', 'power', 'effects'],
                    'room': 'cocina',
                    'state': {'power': False, 'brightness': 75, 'color': '#00FF00'}
                }
            ]
            
            for device_data in lifx_devices:
                device = IoTDevice(
                    id=device_data['id'],
                    name=device_data['name'],
                    type=device_data['type'],
                    brand='lifx',
                    status='online',
                    capabilities=device_data['capabilities'],
                    current_state=device_data['state'],
                    room=device_data['room'],
                    last_updated=datetime.now()
                )
                devices.append(device)
                
        except Exception as e:
            logger.error(f"Error descubriendo LIFX: {e}")
        
        return devices
    
    async def control_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo LIFX"""
        try:
            # Implementación similar a Philips Hue
            if command == 'turn_on':
                device.current_state['power'] = True
            elif command == 'turn_off':
                device.current_state['power'] = False
            elif command == 'set_brightness':
                device.current_state['brightness'] = parameters.get('value', 100)
            elif command == 'set_color':
                device.current_state['color'] = parameters.get('value', '#FFFFFF')
            
            logger.info(f"LIFX: {device.name} - {command} ejecutado")
            return True
            
        except Exception as e:
            logger.error(f"Error controlando LIFX {device.name}: {e}")
            return False

class TPLinkHandler:
    """Handler para dispositivos TP-Link"""
    
    async def discover(self) -> List[IoTDevice]:
        """Descubre dispositivos TP-Link"""
        devices = []
        try:
            logger.info("Buscando dispositivos TP-Link...")
            
            # Simulación de dispositivos TP-Link
            tplink_devices = [
                {
                    'id': 'tplink_switch_1',
                    'name': 'Enchufe Inteligente Estudio',
                    'type': 'switch',
                    'capabilities': ['power', 'scheduling', 'energy_monitoring'],
                    'room': 'estudio',
                    'state': {'power': False, 'energy_usage': 0}
                }
            ]
            
            for device_data in tplink_devices:
                device = IoTDevice(
                    id=device_data['id'],
                    name=device_data['name'],
                    type=device_data['type'],
                    brand='tplink',
                    status='online',
                    capabilities=device_data['capabilities'],
                    current_state=device_data['state'],
                    room=device_data['room'],
                    last_updated=datetime.now()
                )
                devices.append(device)
                
        except Exception as e:
            logger.error(f"Error descubriendo TP-Link: {e}")
        
        return devices
    
    async def control_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo TP-Link"""
        try:
            if command == 'turn_on':
                device.current_state['power'] = True
            elif command == 'turn_off':
                device.current_state['power'] = False
            
            logger.info(f"TP-Link: {device.name} - {command} ejecutado")
            return True
            
        except Exception as e:
            logger.error(f"Error controlando TP-Link {device.name}: {e}")
            return False

class NestHandler:
    """Handler para dispositivos Nest (termostatos)"""
    
    async def discover(self) -> List[IoTDevice]:
        """Descubre dispositivos Nest"""
        devices = []
        try:
            logger.info("Buscando dispositivos Nest...")
            
            # Simulación de termostato Nest
            nest_devices = [
                {
                    'id': 'nest_thermostat_1',
                    'name': 'Termostato Principal',
                    'type': 'thermostat',
                    'capabilities': ['temperature', 'humidity', 'scheduling', 'eco_mode'],
                    'room': 'sala',
                    'state': {
                        'temperature': 22.5,
                        'target_temperature': 23.0,
                        'humidity': 45,
                        'mode': 'heat',
                        'eco_mode': False
                    }
                }
            ]
            
            for device_data in nest_devices:
                device = IoTDevice(
                    id=device_data['id'],
                    name=device_data['name'],
                    type=device_data['type'],
                    brand='nest',
                    status='online',
                    capabilities=device_data['capabilities'],
                    current_state=device_data['state'],
                    room=device_data['room'],
                    last_updated=datetime.now()
                )
                devices.append(device)
                
        except Exception as e:
            logger.error(f"Error descubriendo Nest: {e}")
        
        return devices
    
    async def control_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo Nest"""
        try:
            if command == 'set_temperature':
                device.current_state['target_temperature'] = parameters.get('value', 22.0)
            elif command == 'set_mode':
                device.current_state['mode'] = parameters.get('value', 'auto')
            elif command == 'eco_mode_on':
                device.current_state['eco_mode'] = True
            elif command == 'eco_mode_off':
                device.current_state['eco_mode'] = False
            
            logger.info(f"Nest: {device.name} - {command} ejecutado")
            return True
            
        except Exception as e:
            logger.error(f"Error controlando Nest {device.name}: {e}")
            return False

class ZigbeeHandler:
    """Handler genérico para dispositivos Zigbee"""
    
    async def discover(self) -> List[IoTDevice]:
        """Descubre dispositivos Zigbee genéricos"""
        return []  # Implementar según hub Zigbee específico
    
    async def control_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo Zigbee genérico"""
        return False  # Implementar según protocolo específico

class WiFiHandler:
    """Handler genérico para dispositivos WiFi"""
    
    async def discover(self) -> List[IoTDevice]:
        """Descubre dispositivos WiFi genéricos"""
        return []  # Implementar descubrimiento UPnP/mDNS
    
    async def control_device(self, device: IoTDevice, command: str, parameters: Dict) -> bool:
        """Controla dispositivo WiFi genérico"""
        return False  # Implementar según protocolo específico

# Instancia global
hamilton_iot = IoTController()
