#!/usr/bin/env python3
"""
Hamilton AI Assistant - Startup Script
Script de inicio principal para Hamilton
"""

import sys
import asyncio
import logging
from pathlib import Path

# Agregar directorio actual al path
sys.path.insert(0, str(Path(__file__).parent))

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)

async def main():
    """Función principal de inicio"""
    print("🤖 HAMILTON AI ASSISTANT")
    print("=" * 40)
    print("Iniciando sistema completo...")
    
    try:
        # Importar núcleo principal
        from core.hamilton_core import hamilton
        
        # Inicializar <PERSON>
        print("🚀 Inicializando Hamilton...")
        hamilton.initialize()
        
        # Activar Hamilton
        print("⚡ Activando Hamilton...")
        hamilton.activate()
        
        print("✅ Hamilton está listo y operativo!")
        print("💬 Puede comenzar a interactuar con Hamilton.")
        print("🛑 Presione Ctrl+C para detener el sistema.")
        
        # Mantener el sistema activo
        while hamilton.is_active:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 Deteniendo Hamilton...")
        if 'hamilton' in locals():
            hamilton.shutdown()
        print("✅ Hamilton detenido correctamente.")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
