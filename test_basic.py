#!/usr/bin/env python3
"""
Hamilton AI Assistant - Basic Test
Prueba básica para verificar importaciones y funcionalidad mínima
"""

import sys
import os
from pathlib import Path

print("🧪 HAMILTON - PRUEBA BÁSICA")
print("="*50)

# Agregar directorio actual al path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print(f"📁 Directorio de trabajo: {current_dir}")
print(f"🐍 Python: {sys.version}")

# Verificar archivos principales
print("\n📋 Verificando archivos...")
required_files = [
    "config/settings.py",
    "logic/prolog_engine.py", 
    "core/hamilton_core.py",
    "recognition/voice_recognition.py",
    "recognition/face_recognition.py"
]

for file_path in required_files:
    if Path(file_path).exists():
        print(f"✅ {file_path}")
    else:
        print(f"❌ {file_path} - NO ENCONTRADO")

# Probar importaciones básicas
print("\n🔧 Probando importaciones...")

try:
    print("Importando configuración...")
    from config.settings import settings
    print(f"✅ Settings: {settings.PROJECT_NAME}")
except Exception as e:
    print(f"❌ Error en settings: {e}")
    sys.exit(1)

try:
    print("Importando motor de voz...")
    from recognition.voice_recognition import hamilton_voice
    print("✅ Motor de voz importado")
except Exception as e:
    print(f"❌ Error en motor de voz: {e}")

try:
    print("Importando reconocimiento facial...")
    from recognition.face_recognition import hamilton_face
    print("✅ Reconocimiento facial importado")
except Exception as e:
    print(f"❌ Error en reconocimiento facial: {e}")

try:
    print("Importando motor Prolog...")
    from logic.prolog_engine import hamilton_prolog
    prolog_info = hamilton_prolog.get_system_info()
    print(f"✅ Motor Prolog: {'Disponible' if prolog_info['prolog_available'] else 'No disponible'}")
except Exception as e:
    print(f"❌ Error en motor Prolog: {e}")

try:
    print("Importando núcleo de Hamilton...")
    from core.hamilton_core import hamilton
    print("✅ Núcleo de Hamilton importado")
except Exception as e:
    print(f"❌ Error en núcleo: {e}")

# Prueba básica de voz
print("\n🔊 Probando síntesis de voz...")
try:
    hamilton_voice.speak("Prueba básica de Hamilton")
    print("✅ Síntesis de voz funcionando")
except Exception as e:
    print(f"❌ Error en síntesis de voz: {e}")

# Información del sistema
print("\n📊 Información del sistema:")
try:
    voice_info = hamilton_voice.get_voice_info()
    print(f"Voz configurada: {voice_info.get('gender', 'unknown')}")
    
    status = hamilton.get_status()
    print(f"Estado de Hamilton: {status['status']}")
    
    if 'prolog_engine' in status:
        prolog_status = status['prolog_engine']
        print(f"Motor Prolog: {'✅ Activo' if prolog_status['available'] else '❌ No disponible'}")
    
except Exception as e:
    print(f"❌ Error obteniendo información: {e}")

print("\n🎉 Prueba básica completada!")
print("Si no hay errores críticos, Hamilton está listo para usar.")
