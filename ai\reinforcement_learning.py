"""
Hamilton AI Assistant - Reinforcement Learning System
Sistema de aprendizaje por refuerzo basado en feedback del usuario
"""

import logging
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import random

from storage.database_manager import hamilton_db
from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class ActionType(Enum):
    """Tipos de acciones que Hamilton puede tomar"""
    RESPONSE_STYLE = "response_style"
    INFORMATION_DEPTH = "information_depth"
    PROACTIVITY_LEVEL = "proactivity_level"
    VOICE_TONE = "voice_tone"
    SUGGESTION_TIMING = "suggestion_timing"
    AUTOMATION_TRIGGER = "automation_trigger"

class FeedbackType(Enum):
    """Tipos de feedback del usuario"""
    EXPLICIT_POSITIVE = "explicit_positive"  # "Muy bien", "Perfecto"
    EXPLICIT_NEGATIVE = "explicit_negative"  # "No", "Mal"
    IMPLICIT_POSITIVE = "implicit_positive"  # Continúa conversación, acepta sugerencias
    IMPLICIT_NEGATIVE = "implicit_negative"  # Interrumpe, reformula, ignora
    NEUTRAL = "neutral"

@dataclass
class State:
    """Estado del sistema en un momento dado"""
    user_emotion: str
    time_of_day: str
    conversation_context: str
    user_activity: str
    recent_interactions: int
    user_satisfaction_trend: float

@dataclass
class Action:
    """Acción tomada por Hamilton"""
    action_type: ActionType
    parameters: Dict[str, Any]
    timestamp: datetime
    context_state: State

@dataclass
class Feedback:
    """Feedback recibido del usuario"""
    feedback_type: FeedbackType
    intensity: float  # 0.0 a 1.0
    timestamp: datetime
    related_action: Optional[Action] = None
    explicit_comment: Optional[str] = None

@dataclass
class Experience:
    """Experiencia de aprendizaje (State, Action, Reward, Next State)"""
    state: State
    action: Action
    reward: float
    next_state: State
    feedback: Feedback

class ReinforcementLearningAgent:
    """Agente de aprendizaje por refuerzo para Hamilton"""
    
    def __init__(self):
        self.q_table: Dict[str, Dict[str, float]] = {}
        self.experience_buffer: List[Experience] = []
        self.learning_rate = 0.1
        self.discount_factor = 0.95
        self.exploration_rate = 0.1
        self.exploration_decay = 0.995
        
        self.config_file = Path("config/reinforcement_learning.json")
        self.data_file = Path("data/rl_experiences.json")
        
        self.config = {}  # Inicializar config antes de cargar
        self.load_configuration()
        self._initialize_q_table()
    
    def load_configuration(self):
        """Carga configuración del sistema de RL"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self._load_parameters(config)
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de RL: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.config = {
            "learning_parameters": {
                "learning_rate": 0.1,
                "discount_factor": 0.95,
                "exploration_rate": 0.1,
                "exploration_decay": 0.995,
                "min_exploration_rate": 0.01
            },
            "reward_system": {
                "explicit_positive": 1.0,
                "explicit_negative": -1.0,
                "implicit_positive": 0.5,
                "implicit_negative": -0.3,
                "neutral": 0.0,
                "time_decay_factor": 0.9  # Reducir recompensa con el tiempo
            },
            "state_features": {
                "user_emotions": ["happy", "sad", "neutral", "frustrated", "excited"],
                "time_periods": ["morning", "afternoon", "evening", "late_night"],
                "contexts": ["casual", "work", "entertainment", "information", "control"],
                "activities": ["working", "relaxing", "sleeping", "eating", "exercising"]
            },
            "action_spaces": {
                "response_style": ["formal", "casual", "enthusiastic", "empathetic"],
                "information_depth": ["brief", "detailed", "comprehensive"],
                "proactivity_level": ["reactive", "moderate", "proactive"],
                "voice_tone": ["neutral", "warm", "cheerful", "calm"],
                "suggestion_timing": ["immediate", "delayed", "contextual"]
            },
            "learning_settings": {
                "experience_buffer_size": 1000,
                "batch_learning": True,
                "update_frequency": 10,  # Actualizar cada 10 experiencias
                "save_frequency": 50     # Guardar cada 50 experiencias
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        self._load_parameters(self.config)
    
    def _load_parameters(self, config: Dict):
        """Carga parámetros desde configuración"""
        params = config["learning_parameters"]
        self.learning_rate = params["learning_rate"]
        self.discount_factor = params["discount_factor"]
        self.exploration_rate = params["exploration_rate"]
        self.exploration_decay = params["exploration_decay"]
        self.min_exploration_rate = params.get("min_exploration_rate", 0.01)
    
    def _initialize_q_table(self):
        """Inicializa tabla Q con valores por defecto"""
        if not self.config:
            return
        state_features = self.config.get("state_features", {})
        action_spaces = self.config.get("action_spaces", {})
        
        # Crear estados combinando características
        for emotion in state_features["user_emotions"]:
            for time_period in state_features["time_periods"]:
                for context in state_features["contexts"]:
                    state_key = f"{emotion}_{time_period}_{context}"
                    
                    if state_key not in self.q_table:
                        self.q_table[state_key] = {}
                    
                    # Inicializar acciones para este estado
                    for action_type, actions in action_spaces.items():
                        for action in actions:
                            action_key = f"{action_type}_{action}"
                            if action_key not in self.q_table[state_key]:
                                self.q_table[state_key][action_key] = 0.0
        
        logger.info(f"Tabla Q inicializada con {len(self.q_table)} estados")
    
    def get_state_key(self, state: State) -> str:
        """Convierte estado a clave para tabla Q"""
        return f"{state.user_emotion}_{state.time_of_day}_{state.conversation_context}"
    
    def get_action_key(self, action: Action) -> str:
        """Convierte acción a clave para tabla Q"""
        action_value = list(action.parameters.values())[0] if action.parameters else "default"
        return f"{action.action_type.value}_{action_value}"
    
    def select_action(self, state: State, available_actions: List[Action]) -> Action:
        """Selecciona acción usando política epsilon-greedy"""
        state_key = self.get_state_key(state)
        
        # Exploración vs Explotación
        if random.random() < self.exploration_rate:
            # Exploración: acción aleatoria
            selected_action = random.choice(available_actions)
            logger.debug(f"Acción exploratoria seleccionada: {selected_action.action_type.value}")
        else:
            # Explotación: mejor acción conocida
            best_action = self._get_best_action(state_key, available_actions)
            selected_action = best_action if best_action else random.choice(available_actions)
            logger.debug(f"Acción explotativa seleccionada: {selected_action.action_type.value}")
        
        return selected_action
    
    def _get_best_action(self, state_key: str, available_actions: List[Action]) -> Optional[Action]:
        """Obtiene la mejor acción para un estado dado"""
        if state_key not in self.q_table:
            return None
        
        best_action = None
        best_q_value = float('-inf')
        
        for action in available_actions:
            action_key = self.get_action_key(action)
            q_value = self.q_table[state_key].get(action_key, 0.0)
            
            if q_value > best_q_value:
                best_q_value = q_value
                best_action = action
        
        return best_action
    
    def process_feedback(self, action: Action, feedback: Feedback, 
                        current_state: State, next_state: State):
        """Procesa feedback y actualiza el aprendizaje"""
        # Calcular recompensa
        reward = self._calculate_reward(feedback)
        
        # Crear experiencia
        experience = Experience(
            state=current_state,
            action=action,
            reward=reward,
            next_state=next_state,
            feedback=feedback
        )
        
        # Agregar a buffer de experiencias
        self.experience_buffer.append(experience)
        
        # Mantener tamaño del buffer
        max_buffer_size = self.config["learning_settings"]["experience_buffer_size"]
        if len(self.experience_buffer) > max_buffer_size:
            self.experience_buffer = self.experience_buffer[-max_buffer_size:]
        
        # Actualizar tabla Q
        self._update_q_table(experience)
        
        # Decaer exploración
        self.exploration_rate = max(
            self.min_exploration_rate,
            self.exploration_rate * self.exploration_decay
        )
        
        logger.info(f"Feedback procesado: {feedback.feedback_type.value}, recompensa: {reward:.2f}")
        
        # Guardar experiencias periódicamente
        if len(self.experience_buffer) % self.config["learning_settings"]["save_frequency"] == 0:
            self._save_experiences()
    
    def _calculate_reward(self, feedback: Feedback) -> float:
        """Calcula recompensa basada en feedback"""
        reward_config = self.config["reward_system"]
        base_reward = reward_config[feedback.feedback_type.value]
        
        # Ajustar por intensidad
        adjusted_reward = base_reward * feedback.intensity
        
        # Aplicar decaimiento temporal si es necesario
        time_since_action = (datetime.now() - feedback.timestamp).total_seconds()
        if time_since_action > 300:  # 5 minutos
            decay_factor = reward_config["time_decay_factor"]
            adjusted_reward *= decay_factor
        
        return adjusted_reward
    
    def _update_q_table(self, experience: Experience):
        """Actualiza tabla Q usando la experiencia"""
        state_key = self.get_state_key(experience.state)
        action_key = self.get_action_key(experience.action)
        next_state_key = self.get_state_key(experience.next_state)
        
        # Asegurar que las claves existen
        if state_key not in self.q_table:
            self.q_table[state_key] = {}
        if action_key not in self.q_table[state_key]:
            self.q_table[state_key][action_key] = 0.0
        
        # Obtener valor Q actual
        current_q = self.q_table[state_key][action_key]
        
        # Calcular valor Q máximo del siguiente estado
        max_next_q = 0.0
        if next_state_key in self.q_table:
            max_next_q = max(self.q_table[next_state_key].values()) if self.q_table[next_state_key] else 0.0
        
        # Actualización Q-learning
        new_q = current_q + self.learning_rate * (
            experience.reward + self.discount_factor * max_next_q - current_q
        )
        
        self.q_table[state_key][action_key] = new_q
        
        logger.debug(f"Q-value actualizado: {state_key}[{action_key}] = {new_q:.3f}")
    
    def detect_implicit_feedback(self, user_response: str, response_time: float, 
                                action_taken: Action) -> Feedback:
        """Detecta feedback implícito del comportamiento del usuario"""
        feedback_type = FeedbackType.NEUTRAL
        intensity = 0.5
        
        # Analizar respuesta del usuario
        user_response_lower = user_response.lower()
        
        # Indicadores positivos
        positive_indicators = [
            'gracias', 'perfecto', 'excelente', 'bien', 'correcto',
            'sí', 'ok', 'vale', 'continúa', 'siguiente'
        ]
        
        # Indicadores negativos
        negative_indicators = [
            'no', 'mal', 'error', 'incorrecto', 'para', 'detente',
            'repite', 'otra vez', 'no entiendo'
        ]
        
        positive_score = sum(1 for indicator in positive_indicators if indicator in user_response_lower)
        negative_score = sum(1 for indicator in negative_indicators if indicator in user_response_lower)
        
        if positive_score > negative_score:
            feedback_type = FeedbackType.IMPLICIT_POSITIVE
            intensity = min(1.0, 0.5 + positive_score * 0.2)
        elif negative_score > positive_score:
            feedback_type = FeedbackType.IMPLICIT_NEGATIVE
            intensity = min(1.0, 0.5 + negative_score * 0.2)
        
        # Ajustar por tiempo de respuesta
        if response_time < 2.0:  # Respuesta rápida = positivo
            if feedback_type == FeedbackType.NEUTRAL:
                feedback_type = FeedbackType.IMPLICIT_POSITIVE
                intensity = 0.3
        elif response_time > 10.0:  # Respuesta lenta = posible confusión
            if feedback_type == FeedbackType.NEUTRAL:
                feedback_type = FeedbackType.IMPLICIT_NEGATIVE
                intensity = 0.2
        
        return Feedback(
            feedback_type=feedback_type,
            intensity=intensity,
            timestamp=datetime.now(),
            related_action=action_taken,
            explicit_comment=user_response if len(user_response) < 100 else None
        )
    
    def get_learning_recommendations(self, state: State) -> List[Dict[str, Any]]:
        """Obtiene recomendaciones basadas en aprendizaje"""
        state_key = self.get_state_key(state)
        
        if state_key not in self.q_table:
            return []
        
        # Obtener acciones ordenadas por valor Q
        action_values = self.q_table[state_key]
        sorted_actions = sorted(action_values.items(), key=lambda x: x[1], reverse=True)
        
        recommendations = []
        for action_key, q_value in sorted_actions[:3]:  # Top 3 acciones
            action_type, action_value = action_key.split('_', 1)
            
            recommendations.append({
                'action_type': action_type,
                'action_value': action_value,
                'confidence': min(1.0, max(0.0, (q_value + 1) / 2)),  # Normalizar a 0-1
                'q_value': q_value
            })
        
        return recommendations
    
    def _save_experiences(self):
        """Guarda experiencias en archivo"""
        try:
            # Convertir experiencias a formato serializable
            serializable_experiences = []
            for exp in self.experience_buffer[-100:]:  # Guardar últimas 100
                exp_dict = {
                    'state': asdict(exp.state),
                    'action': {
                        'action_type': exp.action.action_type.value,
                        'parameters': exp.action.parameters,
                        'timestamp': exp.action.timestamp.isoformat()
                    },
                    'reward': exp.reward,
                    'next_state': asdict(exp.next_state),
                    'feedback': {
                        'feedback_type': exp.feedback.feedback_type.value,
                        'intensity': exp.feedback.intensity,
                        'timestamp': exp.feedback.timestamp.isoformat(),
                        'explicit_comment': exp.feedback.explicit_comment
                    }
                }
                serializable_experiences.append(exp_dict)
            
            # Guardar datos
            data = {
                'experiences': serializable_experiences,
                'q_table': self.q_table,
                'learning_stats': {
                    'total_experiences': len(self.experience_buffer),
                    'exploration_rate': self.exploration_rate,
                    'last_updated': datetime.now().isoformat()
                }
            }
            
            self.data_file.parent.mkdir(exist_ok=True)
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Experiencias guardadas: {len(serializable_experiences)} registros")
            
        except Exception as e:
            logger.error(f"Error guardando experiencias: {e}")
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas de aprendizaje"""
        return {
            'total_experiences': len(self.experience_buffer),
            'q_table_size': len(self.q_table),
            'exploration_rate': self.exploration_rate,
            'learning_rate': self.learning_rate,
            'recent_rewards': [exp.reward for exp in self.experience_buffer[-10:]],
            'avg_recent_reward': np.mean([exp.reward for exp in self.experience_buffer[-10:]]) if self.experience_buffer else 0.0
        }

class PredictiveAnalyzer:
    """Analizador predictivo de necesidades del usuario"""

    def __init__(self):
        self.pattern_history = []
        self.prediction_models = {}
        self.confidence_threshold = 0.7

    def analyze_user_patterns(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analiza patrones del usuario para predicciones"""
        patterns = {
            'temporal_patterns': self._analyze_temporal_patterns(user_data),
            'behavioral_patterns': self._analyze_behavioral_patterns(user_data),
            'preference_patterns': self._analyze_preference_patterns(user_data),
            'context_patterns': self._analyze_context_patterns(user_data)
        }

        return patterns

    def _analyze_temporal_patterns(self, user_data: Dict) -> Dict[str, Any]:
        """Analiza patrones temporales"""
        # Implementación simplificada
        return {
            'peak_hours': ['09:00', '14:00', '20:00'],
            'common_days': ['monday', 'wednesday', 'friday'],
            'seasonal_trends': {}
        }

    def _analyze_behavioral_patterns(self, user_data: Dict) -> Dict[str, Any]:
        """Analiza patrones de comportamiento"""
        return {
            'command_sequences': [],
            'response_preferences': {},
            'interaction_duration': 0
        }

    def _analyze_preference_patterns(self, user_data: Dict) -> Dict[str, Any]:
        """Analiza patrones de preferencias"""
        return {
            'preferred_response_style': 'formal',
            'information_depth': 'detailed',
            'automation_acceptance': 0.8
        }

    def _analyze_context_patterns(self, user_data: Dict) -> Dict[str, Any]:
        """Analiza patrones contextuales"""
        return {
            'location_based': {},
            'activity_based': {},
            'mood_based': {}
        }

    def predict_next_need(self, current_context: Dict) -> Optional[Dict[str, Any]]:
        """Predice la próxima necesidad del usuario"""
        # Implementación simplificada de predicción
        predictions = [
            {
                'need_type': 'information_request',
                'specific_need': 'weather_update',
                'confidence': 0.75,
                'suggested_timing': 'now',
                'reasoning': 'Usuario suele preguntar el clima a esta hora'
            },
            {
                'need_type': 'automation_trigger',
                'specific_need': 'evening_routine',
                'confidence': 0.65,
                'suggested_timing': '30_minutes',
                'reasoning': 'Patrón de activación de rutina nocturna'
            }
        ]

        # Filtrar por umbral de confianza
        high_confidence_predictions = [
            p for p in predictions if p['confidence'] >= self.confidence_threshold
        ]

        return high_confidence_predictions[0] if high_confidence_predictions else None

# Instancias globales
hamilton_rl_agent = ReinforcementLearningAgent()
hamilton_predictive = PredictiveAnalyzer()
