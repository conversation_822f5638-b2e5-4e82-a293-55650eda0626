"""
Hamilton AI Assistant - Web Interface
Interfaz web moderna para Hamilton usando FastAPI
"""

import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime
import json

# Agregar directorio padre al path
sys.path.insert(0, str(Path(__file__).parent.parent))

# FastAPI y dependencias web
try:
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    import uvicorn
except ImportError:
    print("❌ FastAPI no está instalado. Instalar con: pip install fastapi uvicorn websockets")
    sys.exit(1)

# Importar Hamilton
try:
    from core.hamilton_core import hamilton
    from integrations.iot_controller import hamilton_iot
    from integrations.home_automation import hamilton_automation
    from integrations.calendar_manager import hamilton_calendar
    from integrations.emotion_recognition import hamilton_emotion
except ImportError as e:
    print(f"❌ Error importando módu<PERSON> de <PERSON>: {e}")
    sys.exit(1)

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Crear aplicación FastAPI
app = FastAPI(
    title="Hamilton AI Assistant",
    description="Interfaz web para el asistente personal Hamilton",
    version="2.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modelos Pydantic
class ChatMessage(BaseModel):
    message: str
    user_id: str = "senor_ibero"

class IoTCommand(BaseModel):
    device_id: str
    command: str
    parameters: Dict[str, Any] = {}

class CalendarEvent(BaseModel):
    title: str
    start_time: str
    end_time: str
    description: str = ""

# Gestor de conexiones WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"Nueva conexión WebSocket: {len(self.active_connections)} activas")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info(f"Conexión WebSocket cerrada: {len(self.active_connections)} activas")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Conexión cerrada, remover
                self.active_connections.remove(connection)

manager = ConnectionManager()

# Rutas estáticas
web_dir = Path(__file__).parent / "static"
web_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(web_dir)), name="static")

# Rutas principales
@app.get("/", response_class=HTMLResponse)
async def get_dashboard():
    """Página principal del dashboard"""
    html_content = """
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Hamilton AI Assistant</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; min-height: 100vh;
            }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
            .header p { font-size: 1.2em; opacity: 0.9; }
            .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .card { 
                background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);
                border-radius: 15px; padding: 20px; border: 1px solid rgba(255,255,255,0.2);
            }
            .card h3 { margin-bottom: 15px; color: #fff; }
            .chat-container { height: 400px; display: flex; flex-direction: column; }
            .chat-messages { 
                flex: 1; overflow-y: auto; padding: 10px; 
                background: rgba(0,0,0,0.2); border-radius: 10px; margin-bottom: 10px;
            }
            .message { margin-bottom: 10px; padding: 8px 12px; border-radius: 8px; }
            .user-message { background: rgba(100,149,237,0.3); text-align: right; }
            .hamilton-message { background: rgba(50,205,50,0.3); }
            .chat-input { display: flex; gap: 10px; }
            .chat-input input { 
                flex: 1; padding: 10px; border: none; border-radius: 8px;
                background: rgba(255,255,255,0.2); color: white;
            }
            .chat-input input::placeholder { color: rgba(255,255,255,0.7); }
            .btn { 
                padding: 10px 20px; border: none; border-radius: 8px;
                background: rgba(255,255,255,0.2); color: white; cursor: pointer;
                transition: background 0.3s;
            }
            .btn:hover { background: rgba(255,255,255,0.3); }
            .status { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }
            .status-dot { width: 12px; height: 12px; border-radius: 50%; }
            .online { background: #4CAF50; }
            .offline { background: #f44336; }
            .device-list { max-height: 200px; overflow-y: auto; }
            .device-item { 
                display: flex; justify-content: between; align-items: center;
                padding: 8px; margin-bottom: 5px; background: rgba(0,0,0,0.2); border-radius: 5px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 Hamilton</h1>
                <p>Asistente Personal de IA - Interfaz Web</p>
            </div>
            
            <div class="dashboard">
                <!-- Chat con Hamilton -->
                <div class="card">
                    <h3>💬 Chat con Hamilton</h3>
                    <div class="status">
                        <div class="status-dot online"></div>
                        <span>Hamilton está activo</span>
                    </div>
                    <div class="chat-container">
                        <div class="chat-messages" id="chatMessages">
                            <div class="message hamilton-message">
                                ¡Hola! Soy Hamilton, su asistente personal. ¿En qué puedo ayudarle?
                            </div>
                        </div>
                        <div class="chat-input">
                            <input type="text" id="messageInput" placeholder="Escriba su mensaje...">
                            <button class="btn" onclick="sendMessage()">Enviar</button>
                        </div>
                    </div>
                </div>
                
                <!-- Estado del Sistema -->
                <div class="card">
                    <h3>⚙️ Estado del Sistema</h3>
                    <div id="systemStatus">Cargando...</div>
                </div>
                
                <!-- Dispositivos IoT -->
                <div class="card">
                    <h3>🏠 Dispositivos IoT</h3>
                    <div class="device-list" id="deviceList">
                        Cargando dispositivos...
                    </div>
                    <button class="btn" onclick="refreshDevices()">Actualizar</button>
                </div>
                
                <!-- Calendario -->
                <div class="card">
                    <h3>📅 Próximos Eventos</h3>
                    <div id="calendarEvents">Cargando eventos...</div>
                </div>
                
                <!-- Estado Emocional -->
                <div class="card">
                    <h3>😊 Estado Emocional</h3>
                    <div id="emotionStatus">Analizando...</div>
                </div>
                
                <!-- Controles Rápidos -->
                <div class="card">
                    <h3>🎛️ Controles Rápidos</h3>
                    <button class="btn" onclick="activateScene('modo_trabajo')">Modo Trabajo</button>
                    <button class="btn" onclick="activateScene('modo_noche')">Modo Noche</button>
                    <button class="btn" onclick="activateScene('llegada_casa')">Llegada Casa</button>
                </div>
            </div>
        </div>
        
        <script>
            // WebSocket para comunicación en tiempo real
            const ws = new WebSocket('ws://localhost:8000/ws');
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'chat_response') {
                    addMessage(data.message, 'hamilton');
                }
            };
            
            function addMessage(message, sender) {
                const messagesDiv = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                messageDiv.textContent = message;
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
            
            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (message) {
                    addMessage(message, 'user');
                    fetch('/api/chat', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({message: message, user_id: 'senor_ibero'})
                    });
                    input.value = '';
                }
            }
            
            function activateScene(sceneId) {
                fetch(`/api/automation/scene/${sceneId}`, {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    addMessage(`Escena ${sceneId} ${data.success ? 'activada' : 'falló'}`, 'hamilton');
                });
            }
            
            function refreshDevices() {
                fetch('/api/iot/devices')
                .then(response => response.json())
                .then(devices => {
                    const deviceList = document.getElementById('deviceList');
                    deviceList.innerHTML = devices.map(device => 
                        `<div class="device-item">
                            <span>${device.name}</span>
                            <span>${device.status}</span>
                        </div>`
                    ).join('');
                });
            }
            
            // Cargar datos iniciales
            document.addEventListener('DOMContentLoaded', function() {
                refreshDevices();
                
                // Actualizar estado del sistema
                fetch('/api/status')
                .then(response => response.json())
                .then(status => {
                    document.getElementById('systemStatus').innerHTML = `
                        <p>Estado: ${status.status}</p>
                        <p>Usuario: ${status.current_user}</p>
                        <p>Sesión: ${status.session_start_time ? 'Activa' : 'Inactiva'}</p>
                    `;
                });
                
                // Enter para enviar mensaje
                document.getElementById('messageInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') sendMessage();
                });
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# API Routes
@app.post("/api/chat")
async def chat_with_hamilton(message: ChatMessage):
    """Endpoint para chat con Hamilton"""
    try:
        # Procesar mensaje con Hamilton
        response = hamilton.process_text_input(message.message)
        
        # Enviar respuesta por WebSocket
        await manager.broadcast(json.dumps({
            "type": "chat_response",
            "message": response,
            "timestamp": datetime.now().isoformat()
        }))
        
        return {"response": response, "success": True}
        
    except Exception as e:
        logger.error(f"Error en chat: {e}")
        return {"response": "Error procesando mensaje", "success": False}

@app.get("/api/status")
async def get_system_status():
    """Obtiene estado del sistema Hamilton"""
    try:
        status = hamilton.get_status()
        return status
    except Exception as e:
        logger.error(f"Error obteniendo estado: {e}")
        return {"error": str(e)}

@app.get("/api/iot/devices")
async def get_iot_devices():
    """Obtiene lista de dispositivos IoT"""
    try:
        devices = []
        for device_id, device in hamilton_iot.devices.items():
            devices.append({
                "id": device_id,
                "name": device.name,
                "type": device.type,
                "status": device.status,
                "room": device.room
            })
        return devices
    except Exception as e:
        logger.error(f"Error obteniendo dispositivos: {e}")
        return []

@app.post("/api/iot/control")
async def control_iot_device(command: IoTCommand):
    """Controla dispositivo IoT"""
    try:
        success = await hamilton_iot.control_device(
            command.device_id, 
            command.command, 
            command.parameters
        )
        return {"success": success}
    except Exception as e:
        logger.error(f"Error controlando dispositivo: {e}")
        return {"success": False, "error": str(e)}

@app.post("/api/automation/scene/{scene_id}")
async def activate_scene(scene_id: str):
    """Activa escena de automatización"""
    try:
        success = await hamilton_automation.activate_scene(scene_id)
        return {"success": success}
    except Exception as e:
        logger.error(f"Error activando escena: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/calendar/events")
async def get_calendar_events():
    """Obtiene eventos de calendario"""
    try:
        schedule = hamilton_calendar.get_today_schedule()
        return schedule
    except Exception as e:
        logger.error(f"Error obteniendo eventos: {e}")
        return {"events": [], "reminders": []}

@app.get("/api/emotion/status")
async def get_emotion_status():
    """Obtiene estado emocional"""
    try:
        emotion_summary = hamilton_emotion.get_emotion_summary()
        return emotion_summary
    except Exception as e:
        logger.error(f"Error obteniendo estado emocional: {e}")
        return {"status": "error"}

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo para mantener conexión activa
            await manager.send_personal_message(f"Echo: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Función para ejecutar servidor
def run_web_interface():
    """Ejecuta la interfaz web"""
    print("🌐 HAMILTON - INTERFAZ WEB")
    print("=" * 30)
    print("Iniciando servidor web...")
    print("URL: http://localhost:8000")
    print("Presione Ctrl+C para detener")
    
    try:
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    except KeyboardInterrupt:
        print("\n👋 Servidor web detenido")

if __name__ == "__main__":
    run_web_interface()
