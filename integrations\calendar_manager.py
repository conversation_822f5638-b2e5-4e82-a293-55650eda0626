"""
Hamilton AI Assistant - Calendar and Reminders Manager
Sistema de gestión de calendarios y recordatorios inteligentes
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta, date, time
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import requests
from urllib.parse import urlencode

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class EventType(Enum):
    """Tipos de eventos de calendario"""
    MEETING = "meeting"
    APPOINTMENT = "appointment"
    REMINDER = "reminder"
    TASK = "task"
    BIRTHDAY = "birthday"
    HOLIDAY = "holiday"
    PERSONAL = "personal"
    WORK = "work"

class Priority(Enum):
    """Niveles de prioridad"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

@dataclass
class CalendarEvent:
    """Evento de calendario"""
    id: str
    title: str
    description: str
    start_time: datetime
    end_time: datetime
    event_type: EventType
    priority: Priority
    location: Optional[str] = None
    attendees: List[str] = None
    reminders: List[int] = None  # Minutos antes del evento
    recurring: bool = False
    recurring_pattern: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    calendar_source: str = "hamilton"  # hamilton, google, outlook, etc.

@dataclass
class Reminder:
    """Recordatorio independiente"""
    id: str
    title: str
    description: str
    reminder_time: datetime
    priority: Priority
    completed: bool = False
    recurring: bool = False
    recurring_pattern: Optional[str] = None
    created_at: datetime = None

class CalendarManager:
    """Gestor principal de calendarios y recordatorios"""
    
    def __init__(self):
        self.events: Dict[str, CalendarEvent] = {}
        self.reminders: Dict[str, Reminder] = {}
        self.calendar_sources = {
            'google': GoogleCalendarIntegration(),
            'outlook': OutlookCalendarIntegration(),
            'apple': AppleCalendarIntegration()
        }
        self.config_file = Path("config/calendar_config.json")
        self.data_file = Path("data/calendar_data.json")
        self.load_configuration()
        self.load_data()
    
    def load_configuration(self):
        """Carga configuración de calendarios"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de calendario: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.config = {
            "integrations": {
                "google_calendar": {
                    "enabled": False,
                    "client_id": "",
                    "client_secret": "",
                    "refresh_token": "",
                    "calendar_ids": []
                },
                "outlook_calendar": {
                    "enabled": False,
                    "client_id": "",
                    "client_secret": "",
                    "refresh_token": ""
                },
                "apple_calendar": {
                    "enabled": False,
                    "username": "",
                    "app_password": ""
                }
            },
            "preferences": {
                "default_reminder_minutes": [15, 60],  # 15 min y 1 hora antes
                "work_hours_start": "09:00",
                "work_hours_end": "18:00",
                "timezone": "America/Mexico_City",
                "auto_sync_interval_minutes": 30,
                "smart_reminders": True,
                "voice_announcements": True
            },
            "smart_features": {
                "travel_time_estimation": True,
                "weather_integration": True,
                "conflict_detection": True,
                "automatic_scheduling": True
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def load_data(self):
        """Carga eventos y recordatorios guardados"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._load_events_from_data(data.get('events', {}))
                    self._load_reminders_from_data(data.get('reminders', {}))
        except Exception as e:
            logger.error(f"Error cargando datos de calendario: {e}")
    
    def _load_events_from_data(self, events_data: Dict):
        """Carga eventos desde datos guardados"""
        for event_id, event_data in events_data.items():
            try:
                event = CalendarEvent(
                    id=event_data['id'],
                    title=event_data['title'],
                    description=event_data['description'],
                    start_time=datetime.fromisoformat(event_data['start_time']),
                    end_time=datetime.fromisoformat(event_data['end_time']),
                    event_type=EventType(event_data['event_type']),
                    priority=Priority(event_data['priority']),
                    location=event_data.get('location'),
                    attendees=event_data.get('attendees', []),
                    reminders=event_data.get('reminders', []),
                    recurring=event_data.get('recurring', False),
                    recurring_pattern=event_data.get('recurring_pattern'),
                    created_at=datetime.fromisoformat(event_data['created_at']) if event_data.get('created_at') else None,
                    updated_at=datetime.fromisoformat(event_data['updated_at']) if event_data.get('updated_at') else None,
                    calendar_source=event_data.get('calendar_source', 'hamilton')
                )
                self.events[event_id] = event
            except Exception as e:
                logger.warning(f"Error cargando evento {event_id}: {e}")
    
    def _load_reminders_from_data(self, reminders_data: Dict):
        """Carga recordatorios desde datos guardados"""
        for reminder_id, reminder_data in reminders_data.items():
            try:
                reminder = Reminder(
                    id=reminder_data['id'],
                    title=reminder_data['title'],
                    description=reminder_data['description'],
                    reminder_time=datetime.fromisoformat(reminder_data['reminder_time']),
                    priority=Priority(reminder_data['priority']),
                    completed=reminder_data.get('completed', False),
                    recurring=reminder_data.get('recurring', False),
                    recurring_pattern=reminder_data.get('recurring_pattern'),
                    created_at=datetime.fromisoformat(reminder_data['created_at']) if reminder_data.get('created_at') else None
                )
                self.reminders[reminder_id] = reminder
            except Exception as e:
                logger.warning(f"Error cargando recordatorio {reminder_id}: {e}")
    
    async def create_event(self, title: str, start_time: datetime, end_time: datetime, 
                          description: str = "", event_type: EventType = EventType.PERSONAL,
                          priority: Priority = Priority.MEDIUM, location: str = None) -> str:
        """Crea un nuevo evento"""
        try:
            event_id = f"hamilton_event_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            event = CalendarEvent(
                id=event_id,
                title=title,
                description=description,
                start_time=start_time,
                end_time=end_time,
                event_type=event_type,
                priority=priority,
                location=location,
                attendees=[],
                reminders=self.config['preferences']['default_reminder_minutes'].copy(),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.events[event_id] = event
            await self._save_data()
            
            logger.info(f"✅ Evento creado: {title} para {start_time}")
            return event_id
            
        except Exception as e:
            logger.error(f"Error creando evento: {e}")
            return ""
    
    async def create_reminder(self, title: str, reminder_time: datetime, 
                            description: str = "", priority: Priority = Priority.MEDIUM) -> str:
        """Crea un nuevo recordatorio"""
        try:
            reminder_id = f"hamilton_reminder_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            reminder = Reminder(
                id=reminder_id,
                title=title,
                description=description,
                reminder_time=reminder_time,
                priority=priority,
                created_at=datetime.now()
            )
            
            self.reminders[reminder_id] = reminder
            await self._save_data()
            
            logger.info(f"✅ Recordatorio creado: {title} para {reminder_time}")
            return reminder_id
            
        except Exception as e:
            logger.error(f"Error creando recordatorio: {e}")
            return ""
    
    def get_upcoming_events(self, hours_ahead: int = 24) -> List[CalendarEvent]:
        """Obtiene eventos próximos"""
        now = datetime.now()
        cutoff_time = now + timedelta(hours=hours_ahead)
        
        upcoming = []
        for event in self.events.values():
            if now <= event.start_time <= cutoff_time:
                upcoming.append(event)
        
        # Ordenar por tiempo de inicio
        upcoming.sort(key=lambda e: e.start_time)
        return upcoming
    
    def get_upcoming_reminders(self, hours_ahead: int = 24) -> List[Reminder]:
        """Obtiene recordatorios próximos"""
        now = datetime.now()
        cutoff_time = now + timedelta(hours=hours_ahead)
        
        upcoming = []
        for reminder in self.reminders.values():
            if not reminder.completed and now <= reminder.reminder_time <= cutoff_time:
                upcoming.append(reminder)
        
        # Ordenar por tiempo de recordatorio
        upcoming.sort(key=lambda r: r.reminder_time)
        return upcoming
    
    def get_today_schedule(self) -> Dict[str, Any]:
        """Obtiene la agenda de hoy"""
        today = date.today()
        today_start = datetime.combine(today, time.min)
        today_end = datetime.combine(today, time.max)
        
        today_events = []
        for event in self.events.values():
            if today_start <= event.start_time <= today_end:
                today_events.append(event)
        
        today_reminders = []
        for reminder in self.reminders.values():
            if not reminder.completed and today_start <= reminder.reminder_time <= today_end:
                today_reminders.append(reminder)
        
        # Ordenar por tiempo
        today_events.sort(key=lambda e: e.start_time)
        today_reminders.sort(key=lambda r: r.reminder_time)
        
        return {
            'date': today.isoformat(),
            'events': today_events,
            'reminders': today_reminders,
            'total_events': len(today_events),
            'total_reminders': len(today_reminders)
        }
    
    async def process_voice_command(self, command: str) -> Optional[str]:
        """Procesa comandos de voz relacionados con calendario"""
        command_lower = command.lower()
        
        # Comandos de consulta
        if any(word in command_lower for word in ["agenda", "calendario", "eventos"]):
            if "hoy" in command_lower:
                return await self._get_today_summary()
            elif "mañana" in command_lower:
                return await self._get_tomorrow_summary()
            elif "semana" in command_lower:
                return await self._get_week_summary()
            else:
                return await self._get_today_summary()
        
        # Comandos de recordatorios
        if "recordatorio" in command_lower or "recordar" in command_lower:
            if "crear" in command_lower or "agregar" in command_lower:
                return "Para crear un recordatorio, dígame: 'Recordar [título] para [fecha/hora]'"
            else:
                upcoming_reminders = self.get_upcoming_reminders(24)
                if upcoming_reminders:
                    return f"Tiene {len(upcoming_reminders)} recordatorios próximos. El siguiente es: {upcoming_reminders[0].title} a las {upcoming_reminders[0].reminder_time.strftime('%H:%M')}"
                else:
                    return "No tiene recordatorios próximos."
        
        # Comandos de próximos eventos
        if any(word in command_lower for word in ["próximo", "siguiente", "próximos"]):
            upcoming = self.get_upcoming_events(24)
            if upcoming:
                next_event = upcoming[0]
                time_str = next_event.start_time.strftime('%H:%M')
                return f"Su próximo evento es: {next_event.title} a las {time_str}"
            else:
                return "No tiene eventos próximos en las siguientes 24 horas."
        
        return None
    
    async def _get_today_summary(self) -> str:
        """Obtiene resumen de la agenda de hoy"""
        schedule = self.get_today_schedule()
        events = schedule['events']
        reminders = schedule['reminders']
        
        if not events and not reminders:
            return "No tiene eventos ni recordatorios programados para hoy."
        
        summary_parts = []
        
        if events:
            summary_parts.append(f"Tiene {len(events)} evento(s) hoy:")
            for event in events[:3]:  # Mostrar máximo 3 eventos
                time_str = event.start_time.strftime('%H:%M')
                summary_parts.append(f"- {event.title} a las {time_str}")
            
            if len(events) > 3:
                summary_parts.append(f"... y {len(events) - 3} evento(s) más")
        
        if reminders:
            summary_parts.append(f"Tiene {len(reminders)} recordatorio(s):")
            for reminder in reminders[:2]:  # Mostrar máximo 2 recordatorios
                time_str = reminder.reminder_time.strftime('%H:%M')
                summary_parts.append(f"- {reminder.title} a las {time_str}")
        
        return " ".join(summary_parts)
    
    async def _get_tomorrow_summary(self) -> str:
        """Obtiene resumen de la agenda de mañana"""
        tomorrow = date.today() + timedelta(days=1)
        tomorrow_start = datetime.combine(tomorrow, time.min)
        tomorrow_end = datetime.combine(tomorrow, time.max)
        
        tomorrow_events = []
        for event in self.events.values():
            if tomorrow_start <= event.start_time <= tomorrow_end:
                tomorrow_events.append(event)
        
        if not tomorrow_events:
            return "No tiene eventos programados para mañana."
        
        tomorrow_events.sort(key=lambda e: e.start_time)
        
        summary = f"Mañana tiene {len(tomorrow_events)} evento(s):"
        for event in tomorrow_events[:3]:
            time_str = event.start_time.strftime('%H:%M')
            summary += f" {event.title} a las {time_str}."
        
        return summary
    
    async def _get_week_summary(self) -> str:
        """Obtiene resumen de la semana"""
        today = date.today()
        week_start = today
        week_end = today + timedelta(days=7)
        
        week_events = []
        for event in self.events.values():
            if week_start <= event.start_time.date() <= week_end:
                week_events.append(event)
        
        if not week_events:
            return "No tiene eventos programados para esta semana."
        
        return f"Esta semana tiene {len(week_events)} evento(s) programados."
    
    async def _save_data(self):
        """Guarda eventos y recordatorios"""
        try:
            data = {
                'events': {eid: asdict(event) for eid, event in self.events.items()},
                'reminders': {rid: asdict(reminder) for rid, reminder in self.reminders.items()},
                'last_updated': datetime.now().isoformat()
            }
            
            # Convertir datetime a string para JSON
            for event_data in data['events'].values():
                for key in ['start_time', 'end_time', 'created_at', 'updated_at']:
                    if event_data.get(key):
                        event_data[key] = event_data[key].isoformat() if hasattr(event_data[key], 'isoformat') else event_data[key]
                event_data['event_type'] = event_data['event_type'].value if hasattr(event_data['event_type'], 'value') else event_data['event_type']
                event_data['priority'] = event_data['priority'].value if hasattr(event_data['priority'], 'value') else event_data['priority']
            
            for reminder_data in data['reminders'].values():
                for key in ['reminder_time', 'created_at']:
                    if reminder_data.get(key):
                        reminder_data[key] = reminder_data[key].isoformat() if hasattr(reminder_data[key], 'isoformat') else reminder_data[key]
                reminder_data['priority'] = reminder_data['priority'].value if hasattr(reminder_data['priority'], 'value') else reminder_data['priority']
            
            self.data_file.parent.mkdir(exist_ok=True)
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Error guardando datos de calendario: {e}")

class GoogleCalendarIntegration:
    """Integración con Google Calendar"""
    
    async def sync_events(self) -> List[CalendarEvent]:
        """Sincroniza eventos desde Google Calendar"""
        # Implementación de la API de Google Calendar
        logger.info("Sincronizando con Google Calendar...")
        return []

class OutlookCalendarIntegration:
    """Integración con Outlook Calendar"""
    
    async def sync_events(self) -> List[CalendarEvent]:
        """Sincroniza eventos desde Outlook Calendar"""
        # Implementación de la API de Microsoft Graph
        logger.info("Sincronizando con Outlook Calendar...")
        return []

class AppleCalendarIntegration:
    """Integración con Apple Calendar"""
    
    async def sync_events(self) -> List[CalendarEvent]:
        """Sincroniza eventos desde Apple Calendar"""
        # Implementación de CalDAV para Apple Calendar
        logger.info("Sincronizando con Apple Calendar...")
        return []

# Instancia global
hamilton_calendar = CalendarManager()
