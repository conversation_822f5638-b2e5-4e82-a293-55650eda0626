#!/usr/bin/env python3
"""
Hamilton AI Assistant - Main Entry Point
Punto de entrada principal para el asistente personal Hamilton
"""

import asyncio
import signal
import sys
import logging
import time
from pathlib import Path

# Agregar el directorio actual al path
sys.path.append(str(Path(__file__).parent))

from core.hamilton_core import hamilton
from recognition.voice_recognition import hamilton_voice
from recognition.face_recognition import hamilton_face
from config.settings import settings

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/hamilton.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HamiltonApplication:
    """Aplicación principal de Hamilton"""
    
    def __init__(self):
        self.running = False
        self.hamilton = hamilton
        self.setup_signal_handlers()
        
    def setup_signal_handlers(self):
        """Configura manejadores de señales para cierre limpio"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Manejador de señales para cierre limpio"""
        logger.info(f"Señal recibida: {signum}")
        self.shutdown()
    
    def startup_checks(self) -> bool:
        """Realiza verificaciones de inicio"""
        logger.info("Realizando verificaciones de inicio...")
        
        # Verificar configuración
        if not settings.OPENAI_API_KEY and settings.DEBUG:
            logger.warning("OpenAI API Key no configurada (modo debug)")
        
        # Verificar motor de voz
        voice_info = hamilton_voice.get_voice_info()
        if "error" in voice_info:
            logger.error(f"Error en motor de voz: {voice_info['error']}")
            return False
        else:
            logger.info(f"Motor de voz configurado: {voice_info}")
        
        # Verificar reconocimiento facial
        if len(hamilton_face.known_face_encodings) == 0:
            logger.warning("No hay caras registradas. Use el modo de registro.")
            print("\n" + "="*60)
            print("AVISO: No hay caras registradas para el señor Ibero")
            print("Para registrar su cara, ejecute: python register_face.py")
            print("="*60 + "\n")
        else:
            logger.info(f"Caras registradas: {len(hamilton_face.known_face_encodings)}")
        
        logger.info("Verificaciones de inicio completadas")
        return True
    
    def start_wake_word_detection(self):
        """Inicia detección de palabra de activación"""
        logger.info("Iniciando detección de palabra de activación...")
        
        def wake_word_callback(text):
            if hamilton_voice.is_wake_word(text) and not self.hamilton.is_active:
                logger.info(f"Palabra de activación detectada: {text}")
                self.hamilton.wake_up()
        
        hamilton_voice.start_continuous_listening(wake_word_callback)
        logger.info("Detección de palabra de activación activa")
    
    def run(self):
        """Ejecuta la aplicación principal"""
        logger.info("="*60)
        logger.info("HAMILTON AI ASSISTANT - INICIANDO")
        logger.info(f"Versión: {settings.VERSION}")
        logger.info(f"Usuario autorizado: {settings.AUTHORIZED_USER}")
        logger.info("="*60)
        
        # Verificaciones de inicio
        if not self.startup_checks():
            logger.error("Fallo en verificaciones de inicio")
            return False
        
        # Mensaje de bienvenida
        print("\n" + "="*60)
        print("🤖 HAMILTON AI ASSISTANT")
        print("="*60)
        print("Hamilton está iniciando...")
        print("Para activar a Hamilton, diga: 'Hamilton' o 'Hey Hamilton'")
        print("Para salir, presione Ctrl+C")
        print("="*60 + "\n")
        
        # Configurar callbacks
        self.hamilton.on_wake_callback = self.on_hamilton_wake
        self.hamilton.on_sleep_callback = self.on_hamilton_sleep
        self.hamilton.on_authentication_callback = self.on_authentication
        self.hamilton.on_command_callback = self.on_command
        
        # Iniciar aplicación
        self.running = True
        
        # Mensaje de voz inicial
        hamilton_voice.speak("Hamilton iniciando. Esperando palabra de activación.")
        
        # Iniciar detección de palabra de activación
        self.start_wake_word_detection()
        
        try:
            # Loop principal
            while self.running:
                time.sleep(1)
                
                # Mostrar estado cada 30 segundos si está en debug
                if settings.DEBUG and int(time.time()) % 30 == 0:
                    status = self.hamilton.get_status()
                    logger.debug(f"Estado Hamilton: {status}")
        
        except KeyboardInterrupt:
            logger.info("Interrupción de teclado recibida")
        except Exception as e:
            logger.error(f"Error inesperado: {e}")
        finally:
            self.shutdown()
        
        return True
    
    def on_hamilton_wake(self):
        """Callback cuando Hamilton despierta"""
        print("\n🟢 Hamilton está ACTIVO y autenticado")
        print("Escuchando comandos de voz...")
    
    def on_hamilton_sleep(self):
        """Callback cuando Hamilton se duerme"""
        print("\n🔴 Hamilton está DORMIDO")
        print("Esperando palabra de activación...")
    
    def on_authentication(self, success: bool, user: str, confidence: float):
        """Callback de autenticación"""
        if success:
            print(f"\n✅ Usuario autenticado: {user} (confianza: {confidence:.2f})")
        else:
            print(f"\n❌ Fallo en autenticación: {user}")
    
    def on_command(self, command: str, response: str):
        """Callback de comando procesado"""
        print(f"\n👤 Usuario: {command}")
        print(f"🤖 Hamilton: {response}")
    
    def shutdown(self):
        """Cierre limpio de la aplicación"""
        if not self.running:
            return
        
        logger.info("Iniciando cierre de Hamilton...")
        self.running = False
        
        # Dormir Hamilton si está activo
        if self.hamilton.is_active:
            self.hamilton.sleep()
        
        # Detener escucha de voz
        hamilton_voice.stop_continuous_listening()
        
        print("\n" + "="*60)
        print("🤖 Hamilton se ha desconectado")
        print("¡Hasta luego, señor Ibero!")
        print("="*60)
        
        logger.info("Hamilton desconectado exitosamente")

def main():
    """Función principal"""
    try:
        app = HamiltonApplication()
        success = app.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Error fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
