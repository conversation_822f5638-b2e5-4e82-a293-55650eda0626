#!/usr/bin/env python3
"""
Hamilton AI Assistant - Main Entry Point
Punto de entrada principal para el asistente personal Hamilton
"""

import asyncio
import signal
import sys
import logging
import time
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# Agregar el directorio actual al path
sys.path.append(str(Path(__file__).parent))

from core.hamilton_core import hamilton
from recognition.voice_recognition import hamilton_voice
from recognition.face_recognition import hamilton_face
from config.settings import settings
from ai.gpt_engine import hamilton_gpt
from ai.local_llm import hamilton_local
from ai.huggingface_engine import hamilton_hf
from ai.anthropic_engine import hamilton_anthropic
from ai.response_evaluator import response_evaluator

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/hamilton.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HamiltonApplication:
    """Aplicación principal de Hamilton"""
    
    def __init__(self):
        self.running = False
        self.hamilton = hamilton
        self.setup_signal_handlers()
        self.gpt = hamilton_gpt
        self.local_llm = hamilton_local
        self.hf = hamilton_hf
        self.anthropic = hamilton_anthropic
        self.evaluator = response_evaluator
        
    def setup_signal_handlers(self):
        """Configura manejadores de señales para cierre limpio"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Manejador de señales para cierre limpio"""
        logger.info(f"Señal recibida: {signum}")
        self.shutdown()
    
    def startup_checks(self) -> bool:
        """Realiza verificaciones de inicio"""
        logger.info("Realizando verificaciones de inicio...")
        
        # Verificar configuración
        if not settings.OPENAI_API_KEY and settings.DEBUG:
            logger.warning("OpenAI API Key no configurada (modo debug)")
        
        # Verificar motor de voz
        voice_info = hamilton_voice.get_voice_info()
        if "error" in voice_info:
            logger.error(f"Error en motor de voz: {voice_info['error']}")
            return False
        else:
            logger.info(f"Motor de voz configurado: {voice_info}")
        
        # Verificar reconocimiento facial
        if len(hamilton_face.known_face_encodings) == 0:
            logger.warning("No hay caras registradas. Use el modo de registro.")
            print("\n" + "="*60)
            print("AVISO: No hay caras registradas para el señor Ibero")
            print("Para registrar su cara, ejecute: python register_face.py")
            print("="*60 + "\n")
        else:
            logger.info(f"Caras registradas: {len(hamilton_face.known_face_encodings)}")
        
        logger.info("Verificaciones de inicio completadas")
        return True
    
    def start_wake_word_detection(self):
        """Inicia detección de palabra de activación"""
        logger.info("Iniciando detección de palabra de activación...")
        
        def wake_word_callback(text):
            if hamilton_voice.is_wake_word(text) and not self.hamilton.is_active:
                logger.info(f"Palabra de activación detectada: {text}")
                self.hamilton.wake_up()
        
        hamilton_voice.start_continuous_listening(wake_word_callback)
        logger.info("Detección de palabra de activación activa")
    
    def run(self):
        """Ejecuta la aplicación principal"""
        logger.info("="*60)
        logger.info("HAMILTON AI ASSISTANT - INICIANDO")
        logger.info(f"Versión: {settings.VERSION}")
        logger.info(f"Usuario autorizado: {settings.AUTHORIZED_USER}")
        logger.info("="*60)
        
        # Verificaciones de inicio
        if not self.startup_checks():
            logger.error("Fallo en verificaciones de inicio")
            return False
        
        # Mensaje de bienvenida
        print("\n" + "="*60)
        print("🤖 HAMILTON AI ASSISTANT")
        print("="*60)
        print("Hamilton está iniciando...")
        print("Para activar a Hamilton, diga: 'Hamilton' o 'Hey Hamilton'")
        print("Para salir, presione Ctrl+C")
        print("="*60 + "\n")
        
        # Configurar callbacks
        self.hamilton.on_wake_callback = self.on_hamilton_wake
        self.hamilton.on_sleep_callback = self.on_hamilton_sleep
        self.hamilton.on_authentication_callback = self.on_authentication
        self.hamilton.on_command_callback = self.on_command
        
        # Iniciar aplicación
        self.running = True
        
        # Mensaje de voz inicial
        hamilton_voice.speak("Hamilton iniciando. Esperando palabra de activación.")
        
        # Iniciar detección de palabra de activación
        self.start_wake_word_detection()
        
        try:
            # Loop principal
            while self.running:
                time.sleep(1)
                
                # Mostrar estado cada 30 segundos si está en debug
                if settings.DEBUG and int(time.time()) % 30 == 0:
                    status = self.hamilton.get_status()
                    logger.debug(f"Estado Hamilton: {status}")
        
        except KeyboardInterrupt:
            logger.info("Interrupción de teclado recibida")
        except Exception as e:
            logger.error(f"Error inesperado: {e}")
        finally:
            self.shutdown()
        
        return True
    
    def on_hamilton_wake(self):
        """Callback cuando Hamilton despierta"""
        print("\n🟢 Hamilton está ACTIVO y autenticado")
        print("Escuchando comandos de voz...")
    
    def on_hamilton_sleep(self):
        """Callback cuando Hamilton se duerme"""
        print("\n🔴 Hamilton está DORMIDO")
        print("Esperando palabra de activación...")
    
    def on_authentication(self, success: bool, user: str, confidence: float):
        """Callback de autenticación"""
        if success:
            print(f"\n✅ Usuario autenticado: {user} (confianza: {confidence:.2f})")
        else:
            print(f"\n❌ Fallo en autenticación: {user}")
    
    def on_command(self, command: str, response: str):
        """Callback de comando procesado"""
        print(f"\n👤 Usuario: {command}")
        print(f"🤖 Hamilton: {response}")
    
    def shutdown(self):
        """Cierre limpio de la aplicación"""
        if not self.running:
            return
        
        logger.info("Iniciando cierre de Hamilton...")
        self.running = False
        
        # Dormir Hamilton si está activo
        if self.hamilton.is_active:
            self.hamilton.sleep()
        
        # Detener escucha de voz
        hamilton_voice.stop_continuous_listening()
        
        print("\n" + "="*60)
        print("🤖 Hamilton se ha desconectado")
        print("¡Hasta luego, señor Ibero!")
        print("="*60)
        
        logger.info("Hamilton desconectado exitosamente")

    def process_input(self, user_input: str) -> str:
        """Procesa la entrada del usuario usando múltiples motores de IA"""
        try:
            # Obtener contexto del usuario
            context = self.get_user_context()
            
            # Lista de motores a probar en orden
            engines = [
                (self.gpt, settings.ENABLE_GPT),
                (self.anthropic, settings.ENABLE_ANTHROPIC),
                (self.hf, settings.ENABLE_HUGGINGFACE),
                (self.local_llm, True)  # Siempre habilitado como fallback
            ]
            
            best_response = None
            best_quality = 0.0
            
            # Probar cada motor y evaluar la calidad
            for engine, enabled in engines:
                if enabled and hasattr(engine, 'client') and engine.client:
                    response = engine.process_query(user_input, context)
                    if "error" not in response.lower():
                        # Evaluar calidad de la respuesta
                        quality_metrics = self.evaluator.evaluate_response(
                            user_input, response, context
                        )
                        quality_score = sum(quality_metrics.values()) / len(quality_metrics)
                        
                        # Actualizar mejor respuesta si es necesario
                        if quality_score > best_quality:
                            best_response = response
                            best_quality = quality_score
            
            # Si no se encontró una respuesta válida, usar el modelo local
            if not best_response:
                best_response = self.local_llm.process_query(user_input, context)
            
            return best_response
            
        except Exception as e:
            logger.error(f"Error procesando entrada: {e}")
            return "Lo siento, hubo un error procesando tu entrada."
    
    def get_user_context(self) -> Dict[str, Any]:
        """Obtiene el contexto del usuario"""
        return {
            "user_id": self.current_user,
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "preferences": self.get_user_preferences()
        }
    
    def update_conversation_history(self, user_input: str, response: str):
        """Actualiza el historial de conversación"""
        self.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "response": response
        })

    def get_ai_status(self) -> Dict[str, Any]:
        """Obtiene el estado de los motores de IA"""
        return {
            "gpt": {
                "enabled": settings.ENABLE_GPT,
                "initialized": bool(self.gpt.client),
                "model": self.gpt.model
            },
            "anthropic": {
                "enabled": settings.ENABLE_ANTHROPIC,
                "initialized": bool(self.anthropic.client),
                "model": self.anthropic.model
            },
            "huggingface": {
                "enabled": settings.ENABLE_HUGGINGFACE,
                "initialized": bool(self.hf.model),
                "model": self.hf.model_name
            },
            "local": {
                "enabled": True,
                "learning_stats": self.local_llm.get_learning_stats()
            },
            "quality_metrics": self.evaluator.get_quality_metrics()
        }

def main():
    """Función principal"""
    try:
        app = HamiltonApplication()
        success = app.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Error fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
