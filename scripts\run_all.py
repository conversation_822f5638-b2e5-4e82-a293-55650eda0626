#!/usr/bin/env python3
"""
Script para ejecutar todas las tareas del sistema.
"""

import os
import sys
import time
import json
import logging
import subprocess
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/system.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'tests', 'analysis', 'interfaces/dashboard']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivos necesarios
        required_files = [
            'requirements.txt',
            'config/settings.py',
            'logs/metrics.json'
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                logger.error(f"Archivo no encontrado: {file_path}")
                return False

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def run_script(script_name, *args):
    """Ejecuta un script de Python."""
    try:
        script_path = Path('scripts') / script_name
        if not script_path.exists():
            logger.error(f"Script no encontrado: {script_name}")
            return False

        cmd = [sys.executable, str(script_path)] + list(args)
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"Script {script_name} ejecutado exitosamente")
            return True
        else:
            logger.error(f"Error ejecutando {script_name}: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Error ejecutando {script_name}: {str(e)}")
        return False

def run_dashboard():
    """Ejecuta el dashboard en segundo plano."""
    try:
        logger.info("Iniciando dashboard...")
        dashboard_process = subprocess.Popen(
            [sys.executable, 'scripts/run_dashboard.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Esperar a que el dashboard esté listo
        time.sleep(5)
        
        if dashboard_process.poll() is None:
            logger.info("Dashboard iniciado exitosamente")
            return dashboard_process
        else:
            logger.error("Error al iniciar el dashboard")
            return None
            
    except Exception as e:
        logger.error(f"Error al ejecutar el dashboard: {str(e)}")
        return None

def run_analysis():
    """Ejecuta el análisis predictivo."""
    try:
        logger.info("Iniciando análisis predictivo...")
        return run_script('run_analysis.py')
    except Exception as e:
        logger.error(f"Error al ejecutar el análisis: {str(e)}")
        return False

def run_checks():
    """Ejecuta las verificaciones del sistema."""
    try:
        logger.info("Iniciando verificaciones del sistema...")
        return run_script('run_checks.py')
    except Exception as e:
        logger.error(f"Error al ejecutar las verificaciones: {str(e)}")
        return False

def save_system_status(status):
    """Guarda el estado del sistema."""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = Path(f'logs/system_status_{timestamp}.json')
        
        with open(output_file, 'w') as f:
            json.dump(status, f, indent=2)
            
        logger.info(f"Estado del sistema guardado en {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error al guardar el estado del sistema: {str(e)}")
        return False

def run_all():
    """Ejecuta todas las tareas del sistema."""
    try:
        start_time = datetime.now()
        logger.info(f"Iniciando proceso completo a las {start_time}")
        
        # Verificar entorno
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            return False
            
        # Ejecutar verificaciones
        if not run_checks():
            logger.error("Las verificaciones fallaron")
            return False
            
        # Ejecutar análisis
        if not run_analysis():
            logger.error("El análisis falló")
            return False
            
        # Iniciar dashboard
        dashboard_process = run_dashboard()
        if not dashboard_process:
            logger.error("No se pudo iniciar el dashboard")
            return False
            
        try:
            # Mantener el script en ejecución
            while True:
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("Deteniendo el sistema...")
            
        finally:
            # Detener el dashboard
            if dashboard_process:
                dashboard_process.terminate()
                dashboard_process.wait()
                
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Guardar estado final
        status = {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration': str(duration),
            'success': True
        }
        
        if save_system_status(status):
            logger.info(f"""
            Proceso completado:
            - Inicio: {start_time}
            - Fin: {end_time}
            - Duración: {duration}
            - Logs guardados en: logs/
            """)
            
        return True
        
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        return False

def main():
    """Función principal."""
    try:
        if run_all():
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Sistema detenido por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 