#!/usr/bin/env python3
"""
Hamilton AI Assistant - Complete Setup Script
Script de configuración completa para todas las funcionalidades
"""

import os
import sys
import json
import asyncio
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_directory_structure():
    """Crea estructura de directorios necesaria"""
    directories = [
        "config",
        "data",
        "data/translations",
        "models",
        "storage",
        "storage/conversations",
        "storage/user_data",
        "logs",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"✅ Directorio creado/verificado: {directory}")

def create_default_configurations():
    """Crea archivos de configuración por defecto"""
    
    # Configuración principal
    main_config = {
        "version": "2.0.0",
        "debug": True,
        "authorized_user": "senor_ibero",
        "language": "es",
        "voice": {
            "enabled": True,
            "engine": "pyttsx3",
            "rate": 180,
            "volume": 0.9
        },
        "face_recognition": {
            "enabled": True,
            "confidence_threshold": 0.6
        },
        "learning": {
            "enabled": True,
            "save_conversations": True,
            "adapt_responses": True
        }
    }
    
    config_file = Path("config/settings.json")
    if not config_file.exists():
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(main_config, f, indent=2, ensure_ascii=False)
        logger.info("✅ Configuración principal creada")
    
    # Configuración de base de datos
    db_config = {
        "database": {
            "type": "sqlite",
            "path": "storage/hamilton.db",
            "backup_enabled": True,
            "backup_interval_hours": 24
        },
        "tables": {
            "users": True,
            "conversations": True,
            "preferences": True,
            "learning_data": True,
            "calendar_events": True,
            "iot_devices": True
        }
    }
    
    db_config_file = Path("config/database.json")
    if not db_config_file.exists():
        with open(db_config_file, 'w', encoding='utf-8') as f:
            json.dump(db_config, f, indent=2, ensure_ascii=False)
        logger.info("✅ Configuración de base de datos creada")

def setup_prolog_knowledge_base():
    """Configura base de conocimientos Prolog"""
    prolog_dir = Path("logic/prolog_files")
    prolog_dir.mkdir(exist_ok=True)
    
    # Crear archivo de hechos básicos si no existe
    facts_file = prolog_dir / "hamilton_facts.pl"
    if not facts_file.exists():
        basic_facts = """
% Hechos básicos sobre Hamilton
asistente(hamilton).
usuario_autorizado(senor_ibero).
idioma_principal(espanol).
modo_formal(true).

% Capacidades de Hamilton
puede_ayudar_con(hamilton, informacion_general).
puede_ayudar_con(hamilton, control_hogar).
puede_ayudar_con(hamilton, calendario).
puede_ayudar_con(hamilton, conversacion).

% Preferencias por defecto
prefiere(senor_ibero, formalidad, alta).
prefiere(senor_ibero, idioma, espanol).
prefiere(senor_ibero, voz, masculina).
"""
        
        with open(facts_file, 'w', encoding='utf-8') as f:
            f.write(basic_facts)
        logger.info("✅ Base de conocimientos Prolog creada")

def create_sample_user_data():
    """Crea datos de usuario de ejemplo"""
    user_data = {
        "senor_ibero": {
            "name": "Señor Ibero",
            "preferences": {
                "language": "es",
                "formality": "high",
                "voice_type": "masculine",
                "response_style": "professional_warm"
            },
            "schedule": {
                "work_hours": {
                    "start": "09:00",
                    "end": "18:00"
                },
                "timezone": "America/Mexico_City"
            },
            "home_automation": {
                "enabled": True,
                "rooms": ["sala", "cocina", "dormitorio", "estudio"],
                "preferred_scenes": ["modo_trabajo", "modo_noche", "llegada_casa"]
            }
        }
    }
    
    user_file = Path("storage/user_data/users.json")
    if not user_file.exists():
        user_file.parent.mkdir(exist_ok=True)
        with open(user_file, 'w', encoding='utf-8') as f:
            json.dump(user_data, f, indent=2, ensure_ascii=False)
        logger.info("✅ Datos de usuario de ejemplo creados")

def setup_logging_configuration():
    """Configura sistema de logging"""
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
            },
            "detailed": {
                "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "standard",
                "stream": "ext://sys.stdout"
            },
            "file": {
                "class": "logging.FileHandler",
                "level": "DEBUG",
                "formatter": "detailed",
                "filename": "logs/hamilton.log",
                "mode": "a"
            }
        },
        "loggers": {
            "": {
                "handlers": ["console", "file"],
                "level": "DEBUG",
                "propagate": False
            }
        }
    }
    
    log_config_file = Path("config/logging.json")
    if not log_config_file.exists():
        with open(log_config_file, 'w', encoding='utf-8') as f:
            json.dump(log_config, f, indent=2, ensure_ascii=False)
        logger.info("✅ Configuración de logging creada")

async def initialize_all_systems():
    """Inicializa todos los sistemas de Hamilton"""
    logger.info("🚀 Inicializando todos los sistemas de Hamilton...")
    
    try:
        # Importar y configurar sistemas principales
        from storage.database_manager import hamilton_db
        from logic.prolog_engine import hamilton_prolog
        from ai.learning_engine_simple import hamilton_learning
        
        # Inicializar base de datos
        logger.info("📊 Inicializando base de datos...")
        # hamilton_db ya se inicializa automáticamente
        
        # Inicializar Prolog
        logger.info("🧠 Inicializando motor Prolog...")
        # hamilton_prolog ya se inicializa automáticamente
        
        # Inicializar sistemas de integración
        logger.info("🏠 Inicializando sistemas de automatización...")
        from integrations.iot_controller import hamilton_iot
        from integrations.home_automation import hamilton_automation
        from integrations.calendar_manager import hamilton_calendar
        from integrations.multilingual_system import hamilton_multilingual
        
        # Descubrir dispositivos IoT (simulados)
        devices = await hamilton_iot.discover_devices()
        logger.info(f"🔍 Dispositivos IoT descubiertos: {len(devices)}")
        
        # Inicializar sistemas de IA
        logger.info("🤖 Inicializando sistemas de IA...")
        from ai.local_models import hamilton_local_models
        from ai.response_generator import hamilton_response_generator
        from ai.reinforcement_learning import hamilton_rl_agent
        
        # Intentar inicializar modelos locales
        try:
            await hamilton_local_models.initialize_models()
            logger.info("✅ Modelos locales inicializados")
        except Exception as e:
            logger.warning(f"⚠️ Modelos locales no disponibles: {e}")
        
        logger.info("🎉 ¡Todos los sistemas inicializados exitosamente!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error inicializando sistemas: {e}")
        return False

def create_startup_script():
    """Crea script de inicio para Hamilton"""
    startup_script = '''#!/usr/bin/env python3
"""
Hamilton AI Assistant - Startup Script
Script de inicio principal para Hamilton
"""

import sys
import asyncio
import logging
from pathlib import Path

# Agregar directorio actual al path
sys.path.insert(0, str(Path(__file__).parent))

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)

async def main():
    """Función principal de inicio"""
    print("🤖 HAMILTON AI ASSISTANT")
    print("=" * 40)
    print("Iniciando sistema completo...")
    
    try:
        # Importar núcleo principal
        from core.hamilton_core import hamilton
        
        # Inicializar Hamilton
        print("🚀 Inicializando Hamilton...")
        hamilton.initialize()
        
        # Activar Hamilton
        print("⚡ Activando Hamilton...")
        hamilton.activate()
        
        print("✅ Hamilton está listo y operativo!")
        print("💬 Puede comenzar a interactuar con Hamilton.")
        print("🛑 Presione Ctrl+C para detener el sistema.")
        
        # Mantener el sistema activo
        while hamilton.is_active:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\\n👋 Deteniendo Hamilton...")
        if 'hamilton' in locals():
            hamilton.shutdown()
        print("✅ Hamilton detenido correctamente.")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    startup_file = Path("start_hamilton.py")
    with open(startup_file, 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    # Hacer ejecutable en sistemas Unix
    if sys.platform != 'win32':
        os.chmod(startup_file, 0o755)
    
    logger.info("✅ Script de inicio creado: start_hamilton.py")

def create_requirements_file():
    """Crea archivo de dependencias"""
    requirements = [
        "# Hamilton AI Assistant - Dependencias",
        "",
        "# Dependencias básicas",
        "numpy>=1.21.0",
        "opencv-python>=4.5.0",
        "face-recognition>=1.3.0",
        "pyttsx3>=2.90",
        "SpeechRecognition>=3.8.1",
        "pyaudio>=0.2.11",
        "",
        "# Base de datos",
        "sqlite3",  # Incluido en Python estándar
        "",
        "# Prolog (opcional)",
        "pyswip>=0.2.10",
        "",
        "# IA y ML (opcional)",
        "openai>=1.0.0",
        "transformers>=4.20.0",
        "torch>=1.12.0",
        "",
        "# Modelos locales (opcional)",
        "# ollama",
        "# whisper",
        "",
        "# IoT y automatización (opcional)",
        "requests>=2.28.0",
        "asyncio",
        "",
        "# Utilidades",
        "python-dateutil>=2.8.0",
        "pathlib",
        "json",
        "logging"
    ]
    
    req_file = Path("requirements.txt")
    with open(req_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(requirements))
    
    logger.info("✅ Archivo requirements.txt creado")

async def main():
    """Función principal de configuración"""
    print("🔧 HAMILTON AI ASSISTANT - CONFIGURACIÓN COMPLETA")
    print("=" * 60)
    
    try:
        # 1. Crear estructura de directorios
        print("\n1. 📁 Creando estructura de directorios...")
        create_directory_structure()
        
        # 2. Crear configuraciones
        print("\n2. ⚙️ Creando configuraciones...")
        create_default_configurations()
        
        # 3. Configurar Prolog
        print("\n3. 🧠 Configurando base de conocimientos...")
        setup_prolog_knowledge_base()
        
        # 4. Crear datos de usuario
        print("\n4. 👤 Creando datos de usuario...")
        create_sample_user_data()
        
        # 5. Configurar logging
        print("\n5. 📝 Configurando sistema de logging...")
        setup_logging_configuration()
        
        # 6. Inicializar sistemas
        print("\n6. 🚀 Inicializando sistemas...")
        success = await initialize_all_systems()
        
        # 7. Crear scripts de utilidad
        print("\n7. 📜 Creando scripts de utilidad...")
        create_startup_script()
        create_requirements_file()
        
        # Resumen final
        print("\n" + "=" * 60)
        print("✅ CONFIGURACIÓN COMPLETADA EXITOSAMENTE")
        print("=" * 60)
        
        if success:
            print("🎉 ¡Hamilton está completamente configurado y listo para usar!")
            print("\n📋 PRÓXIMOS PASOS:")
            print("1. Ejecutar: python start_hamilton.py")
            print("2. O ejecutar: python test_complete_system.py (para pruebas)")
            print("3. Personalizar configuraciones en el directorio 'config/'")
            print("4. Instalar dependencias opcionales según necesidades")
        else:
            print("⚠️ Configuración completada con algunas limitaciones")
            print("Revise los logs para más detalles")
        
        return success
        
    except Exception as e:
        print(f"❌ Error durante la configuración: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Configuración interrumpida por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
