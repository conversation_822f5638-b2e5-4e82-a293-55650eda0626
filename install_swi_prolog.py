#!/usr/bin/env python3
"""
Hamilton AI Assistant - SWI-Prolog Installer
Script para instalar SWI-Prolog en Windows
"""

import os
import sys
import subprocess
import requests
import tempfile
from pathlib import Path
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_file(url: str, filename: str) -> bool:
    """Descarga un archivo desde una URL"""
    try:
        print(f"📥 Descargando {filename}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ {filename} descargado exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error descargando {filename}: {e}")
        return False

def install_swi_prolog_windows():
    """Instala SWI-Prolog en Windows"""
    print("🧠 INSTALANDO SWI-PROLOG PARA WINDOWS")
    print("=" * 40)
    
    # URL de descarga para Windows 64-bit
    swi_prolog_url = "https://www.swi-prolog.org/download/stable/bin/swipl-9.0.4-1.x64.exe"
    
    with tempfile.TemporaryDirectory() as temp_dir:
        installer_path = Path(temp_dir) / "swipl_installer.exe"
        
        # Descargar instalador
        if not download_file(swi_prolog_url, installer_path):
            return False
        
        print("🚀 Ejecutando instalador...")
        print("NOTA: Se abrirá el instalador de SWI-Prolog.")
        print("Por favor, siga las instrucciones del instalador.")
        
        try:
            # Ejecutar instalador
            result = subprocess.run([str(installer_path)], check=False)
            
            if result.returncode == 0:
                print("✅ SWI-Prolog instalado exitosamente")
                return True
            else:
                print("⚠️ El instalador se cerró. Verifique si la instalación fue exitosa.")
                return True  # Asumir éxito ya que el usuario puede haber cancelado
                
        except Exception as e:
            print(f"❌ Error ejecutando instalador: {e}")
            return False

def install_pyswip():
    """Instala PySwip para integración con Python"""
    print("\n🐍 INSTALANDO PYSWIP")
    print("-" * 30)
    
    try:
        # Intentar instalar PySwip
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pyswip"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PySwip instalado exitosamente")
            return True
        else:
            print(f"❌ Error instalando PySwip: {result.stderr}")
            
            # Intentar instalación alternativa
            print("🔄 Intentando instalación alternativa...")
            result2 = subprocess.run([
                sys.executable, "-m", "pip", "install", "git+https://github.com/yuce/pyswip.git"
            ], capture_output=True, text=True)
            
            if result2.returncode == 0:
                print("✅ PySwip instalado desde repositorio")
                return True
            else:
                print(f"❌ Error en instalación alternativa: {result2.stderr}")
                return False
                
    except Exception as e:
        print(f"❌ Error instalando PySwip: {e}")
        return False

def test_prolog_installation():
    """Prueba la instalación de Prolog"""
    print("\n🧪 PROBANDO INSTALACIÓN")
    print("-" * 30)
    
    try:
        # Probar comando swipl
        result = subprocess.run(["swipl", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ SWI-Prolog está disponible en PATH")
            print(f"Versión: {result.stdout.strip()}")
            
            # Probar PySwip
            try:
                from pyswip import Prolog
                prolog = Prolog()
                
                # Prueba simple
                prolog.assertz("padre(juan, pedro)")
                prolog.assertz("padre(pedro, maria)")
                
                results = list(prolog.query("padre(juan, X)"))
                
                if results:
                    print("✅ PySwip funciona correctamente")
                    print(f"Prueba exitosa: {results}")
                    return True
                else:
                    print("⚠️ PySwip instalado pero no funciona correctamente")
                    return False
                    
            except ImportError:
                print("❌ PySwip no está disponible")
                return False
            except Exception as e:
                print(f"❌ Error probando PySwip: {e}")
                return False
        else:
            print("❌ SWI-Prolog no está disponible en PATH")
            print("Verifique que la instalación fue exitosa y reinicie la terminal")
            return False
            
    except FileNotFoundError:
        print("❌ Comando 'swipl' no encontrado")
        print("SWI-Prolog no está instalado o no está en PATH")
        return False
    except subprocess.TimeoutExpired:
        print("⚠️ Timeout probando SWI-Prolog")
        return False
    except Exception as e:
        print(f"❌ Error probando instalación: {e}")
        return False

def setup_prolog_for_hamilton():
    """Configura Prolog específicamente para Hamilton"""
    print("\n⚙️ CONFIGURANDO PROLOG PARA HAMILTON")
    print("-" * 40)
    
    try:
        # Crear directorio de archivos Prolog si no existe
        prolog_dir = Path("logic/prolog_files")
        prolog_dir.mkdir(parents=True, exist_ok=True)
        
        # Crear archivo de configuración
        config_file = Path("config/prolog_config.json")
        config = {
            "enabled": True,
            "swipl_path": "swipl",
            "knowledge_base_path": "logic/prolog_files/hamilton_knowledge.pl",
            "facts_file": "logic/prolog_files/hamilton_facts.pl",
            "rules_file": "logic/prolog_files/hamilton_rules.pl",
            "auto_load": True,
            "debug_mode": False
        }
        
        config_file.parent.mkdir(exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Configuración creada: {config_file}")
        
        # Crear archivo de conocimientos básico
        knowledge_file = prolog_dir / "hamilton_knowledge.pl"
        if not knowledge_file.exists():
            knowledge_content = """
% Hamilton AI Assistant - Base de Conocimientos
% Archivo principal de conocimientos de Hamilton

% Cargar otros archivos
:- consult('hamilton_facts.pl').
:- consult('hamilton_rules.pl').

% Predicados principales de Hamilton
hamilton_version('2.0.0').
hamilton_status(active).

% Capacidades de Hamilton
capability(hamilton, voice_recognition).
capability(hamilton, face_recognition).
capability(hamilton, home_automation).
capability(hamilton, calendar_management).
capability(hamilton, multilingual_support).
capability(hamilton, emotion_recognition).
capability(hamilton, learning).

% Reglas de razonamiento
can_help_with(hamilton, Topic) :-
    capability(hamilton, Topic).

should_respond(hamilton, User) :-
    authorized_user(User).

% Análisis de intenciones
analyze_intention(User, Text, Intention) :-
    authorized_user(User),
    contains_keywords(Text, Keywords),
    map_keywords_to_intention(Keywords, Intention).

% Mapeo de palabras clave a intenciones
contains_keywords(Text, information) :-
    (sub_string(Text, _, _, _, "información");
     sub_string(Text, _, _, _, "info");
     sub_string(Text, _, _, _, "qué");
     sub_string(Text, _, _, _, "cuál")).

contains_keywords(Text, greeting) :-
    (sub_string(Text, _, _, _, "hola");
     sub_string(Text, _, _, _, "buenos");
     sub_string(Text, _, _, _, "buenas")).

contains_keywords(Text, action) :-
    (sub_string(Text, _, _, _, "hacer");
     sub_string(Text, _, _, _, "ejecutar");
     sub_string(Text, _, _, _, "activar")).

map_keywords_to_intention(information, obtener_informacion).
map_keywords_to_intention(greeting, conversacion_casual).
map_keywords_to_intention(action, solicitar_accion).
"""
            
            with open(knowledge_file, 'w', encoding='utf-8') as f:
                f.write(knowledge_content)
            
            print(f"✅ Base de conocimientos creada: {knowledge_file}")
        
        print("✅ Prolog configurado para Hamilton")
        return True
        
    except Exception as e:
        print(f"❌ Error configurando Prolog: {e}")
        return False

def main():
    """Función principal"""
    print("🧠 HAMILTON - INSTALADOR DE SWI-PROLOG")
    print("=" * 50)
    
    if sys.platform != "win32":
        print("❌ Este script está diseñado para Windows")
        print("Para Linux/Mac, instale SWI-Prolog usando el gestor de paquetes:")
        print("  Ubuntu/Debian: sudo apt-get install swi-prolog")
        print("  macOS: brew install swi-prolog")
        return False
    
    success = True
    
    # Verificar si ya está instalado
    try:
        result = subprocess.run(["swipl", "--version"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ SWI-Prolog ya está instalado")
            print(f"Versión: {result.stdout.strip()}")
        else:
            # Instalar SWI-Prolog
            if not install_swi_prolog_windows():
                success = False
    except FileNotFoundError:
        # Instalar SWI-Prolog
        if not install_swi_prolog_windows():
            success = False
    
    # Instalar PySwip
    if success:
        if not install_pyswip():
            success = False
    
    # Probar instalación
    if success:
        if not test_prolog_installation():
            print("\n⚠️ La instalación puede no estar completa")
            print("Reinicie la terminal e intente nuevamente")
    
    # Configurar para Hamilton
    if success:
        setup_prolog_for_hamilton()
    
    # Resumen final
    print("\n" + "=" * 50)
    if success:
        print("🎉 ¡INSTALACIÓN COMPLETADA!")
        print("SWI-Prolog está listo para usar con Hamilton")
        print("\n📋 PRÓXIMOS PASOS:")
        print("1. Reinicie la terminal/IDE")
        print("2. Ejecute Hamilton para probar el razonamiento avanzado")
        print("3. Los archivos Prolog están en: logic/prolog_files/")
    else:
        print("❌ INSTALACIÓN INCOMPLETA")
        print("Algunos componentes pueden no estar funcionando correctamente")
        print("\n🔧 SOLUCIÓN DE PROBLEMAS:")
        print("1. Verifique que tiene permisos de administrador")
        print("2. Desactive temporalmente el antivirus")
        print("3. Intente la instalación manual desde: https://www.swi-prolog.org/")
    
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Instalación cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
