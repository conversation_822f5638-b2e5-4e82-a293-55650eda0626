"""
Hamilton AI Assistant - Prolog Engine
Motor de razonamiento lógico usando SWI-Prolog para Hamilton
"""

import logging
import os
import sys
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
from datetime import datetime

try:
    from pyswip import Prolog, Functor, Atom, Variable, Query
    PROLOG_AVAILABLE = True
except ImportError:
    PROLOG_AVAILABLE = False
    logging.warning("PySwip no está disponible. Funcionalidad de Prolog limitada.")

from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HamiltonPrologEngine:
    """
    Motor de razonamiento lógico de Hamilton usando Prolog
    Proporciona capacidades avanzadas de inferencia y toma de decisiones
    """
    
    def __init__(self):
        self.prolog = None
        self.knowledge_base_loaded = False
        self.facts_count = 0
        self.rules_count = 0
        self.user_preferences = {}
        self.conversation_context = []
        
        if PROLOG_AVAILABLE:
            self._initialize_prolog()
        else:
            logger.warning("Prolog no disponible - usando motor de razonamiento simplificado")
    
    def _initialize_prolog(self):
        """Inicializa el motor Prolog"""
        try:
            self.prolog = Prolog()
            logger.info("Motor Prolog inicializado exitosamente")
            
            # Cargar base de conocimientos
            self._load_knowledge_base()
            
            # Cargar hechos dinámicos
            self._load_dynamic_facts()
            
        except Exception as e:
            logger.error(f"Error inicializando Prolog: {e}")
            self.prolog = None
    
    def _load_knowledge_base(self):
        """Carga la base de conocimientos desde archivo Prolog"""
        kb_file = Path("logic/reasoning.pl")
        
        if not kb_file.exists():
            logger.info("Creando base de conocimientos inicial")
            self._create_initial_knowledge_base()
        
        try:
            if self.prolog:
                # Cargar archivo de conocimientos
                self.prolog.consult(str(kb_file))
                self.knowledge_base_loaded = True
                logger.info(f"Base de conocimientos cargada: {kb_file}")
                
                # Contar hechos y reglas
                self._count_knowledge_elements()
                
        except Exception as e:
            logger.error(f"Error cargando base de conocimientos: {e}")
    
    def _create_initial_knowledge_base(self):
        """Crea la base de conocimientos inicial de Hamilton"""
        kb_content = """
% Hamilton AI Assistant - Base de Conocimientos
% Reglas de razonamiento y hechos para el asistente personal

% ===== HECHOS BÁSICOS =====

% Usuario autorizado
usuario_autorizado(senor_ibero).
usuario_principal(senor_ibero).

% Información personal del usuario
preferencia_idioma(senor_ibero, espanol).
preferencia_voz(hamilton, masculina).
relacion(hamilton, senor_ibero, asistente_personal).

% Capacidades de Hamilton
capacidad(hamilton, reconocimiento_facial).
capacidad(hamilton, reconocimiento_voz).
capacidad(hamilton, sintesis_voz).
capacidad(hamilton, razonamiento_logico).
capacidad(hamilton, aprendizaje_continuo).

% Estados del sistema
estado_valido(dormido).
estado_valido(activo).
estado_valido(escuchando).
estado_valido(procesando).
estado_valido(hablando).

% ===== REGLAS DE RAZONAMIENTO =====

% Reglas de autenticación
puede_activar(Usuario) :-
    usuario_autorizado(Usuario),
    usuario_principal(Usuario).

% Reglas de interacción
debe_responder(hamilton, Usuario) :-
    usuario_autorizado(Usuario),
    estado_actual(hamilton, activo).

% Reglas de cortesía
saludo_apropiado(Hora, 'Buenos días') :-
    Hora >= 5, Hora < 12.
saludo_apropiado(Hora, 'Buenas tardes') :-
    Hora >= 12, Hora < 18.
saludo_apropiado(Hora, 'Buenas noches') :-
    (Hora >= 18; Hora < 5).

% Reglas de prioridad de comandos
prioridad_alta(comando_emergencia).
prioridad_alta(comando_seguridad).
prioridad_media(comando_informacion).
prioridad_media(comando_asistencia).
prioridad_baja(comando_conversacion).

% Reglas de aprendizaje
debe_recordar(Informacion) :-
    tipo_informacion(Informacion, importante).
debe_recordar(Informacion) :-
    frecuencia_uso(Informacion, alta).

% Reglas de contexto
contexto_relevante(Tema, Conversacion) :-
    menciona_tema(Conversacion, Tema),
    tiempo_reciente(Conversacion).

% ===== REGLAS DE TOMA DE DECISIONES =====

% Decidir si interrumpir
puede_interrumpir(hamilton, Actividad) :-
    prioridad_alta(Actividad).
puede_interrumpir(hamilton, Actividad) :-
    prioridad_media(Actividad),
    not(estado_actual(hamilton, ocupado)).

% Decidir nivel de detalle en respuestas
respuesta_detallada(Usuario, Tema) :-
    usuario_autorizado(Usuario),
    interes_alto(Usuario, Tema).
respuesta_detallada(Usuario, Tema) :-
    usuario_autorizado(Usuario),
    primera_vez_pregunta(Usuario, Tema).

% Reglas de seguridad
acceso_permitido(Usuario, Funcion) :-
    usuario_autorizado(Usuario),
    funcion_publica(Funcion).
acceso_permitido(Usuario, Funcion) :-
    usuario_principal(Usuario),
    funcion_privada(Funcion).

% ===== HECHOS DINÁMICOS (se actualizan en tiempo real) =====

:- dynamic estado_actual/2.
:- dynamic conversacion_activa/3.
:- dynamic preferencia_usuario/3.
:- dynamic historial_comando/4.
:- dynamic contexto_actual/2.

% Estados iniciales
estado_actual(hamilton, dormido).
contexto_actual(hamilton, inicio_sesion).

% ===== REGLAS DE INFERENCIA AVANZADA =====

% Inferir intenciones del usuario
intencion(Usuario, obtener_informacion) :-
    comando_contiene(Usuario, pregunta),
    tipo_pregunta(informativa).

intencion(Usuario, solicitar_accion) :-
    comando_contiene(Usuario, imperativo),
    accion_valida(Accion).

intencion(Usuario, conversacion_casual) :-
    comando_contiene(Usuario, saludo),
    not(comando_contiene(Usuario, pregunta)).

% Inferir estado emocional (básico)
estado_emocional(Usuario, positivo) :-
    usa_palabras_positivas(Usuario),
    tono_amigable(Usuario).

estado_emocional(Usuario, neutral) :-
    not(usa_palabras_positivas(Usuario)),
    not(usa_palabras_negativas(Usuario)).

% Reglas de personalización
respuesta_personalizada(Usuario, Respuesta) :-
    preferencia_usuario(Usuario, estilo_comunicacion, formal),
    respuesta_formal(Respuesta).

respuesta_personalizada(Usuario, Respuesta) :-
    preferencia_usuario(Usuario, estilo_comunicacion, casual),
    respuesta_casual(Respuesta).

% ===== UTILIDADES =====

% Verificar tiempo
tiempo_reciente(Timestamp) :-
    get_time(Now),
    Diff is Now - Timestamp,
    Diff < 300.  % 5 minutos

% Contar elementos
contar_hechos(Tipo, Cantidad) :-
    findall(X, call(Tipo, X), Lista),
    length(Lista, Cantidad).
"""
        
        kb_file = Path("logic/reasoning.pl")
        kb_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(kb_file, 'w', encoding='utf-8') as f:
            f.write(kb_content)
        
        logger.info("Base de conocimientos inicial creada")
    
    def _load_dynamic_facts(self):
        """Carga hechos dinámicos desde archivos de datos"""
        try:
            # Cargar preferencias del usuario
            prefs_file = Path("storage/user_preferences.json")
            if prefs_file.exists():
                with open(prefs_file, 'r', encoding='utf-8') as f:
                    self.user_preferences = json.load(f)
                    self._assert_user_preferences()
            
            # Cargar contexto de conversaciones recientes
            self._load_conversation_context()
            
        except Exception as e:
            logger.error(f"Error cargando hechos dinámicos: {e}")
    
    def _assert_user_preferences(self):
        """Aserta preferencias del usuario en Prolog"""
        if not self.prolog:
            return
        
        try:
            for user, prefs in self.user_preferences.items():
                for pref_type, value in prefs.items():
                    fact = f"preferencia_usuario({user}, {pref_type}, {value})"
                    self.prolog.assertz(fact)
            
            logger.info("Preferencias de usuario cargadas en Prolog")
            
        except Exception as e:
            logger.error(f"Error asertando preferencias: {e}")
    
    def _count_knowledge_elements(self):
        """Cuenta hechos y reglas en la base de conocimientos"""
        if not self.prolog:
            return
        
        try:
            # Contar algunos tipos de hechos
            facts_queries = [
                "usuario_autorizado(_)",
                "capacidad(hamilton, _)",
                "estado_valido(_)"
            ]
            
            total_facts = 0
            for query in facts_queries:
                results = list(self.prolog.query(query))
                total_facts += len(results)
            
            self.facts_count = total_facts
            logger.info(f"Base de conocimientos: ~{self.facts_count} hechos cargados")
            
        except Exception as e:
            logger.error(f"Error contando elementos: {e}")
    
    def query(self, query_string: str) -> List[Dict[str, Any]]:
        """
        Ejecuta una consulta Prolog
        
        Args:
            query_string: Consulta en sintaxis Prolog
            
        Returns:
            Lista de resultados
        """
        if not self.prolog:
            logger.warning("Prolog no disponible para consulta")
            return []
        
        try:
            results = []
            for solution in self.prolog.query(query_string):
                results.append(dict(solution))
            
            logger.debug(f"Consulta '{query_string}': {len(results)} resultados")
            return results
            
        except Exception as e:
            logger.error(f"Error en consulta Prolog '{query_string}': {e}")
            return []
    
    def can_user_activate(self, user: str) -> bool:
        """Verifica si un usuario puede activar Hamilton"""
        query_str = f"puede_activar({user})"
        results = self.query(query_str)
        return len(results) > 0
    
    def should_respond(self, user: str) -> bool:
        """Verifica si Hamilton debe responder al usuario"""
        # Actualizar estado actual
        self.update_system_state("hamilton", "activo")
        
        query_str = f"debe_responder(hamilton, {user})"
        results = self.query(query_str)
        return len(results) > 0
    
    def get_appropriate_greeting(self, hour: int) -> str:
        """Obtiene el saludo apropiado según la hora"""
        query_str = f"saludo_apropiado({hour}, Saludo)"
        results = self.query(query_str)
        
        if results and 'Saludo' in results[0]:
            return results[0]['Saludo']
        else:
            return "Hola"
    
    def analyze_user_intention(self, user: str, command: str) -> Optional[str]:
        """Analiza la intención del usuario basada en el comando"""
        # Primero, agregar el comando al contexto
        self.add_command_to_history(user, command)
        
        # Analizar patrones simples (expandir con NLP más adelante)
        if any(word in command.lower() for word in ['qué', 'cuál', 'cómo', 'cuándo', 'dónde']):
            self.assert_fact(f"comando_contiene({user}, pregunta)")
            self.assert_fact("tipo_pregunta(informativa)")
        
        if any(word in command.lower() for word in ['haz', 'ejecuta', 'inicia', 'para']):
            self.assert_fact(f"comando_contiene({user}, imperativo)")
        
        if any(word in command.lower() for word in ['hola', 'buenos', 'buenas']):
            self.assert_fact(f"comando_contiene({user}, saludo)")
        
        # Consultar intención
        query_str = f"intencion({user}, Intencion)"
        results = self.query(query_str)
        
        if results and 'Intencion' in results[0]:
            return results[0]['Intencion']
        
        return None
    
    def get_command_priority(self, command_type: str) -> str:
        """Obtiene la prioridad de un tipo de comando"""
        query_str = f"prioridad_alta({command_type})"
        if self.query(query_str):
            return "alta"
        
        query_str = f"prioridad_media({command_type})"
        if self.query(query_str):
            return "media"
        
        return "baja"
    
    def should_remember_information(self, information: str) -> bool:
        """Determina si Hamilton debe recordar cierta información"""
        # Simplificado - expandir con análisis más sofisticado
        self.assert_fact(f"tipo_informacion('{information}', importante)")
        
        query_str = f"debe_recordar('{information}')"
        results = self.query(query_str)
        return len(results) > 0
    
    def update_system_state(self, system: str, new_state: str):
        """Actualiza el estado del sistema en Prolog"""
        if not self.prolog:
            return
        
        try:
            # Retraer estado anterior
            self.prolog.retractall(f"estado_actual({system}, _)")
            
            # Asertar nuevo estado
            self.prolog.assertz(f"estado_actual({system}, {new_state})")
            
            logger.debug(f"Estado actualizado: {system} -> {new_state}")
            
        except Exception as e:
            logger.error(f"Error actualizando estado: {e}")
    
    def assert_fact(self, fact: str):
        """Aserta un hecho en la base de conocimientos"""
        if not self.prolog:
            return
        
        try:
            self.prolog.assertz(fact)
            logger.debug(f"Hecho asertado: {fact}")
        except Exception as e:
            logger.error(f"Error asertando hecho '{fact}': {e}")
    
    def retract_fact(self, fact: str):
        """Retrae un hecho de la base de conocimientos"""
        if not self.prolog:
            return
        
        try:
            self.prolog.retract(fact)
            logger.debug(f"Hecho retraído: {fact}")
        except Exception as e:
            logger.error(f"Error retrayendo hecho '{fact}': {e}")
    
    def add_command_to_history(self, user: str, command: str):
        """Agrega un comando al historial"""
        timestamp = int(datetime.now().timestamp())
        fact = f"historial_comando({user}, '{command}', {timestamp}, procesado)"
        self.assert_fact(fact)
    
    def _load_conversation_context(self):
        """Carga contexto de conversaciones recientes"""
        # Implementar carga de contexto desde archivos de conversación
        pass
    
    def get_reasoning_explanation(self, conclusion: str) -> str:
        """Obtiene explicación del razonamiento para una conclusión"""
        if not self.prolog:
            return "Razonamiento no disponible (Prolog no cargado)"
        
        # Implementar explicación de razonamiento
        return f"Conclusión '{conclusion}' basada en reglas lógicas de Hamilton"
    
    def get_system_info(self) -> Dict[str, Any]:
        """Retorna información del sistema de razonamiento"""
        return {
            "prolog_available": PROLOG_AVAILABLE,
            "knowledge_base_loaded": self.knowledge_base_loaded,
            "facts_count": self.facts_count,
            "rules_count": self.rules_count,
            "user_preferences_loaded": len(self.user_preferences) > 0
        }

# Instancia global del motor Prolog
hamilton_prolog = HamiltonPrologEngine()
