{"name": "hamilton-ai-assistant", "version": "1.0.0", "description": "<PERSON> - Personal AI Assistant with voice and facial recognition", "main": "interfaces/web/app.js", "scripts": {"start": "node interfaces/web/app.js", "dev": "nodemon interfaces/web/app.js", "test": "jest", "build": "webpack --mode production", "build:dev": "webpack --mode development"}, "keywords": ["ai", "assistant", "voice-recognition", "facial-recognition", "personal-assistant"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "node-cron": "^3.0.3", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}