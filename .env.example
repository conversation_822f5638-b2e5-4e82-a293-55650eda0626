# Hamilton AI Assistant - Environment Variables
# Copia este archivo como .env y configura tus valores

# OpenAI Configuration
OPENAI_API_KEY=tu_openai_api_key_aqui

# Database Configuration
DATABASE_URL=sqlite:///./hamilton.db
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=hamilton-secret-key-change-in-production

# Azure Speech Services (opcional)
AZURE_SPEECH_KEY=tu_azure_speech_key
AZURE_SPEECH_REGION=tu_region

# Google Cloud Speech (opcional)
GOOGLE_APPLICATION_CREDENTIALS=path/to/google/credentials.json

# Debug Mode
DEBUG=True

# Server Configuration
HOST=localhost
PORT=8000

# Voice Configuration - IMPORTANTE: Configuración de voz masculina
TTS_ENGINE=pyttsx3
TTS_VOICE_GENDER=male
TTS_AZURE_VOICE=es-ES-AlvaroNeural
TTS_GOOGLE_VOICE=es-ES-Standard-B
