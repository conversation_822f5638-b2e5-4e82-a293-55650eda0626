{"timestamp": "2025-06-13T22:46:33.732141", "system_info": {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.13.3", "processor": "Intel64 Family 6 Model 154 Stepping 4, GenuineIntel", "machine": "AMD64", "node": "DESKTOP-SA1RPJC"}, "user_permissions": {"is_admin": false, "dir_permissions": {"logs": {"readable": true, "writable": true, "executable": true}, "analysis": {"readable": true, "writable": true, "executable": true}, "config": {"readable": true, "writable": true, "executable": true}}}, "network_security": {"connections": [{"local_address": "fe80::28e2:1ed6:d91f:60b7:6881", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:63533", "remote_address": null, "status": "NONE", "pid": 11636}, {"local_address": "***********:1900", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "0.0.0.0:65109", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "0.0.0.0:64364", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "***********:52448", "remote_address": "***************:443", "status": "TIME_WAIT", "pid": 0}, {"local_address": "127.0.0.1:49742", "remote_address": null, "status": "LISTEN", "pid": 11120}, {"local_address": "0.0.0.0:1900", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:60055", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "127.0.0.1:49731", "remote_address": "127.0.0.1:49730", "status": "ESTABLISHED", "pid": 15044}, {"local_address": ":::2869", "remote_address": null, "status": "LISTEN", "pid": 4}, {"local_address": "127.0.0.1:44950", "remote_address": null, "status": "LISTEN", "pid": 14744}, {"local_address": "127.0.0.1:49681", "remote_address": "127.0.0.1:49682", "status": "ESTABLISHED", "pid": 1776}, {"local_address": "***********:52470", "remote_address": "*************:443", "status": "TIME_WAIT", "pid": 0}, {"local_address": "0.0.0.0:61079", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": ":::49669", "remote_address": null, "status": "LISTEN", "pid": 3508}, {"local_address": "fe80::73b9:9a00:2e7a:402a:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:60056", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "***********:64352", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:49668", "remote_address": null, "status": "LISTEN", "pid": 2376}, {"local_address": "***********:52147", "remote_address": "************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "fe80::6368:d250:fe8f:9095:6881", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "***********:50143", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "0.0.0.0:3544", "remote_address": null, "status": "NONE", "pid": 3876}, {"local_address": "0.0.0.0:6881", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "127.0.0.1:49680", "remote_address": "127.0.0.1:49679", "status": "ESTABLISHED", "pid": 1720}, {"local_address": "0.0.0.0:5432", "remote_address": null, "status": "LISTEN", "pid": 7044}, {"local_address": "***********:56740", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "0.0.0.0:51426", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "0.0.0.0:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:49667", "remote_address": null, "status": "LISTEN", "pid": 1088}, {"local_address": "fe80::28e2:1ed6:d91f:60b7:64361", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "::1:56739", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "2001:0:2841:f634:28e2:1ed6:d91f:60b7:64360", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:5353", "remote_address": null, "status": "NONE", "pid": 17716}, {"local_address": "0.0.0.0:50001", "remote_address": null, "status": "NONE", "pid": 4836}, {"local_address": ":::445", "remote_address": null, "status": "LISTEN", "pid": 4}, {"local_address": "2001:0:2841:f634:28e2:1ed6:d91f:60b7:6881", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "fe80::68ce:b58d:ea1e:cc78:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::54365", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "***********:51250", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 8748}, {"local_address": ":::5432", "remote_address": null, "status": "LISTEN", "pid": 7044}, {"local_address": ":::7070", "remote_address": null, "status": "LISTEN", "pid": 4836}, {"local_address": ":::5353", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "***********:52476", "remote_address": "***********:7680", "status": "SYN_SENT", "pid": 16800}, {"local_address": "fe80::73b9:9a00:2e7a:402a:64356", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::60056", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "::1:1900", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "fe80::6368:d250:fe8f:9095:64357", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:49669", "remote_address": null, "status": "LISTEN", "pid": 3508}, {"local_address": "0.0.0.0:49672", "remote_address": null, "status": "LISTEN", "pid": 4380}, {"local_address": "2001:0:2841:f634:28e2:1ed6:d91f:60b7:6881", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": "***********:52467", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 10592}, {"local_address": "0.0.0.0:49664", "remote_address": null, "status": "LISTEN", "pid": 1260}, {"local_address": ":::49667", "remote_address": null, "status": "LISTEN", "pid": 1088}, {"local_address": "***********:64354", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:62790", "remote_address": null, "status": "NONE", "pid": 6040}, {"local_address": "***********:52227", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "127.0.0.1:49664", "remote_address": null, "status": "NONE", "pid": 3876}, {"local_address": "0.0.0.0:64366", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:8090", "remote_address": null, "status": "LISTEN", "pid": 16780}, {"local_address": "0.0.0.0:5355", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "fe80::68ce:b58d:ea1e:cc78:64358", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "127.0.0.1:49745", "remote_address": "127.0.0.1:49736", "status": "ESTABLISHED", "pid": 11120}, {"local_address": "127.0.0.1:49736", "remote_address": "127.0.0.1:49745", "status": "ESTABLISHED", "pid": 15044}, {"local_address": "127.0.0.1:44960", "remote_address": null, "status": "LISTEN", "pid": 14744}, {"local_address": "0.0.0.0:57242", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "127.0.0.1:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::5353", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "127.0.0.1:1900", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:61858", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "***********:52455", "remote_address": "*************:443", "status": "TIME_WAIT", "pid": 0}, {"local_address": "::1:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:7070", "remote_address": null, "status": "LISTEN", "pid": 4836}, {"local_address": "***********:52472", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "***********:52479", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "0.0.0.0:49692", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "0.0.0.0:65093", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "***********:56510", "remote_address": "************:443", "status": "ESTABLISHED", "pid": 17920}, {"local_address": "***********:52465", "remote_address": "***********:443", "status": "TIME_WAIT", "pid": 0}, {"local_address": "***********:61628", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 8748}, {"local_address": "0.0.0.0:135", "remote_address": null, "status": "LISTEN", "pid": 1560}, {"local_address": "fe80::28e2:1ed6:d91f:60b7:6881", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": "fe80::6368:d250:fe8f:9095:6881", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": "0.0.0.0:6881", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": ":::61079", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "***********:52483", "remote_address": "************:443", "status": "ESTABLISHED", "pid": 17364}, {"local_address": "0.0.0.0:5353", "remote_address": null, "status": "NONE", "pid": 15384}, {"local_address": "fe80::6368:d250:fe8f:9095:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "***********:61640", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 8748}, {"local_address": ":::49672", "remote_address": null, "status": "LISTEN", "pid": 4380}, {"local_address": "2001:0:2841:f634:28e2:1ed6:d91f:60b7:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::49664", "remote_address": null, "status": "LISTEN", "pid": 1260}, {"local_address": "***********:57641", "remote_address": null, "status": "NONE", "pid": 3876}, {"local_address": "***********:49688", "remote_address": "**************:443", "status": "ESTABLISHED", "pid": 4836}, {"local_address": "***********:138", "remote_address": null, "status": "NONE", "pid": 4}, {"local_address": ":::64365", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:54113", "remote_address": null, "status": "LISTEN", "pid": 19288}, {"local_address": "0.0.0.0:7680", "remote_address": null, "status": "LISTEN", "pid": 16800}, {"local_address": "***********:52464", "remote_address": "************:443", "status": "TIME_WAIT", "pid": 0}, {"local_address": ":::5353", "remote_address": null, "status": "NONE", "pid": 17716}, {"local_address": "***********:51232", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "0.0.0.0:445", "remote_address": null, "status": "LISTEN", "pid": 4}, {"local_address": "***********:52458", "remote_address": "**************:443", "status": "TIME_WAIT", "pid": 0}, {"local_address": "127.0.0.1:56741", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "0.0.0.0:19577", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": "127.0.0.1:6463", "remote_address": null, "status": "LISTEN", "pid": 15064}, {"local_address": "127.0.0.1:49925", "remote_address": null, "status": "LISTEN", "pid": 11120}, {"local_address": "0.0.0.0:54364", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "***********:56657", "remote_address": "***************:5228", "status": "ESTABLISHED", "pid": 17920}, {"local_address": ":::49668", "remote_address": null, "status": "LISTEN", "pid": 2376}, {"local_address": "***********:1900", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::5355", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "127.0.0.1:64355", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "0.0.0.0:19576", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": "127.0.0.1:49700", "remote_address": "127.0.0.1:49701", "status": "ESTABLISHED", "pid": 4996}, {"local_address": "***********:49412", "remote_address": "***************:443", "status": "ESTABLISHED", "pid": 5704}, {"local_address": "***********:54404", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 13404}, {"local_address": "***********:62739", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 14708}, {"local_address": "127.0.0.1:49730", "remote_address": "127.0.0.1:49731", "status": "ESTABLISHED", "pid": 15044}, {"local_address": ":::135", "remote_address": null, "status": "LISTEN", "pid": 1560}, {"local_address": "127.0.0.1:49701", "remote_address": "127.0.0.1:49700", "status": "ESTABLISHED", "pid": 4996}, {"local_address": "127.0.0.1:1900", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "127.0.0.1:64353", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::7680", "remote_address": null, "status": "LISTEN", "pid": 16800}, {"local_address": "***********:52481", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "2001:0:2841:f634:28e2:1ed6:d91f:60b7:64363", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": ":::64367", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "***********:137", "remote_address": null, "status": "NONE", "pid": 4}, {"local_address": "0.0.0.0:49676", "remote_address": null, "status": "LISTEN", "pid": 1232}, {"local_address": ":::54113", "remote_address": null, "status": "LISTEN", "pid": 19288}, {"local_address": "***********:52473", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 8748}, {"local_address": "0.0.0.0:5353", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "0.0.0.0:5040", "remote_address": null, "status": "LISTEN", "pid": 8588}, {"local_address": ":::6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "fe80::6368:d250:fe8f:9095:56738", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "***********:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "***********:52480", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "***********:61675", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 8748}, {"local_address": "127.0.0.1:19292", "remote_address": null, "status": "LISTEN", "pid": 15248}, {"local_address": "127.0.0.1:49679", "remote_address": "127.0.0.1:49680", "status": "ESTABLISHED", "pid": 1720}, {"local_address": "***********:52281", "remote_address": "**************:443", "status": "ESTABLISHED", "pid": 17920}, {"local_address": "::1:64359", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "***********:51850", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "***********:52469", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "***********:64362", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "fe80::6368:d250:fe8f:9095:1900", "remote_address": null, "status": "NONE", "pid": 7480}, {"local_address": "0.0.0.0:54112", "remote_address": null, "status": "LISTEN", "pid": 19288}, {"local_address": ":::49692", "remote_address": null, "status": "NONE", "pid": 2256}, {"local_address": "***********:52468", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "***********:52471", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "fe80::28e2:1ed6:d91f:60b7:6771", "remote_address": null, "status": "NONE", "pid": 15044}, {"local_address": "127.0.0.1:49682", "remote_address": "127.0.0.1:49681", "status": "ESTABLISHED", "pid": 1776}, {"local_address": "***********:52136", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "0.0.0.0:19575", "remote_address": null, "status": "LISTEN", "pid": 15044}, {"local_address": "***********:139", "remote_address": null, "status": "LISTEN", "pid": 4}, {"local_address": "0.0.0.0:2869", "remote_address": null, "status": "LISTEN", "pid": 4}, {"local_address": "***********:52475", "remote_address": "***********:443", "status": "ESTABLISHED", "pid": 6872}, {"local_address": ":::49676", "remote_address": null, "status": "LISTEN", "pid": 1232}, {"local_address": "127.0.0.1:55412", "remote_address": null, "status": "LISTEN", "pid": 19288}, {"local_address": "***********:52463", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 8748}, {"local_address": "***********:52478", "remote_address": "***********:53", "status": "TIME_WAIT", "pid": 0}, {"local_address": "127.0.0.1:55366", "remote_address": null, "status": "LISTEN", "pid": 13572}, {"local_address": "***********:55431", "remote_address": "*************:443", "status": "ESTABLISHED", "pid": 19288}, {"local_address": "0.0.0.0:5353", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "0.0.0.0:54365", "remote_address": null, "status": "NONE", "pid": 17920}, {"local_address": "::1:49673", "remote_address": null, "status": "LISTEN", "pid": 4696}, {"local_address": "0.0.0.0:5050", "remote_address": null, "status": "NONE", "pid": 8588}, {"local_address": ":::5353", "remote_address": null, "status": "NONE", "pid": 15384}], "open_ports": ["127.0.0.1:49742", ":::2869", "127.0.0.1:44950", ":::49669", "0.0.0.0:49668", "0.0.0.0:5432", "0.0.0.0:49667", ":::445", ":::5432", ":::7070", "0.0.0.0:49669", "0.0.0.0:49672", "2001:0:2841:f634:28e2:1ed6:d91f:60b7:6881", "0.0.0.0:49664", ":::49667", "0.0.0.0:8090", "127.0.0.1:44960", "0.0.0.0:7070", "0.0.0.0:135", "fe80::28e2:1ed6:d91f:60b7:6881", "fe80::6368:d250:fe8f:9095:6881", "0.0.0.0:6881", ":::49672", ":::49664", "0.0.0.0:54113", "0.0.0.0:7680", "0.0.0.0:445", "0.0.0.0:19577", "127.0.0.1:6463", "127.0.0.1:49925", ":::49668", "0.0.0.0:19576", ":::135", ":::7680", "0.0.0.0:49676", ":::54113", "0.0.0.0:5040", "127.0.0.1:19292", "0.0.0.0:54112", "0.0.0.0:19575", "***********:139", "0.0.0.0:2869", ":::49676", "127.0.0.1:55412", "127.0.0.1:55366", "::1:49673"]}, "file_integrity": {"config/settings.py": "5fef7098e0141505bb19fa2570586b68f96e79ceb80730016252444db15bcdc6", "requirements.txt": "5ba17021be5c6a611927935e8c77f6024d7318b8ab8bf84d5236c37fcde49fd9", "main.py": "502339ae72ad6f382193c4a66e22e0bd51f83cb6f781084da8cff4722ab196a1"}, "process_security": [{"pid": 0, "name": "System Idle Process", "username": "NT AUTHORITY\\SYSTEM", "cmdline": []}, {"pid": 4, "name": "System", "username": "NT AUTHORITY\\SYSTEM", "cmdline": []}, {"pid": 100, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 140, "name": "", "username": null, "cmdline": null}, {"pid": 184, "name": "Registry", "username": null, "cmdline": null}, {"pid": 276, "name": "CastSrv.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\CastSrv.exe", "CCastServerControlInteractiveUser", "-Embedding"]}, {"pid": 416, "name": "pet.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.python-2025.6.1-win32-x64\\python-env-tools\\bin\\pet.exe", "server"]}, {"pid": 668, "name": "smss.exe", "username": null, "cmdline": null}, {"pid": 700, "name": "msedgewebview2.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "/prefetch:4", "/pfhostedapp:a30987782552d014c8eda76c90663a3bc8644a62", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView\\Crashpad", "--metrics-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "--annotation=IsOfficialBuild=1", "--annotation=channel=", "--annotation=chromium-version=137.0.7151.69", "--annotation=exe=C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--annotation=plat=Win64", "--annotation=prod=Edge WebView2", "--annotation=ver=137.0.3296.68", "--initial-client-data=0x178,0x17c,0x180,0x174,0x150,0x7ffe1489c148,0x7ffe1489c154,0x7ffe1489c160"]}, {"pid": 784, "name": "csrss.exe", "username": null, "cmdline": null}, {"pid": 1088, "name": "wininit.exe", "username": null, "cmdline": null}, {"pid": 1096, "name": "csrss.exe", "username": null, "cmdline": null}, {"pid": 1184, "name": "winlogon.exe", "username": null, "cmdline": null}, {"pid": 1232, "name": "services.exe", "username": null, "cmdline": null}, {"pid": 1240, "name": "LsaIso.exe", "username": null, "cmdline": null}, {"pid": 1260, "name": "lsass.exe", "username": null, "cmdline": null}, {"pid": 1400, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1432, "name": "fontdrvhost.exe", "username": null, "cmdline": null}, {"pid": 1440, "name": "fontdrvhost.exe", "username": null, "cmdline": null}, {"pid": 1456, "name": "WUDFHost.exe", "username": null, "cmdline": null}, {"pid": 1524, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1560, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1648, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1664, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1672, "name": "WUDFHost.exe", "username": null, "cmdline": null}, {"pid": 1720, "name": "WUDFHost.exe", "username": null, "cmdline": null}, {"pid": 1776, "name": "WUDFHost.exe", "username": null, "cmdline": null}, {"pid": 1816, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1828, "name": "WUDFHost.exe", "username": null, "cmdline": null}, {"pid": 1840, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--app-user-model-id=com.squirrel.Figma.Figma", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\resources\\app.asar", "--enable-sandbox", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=7", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=59691868954", "--field-trial-handle=2824,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=2944", "/prefetch:1"]}, {"pid": 1884, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--app-user-model-id=com.squirrel.Figma.Figma", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\resources\\app.asar", "--enable-sandbox", "--enable-blink-features=CSSAnchorPositioning", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=5", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=59691787222", "--field-trial-handle=2640,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=2636", "/prefetch:1"]}, {"pid": 1896, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1936, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 1952, "name": "dwm.exe", "username": null, "cmdline": null}, {"pid": 1968, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=gpu-process", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--gpu-preferences=UAAAAAAAAADgAAAEAAAAQAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--field-trial-handle=1908,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=1916", "/prefetch:2"]}, {"pid": 2056, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2100, "name": "NisSrv.exe", "username": null, "cmdline": null}, {"pid": 2128, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2136, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2184, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=en-US", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=4512,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4272", "/prefetch:14"]}, {"pid": 2256, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2336, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2344, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2376, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2396, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2440, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2500, "name": "IntelCpHDCPSvc.exe", "username": null, "cmdline": null}, {"pid": 2528, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2576, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2612, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2688, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2716, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "/prefetch:4", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34\\Crashpad", "--url=https://o22594.ingest.sentry.io/api/5265832/minidump/?sentry_key=303d5e72c2104327893b10231fce024a", "--annotation=_productName=Figma", "--annotation=_version=125.4.9", "--annotation=plat=Win64", "--annotation=prod=Electron", "--annotation=sentry[contexts][browser][name]=Electron", "--annotation=sentry[contexts][browser][version]=34.5.1", "--annotation=sentry[environment]=stable", "--annotation=sentry[release]=125.4.9", "--annotation=sentry[tags][initial_figma_id]=1470663932127566667", "--annotation=sentry[user][id]=8e989c452204491fbdcf948d88de1639", "--annotation=sentry[user][initial_figma_id]=1470663932127566667", "--annotation=ver=34.5.1", "--initial-client-data=0x4cc,0x4d0,0x4d4,0x4c8,0x4a0,0x7ff702124a74,0x7ff702124a80,0x7ff702124a90"]}, {"pid": 2796, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2848, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2936, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2972, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 2988, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=136", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=59818778527", "--metrics-shmem-handle=7288,i,10569825846964656610,8987111170154994502,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=11124", "/prefetch:1"]}, {"pid": 3084, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--extension-process", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=130", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=59763843984", "--metrics-shmem-handle=8248,i,1250609422565271114,14724684162739632102,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=7672", "/prefetch:9"]}, {"pid": 3096, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "c:\\Users\\<USER>\\.cursor\\extensions\\kisstkondoros.vscode-gutter-preview-0.32.2\\dist\\server.js", "--node-ipc", "--clientProcessId=19288"]}, {"pid": 3160, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=65", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=34221921057", "--metrics-shmem-handle=6004,i,5364851871794998532,8285341802689503194,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=7340", "/prefetch:1"]}, {"pid": 3164, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3204, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3212, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3280, "name": "ApplicationFrameHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\ApplicationFrameHost.exe", "-Embedding"]}, {"pid": 3304, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=33", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=11610044779", "--metrics-shmem-handle=9868,i,8714957798430667619,2848503834317021028,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=9652", "/prefetch:1"]}, {"pid": 3308, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 3412, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 3432, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3452, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=19", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=11582061537", "--metrics-shmem-handle=6148,i,12303053190589776887,11601765847611424225,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=6384", "/prefetch:1"]}, {"pid": 3460, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.vscode-pylance-2025.5.1\\dist\\server.bundle.js", "--cancellationReceive=file:23d92622a885d6e159308626645631d31ce3054ded", "--node-ipc", "--clientProcessId=13572"]}, {"pid": 3508, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3572, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3580, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3588, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3684, "name": "MemCompression", "username": null, "cmdline": null}, {"pid": 3712, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3768, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3772, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3812, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3876, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3888, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3928, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 3936, "name": "usbredirectortechssrv.exe", "username": null, "cmdline": null}, {"pid": 4128, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4136, "name": "CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\vscode-stable-user-x64\\CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.exe", "/verysilent", "/log", "/update=C:\\Users\\<USER>\\AppData\\Local\\Temp\\vscode-stable-user-x64\\CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.flag", "/nocloseapplications", "/mergetasks=runcode,!desktopicon,!quicklaunchicon"]}, {"pid": 4144, "name": "IntelAudioService.exe", "username": null, "cmdline": null}, {"pid": 4200, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4236, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4248, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "80", "--height", "30", "--signal", "0x3f8", "--server", "0x3ec"]}, {"pid": 4380, "name": "spoolsv.exe", "username": null, "cmdline": null}, {"pid": 4416, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4492, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4536, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4572, "name": "OneApp.IGCC.WinService.exe", "username": null, "cmdline": null}, {"pid": 4596, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4632, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4676, "name": "NgcIso.exe", "username": null, "cmdline": null}, {"pid": 4696, "name": "jhi_service.exe", "username": null, "cmdline": null}, {"pid": 4700, "name": "ipf_uf.exe", "username": null, "cmdline": null}, {"pid": 4820, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4828, "name": "armsvc.exe", "username": null, "cmdline": null}, {"pid": 4836, "name": "AnyDesk.exe", "username": null, "cmdline": ["C:\\Program Files (x86)\\AnyDesk\\AnyDesk.exe", "--service"]}, {"pid": 4844, "name": "remoting_host.exe", "username": null, "cmdline": null}, {"pid": 4860, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4908, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4940, "name": "DAX3API.exe", "username": null, "cmdline": null}, {"pid": 4964, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4992, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 4996, "name": "ipfsvc.exe", "username": null, "cmdline": null}, {"pid": 5028, "name": "ElevocControlService.exe", "username": null, "cmdline": null}, {"pid": 5148, "name": "LenovoUtilityService.exe", "username": null, "cmdline": null}, {"pid": 5164, "name": "LNBITSSvc.exe", "username": null, "cmdline": null}, {"pid": 5208, "name": "WsNativePushService.exe", "username": null, "cmdline": null}, {"pid": 5264, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=gpu-process", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--gpu-preferences=UAAAAAAAAADgAAAMAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--field-trial-handle=1732,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=1772", "/prefetch:2"]}, {"pid": 5320, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 5372, "name": "WhatsApp.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\5319275A.WhatsAppDesktop_2.2523.1.0_x64__cv1g1gvanyjgm\\WhatsApp.exe", "-ServerName:App.AppXkf4yh0averk473g9chjmra34tgccdh3d.mca"]}, {"pid": 5448, "name": "pg_ctl.exe", "username": null, "cmdline": null}, {"pid": 5476, "name": "RtkBtManServ.exe", "username": null, "cmdline": null}, {"pid": 5520, "name": "SessionService.exe", "username": null, "cmdline": null}, {"pid": 5528, "name": "RtkAudUService64.exe", "username": null, "cmdline": null}, {"pid": 5568, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 5592, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 5644, "name": "WMIRegistrationService.exe", "username": null, "cmdline": null}, {"pid": 5704, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 5732, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 5740, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 5748, "name": "wslservice.exe", "username": null, "cmdline": null}, {"pid": 5764, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "ClipboardSvcGroup", "-p", "-s", "cbdhsvc"]}, {"pid": 6040, "name": "remoting_host.exe", "username": null, "cmdline": ["C:\\Program Files (x86)\\Google\\Chrome Remote Desktop\\137.0.7151.5\\remoting_host.exe", "--type=host", "--mojo-pipe-token=16740530567196146446", "--crash-server-pipe-handle=1080", "--mojo-platform-channel-handle=1088"]}, {"pid": 6052, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 6088, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "202", "--height", "11", "--signal", "0x5e0", "--server", "0x548"]}, {"pid": 6164, "name": "SearchIndexer.exe", "username": null, "cmdline": null}, {"pid": 6184, "name": "ipf_helper.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\DriverStore\\FileRepository\\ipf_cpu.inf_amd64_bef44694f882994d\\ipf_helper.exe"]}, {"pid": 6260, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 6496, "name": "DAX3API.exe", "username": null, "cmdline": null}, {"pid": 6680, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--app-user-model-id=Microsoft.VisualStudioCode", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app", "--enable-sandbox", "--enable-blink-features=HighlightAPI", "--disable-blink-features=FontMatchingCTMigration,StandardizedBrowserZoom,", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=4", "--time-ticks-at-unix-epoch=-1749796178865395", "--launch-time-ticks=41078975675", "--field-trial-handle=3128,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3124", "--vscode-window-config=vscode:967ab6ea-c656-49d4-8ad6-8b9cd1ada6c8", "/prefetch:1"]}, {"pid": 6688, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=18", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=11582062806", "--metrics-shmem-handle=6168,i,4479273592071605342,15803067710010686905,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=6188", "/prefetch:1"]}, {"pid": 6740, "name": "WmiPrvSE.exe", "username": null, "cmdline": null}, {"pid": 6872, "name": "FileCoAuth.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\OneDrive\\25.095.0518.0002\\FileCoAuth.exe", "-Embedding"]}, {"pid": 6904, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--pdf-renderer", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--js-flags=--jitless", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=43", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=14212273305", "--metrics-shmem-handle=7736,i,11764992515159433253,16172259494337928881,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=8736", "/prefetch:1"]}, {"pid": 6964, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 7044, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 7112, "name": "conhost.exe", "username": null, "cmdline": null}, {"pid": 7144, "name": "Discord.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\Discord.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\discord", "/prefetch:4", "--no-rate-limit", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Roaming\\discord\\Crashpad", "--url=https://f.a.k/e", "--annotation=_productName=discord", "--annotation=_version=1.0.9195", "--annotation=plat=Win64", "--annotation=prod=Electron", "--annotation=ver=35.3.0", "--initial-client-data=0x4e0,0x4e4,0x4e8,0x4d8,0x4ec,0x7ff7f159a064,0x7ff7f159a070,0x7ff7f159a080"]}, {"pid": 7256, "name": "sihost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["sihost.exe"]}, {"pid": 7260, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=21", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=11581735156", "--metrics-shmem-handle=4136,i,15741205106793020278,3404350960731216438,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=2852", "/prefetch:1"]}, {"pid": 7332, "name": "unsecapp.exe", "username": null, "cmdline": null}, {"pid": 7388, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "UnistackSvcGroup"]}, {"pid": 7480, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 7508, "name": "AutoModeDetect.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\AutoModeDetect.exe"]}, {"pid": 7536, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\??\\C:\\WINDOWS\\system32\\conhost.exe", "0x4"]}, {"pid": 7556, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 7632, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 7640, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 7664, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "LocalSystemNetworkRestricted", "-p", "-s", "webthreatdefusersvc"]}, {"pid": 7712, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "UnistackSvcGroup", "-s", "CDPUserSvc"]}, {"pid": 7740, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "BthAppGroup", "-p", "-s", "BluetoothUserService"]}, {"pid": 7812, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "UnistackSvcGroup", "-s", "WpnUserService"]}, {"pid": 7816, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 7824, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 7832, "name": "postgres.exe", "username": null, "cmdline": null}, {"pid": 8140, "name": "CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.tmp", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\is-7TFAA.tmp\\CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.tmp", "/SL5=$6067E,109941604,841216,C:\\Users\\<USER>\\AppData\\Local\\Temp\\vscode-stable-user-x64\\CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.exe", "/verysilent", "/log", "/update=C:\\Users\\<USER>\\AppData\\Local\\Temp\\vscode-stable-user-x64\\CodeSetup-stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1.flag", "/nocloseapplications", "/mergetasks=runcode,!desktopicon,!quicklaunchicon"]}, {"pid": 8160, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "80", "--height", "30", "--signal", "0x454", "--server", "0x44c"]}, {"pid": 8224, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 8236, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=63", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=34075561010", "--metrics-shmem-handle=11004,i,16301239872440375999,763646848260482386,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=10552", "/prefetch:1"]}, {"pid": 8312, "name": "taskhostw.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["taskhostw.exe", "{222A245B-E637-4AE9-A93F-A59CA119A75E}"]}, {"pid": 8428, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "/prefetch:4", "--no-rate-limit", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\Crashpad", "--url=https://f.a.k/e", "--annotation=_productName=Cursor", "--annotation=_version=1.0.0", "--annotation=plat=Win64", "--annotation=prod=Electron", "--annotation=ver=34.5.1", "--initial-client-data=0x6b8,0x6cc,0x6c8,0x6c0,0x6bc,0x7ff6f2174a74,0x7ff6f2174a80,0x7ff6f2174a90"]}, {"pid": 8520, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 8568, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 8580, "name": "explorer.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\Explorer.EXE"]}, {"pid": 8588, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 8704, "name": "ShellHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\ShellHost.exe"]}, {"pid": 8748, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=es", "--service-sandbox-type=none", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--field-trial-handle=2008,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=2004", "/prefetch:11"]}, {"pid": 8828, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 8836, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 8976, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 9064, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\html-language-features\\server\\dist\\node\\htmlServerMain", "--node-ipc", "--clientProcessId=19288"]}, {"pid": 9068, "name": "CrossDeviceResume.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\SystemApps\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\CrossDeviceResume.exe", "/tileid", "MicrosoftWindows.Client.CBS_cw5n1h2txyewy!CrossDeviceResumeApp"]}, {"pid": 9108, "name": "OneDrive.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\OneDrive\\OneDrive.exe", "/background"]}, {"pid": 9112, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 9200, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=es", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=3808,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3800", "/prefetch:14"]}, {"pid": 9348, "name": "Widgets.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\MicrosoftWindows.Client.WebExperience_525.10401.30.0_x64__cw5n1h2txyewy\\Dashboard\\Widgets.exe", "-ServerName:Microsoft.Windows.DashboardServer"]}, {"pid": 9392, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 9492, "name": "SearchHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\SystemApps\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\SearchHost.exe", "-ServerName:CortanaUI.AppXstmwaab17q5s3y22tp6apqz7a45vwv65.mca"]}, {"pid": 9616, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 9684, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "c:\\Users\\<USER>\\.vscode\\extensions\\visualstudioexptteam.intellicode-api-usage-examples-0.2.9\\dist\\server\\server.js", "--node-ipc", "--clientProcessId=13572"]}, {"pid": 9776, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--app-user-model-id=com.squirrel.Figma.Figma", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\resources\\app.asar", "--enable-sandbox", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=8", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=59706812726", "--field-trial-handle=4068,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4080", "/prefetch:1"]}, {"pid": 9876, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=es", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=3860,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3856", "/prefetch:14"]}, {"pid": 10000, "name": "StartMenuExperienceHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\SystemApps\\Microsoft.Windows.StartMenuExperienceHost_cw5n1h2txyewy\\StartMenuExperienceHost.exe", "-ServerName:App.AppXywbrabmsek0gm3tkwpr5kwzbs55tkqay.mca"]}, {"pid": 10056, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe"]}, {"pid": 10060, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 10284, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 10316, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\json-language-features\\server\\dist\\node\\jsonServerMain", "--node-ipc", "--clientProcessId=13572"]}, {"pid": 10332, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "UdkSvcGroup", "-s", "UdkUserSvc"]}, {"pid": 10368, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=62", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=34068785538", "--metrics-shmem-handle=10180,i,2241843465880643931,2295144994167231722,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=5552", "/prefetch:1"]}, {"pid": 10408, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 10500, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=utility", "--utility-sub-type=video_capture.mojom.VideoCaptureService", "--lang=en-US", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--no-pre-read-main-dll", "--metrics-shmem-handle=6660,i,9193448111736162429,10287211377901522266,524288", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=6724", "/prefetch:14"]}, {"pid": 10592, "name": "MpDefenderCoreService.exe", "username": null, "cmdline": null}, {"pid": 10596, "name": "WidgetService.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\Microsoft.WidgetsPlatformRuntime_1.6.9.0_x64__8wekyb3d8bbwe\\WidgetService\\WidgetService.exe", "-RegisterProcessAsComServer", "-Embedding"]}, {"pid": 10628, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 10648, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=88", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=39036747993", "--metrics-shmem-handle=10640,i,11184089887239376134,15690118775059020194,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=8724", "/prefetch:1"]}, {"pid": 10676, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 10724, "name": "UserOOBEBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\oobe\\UserOOBEBroker.exe", "-Embedding"]}, {"pid": 10816, "name": "msedgewebview2.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--embedded-browser-webview=1", "--webview-exe-name=SearchHost.exe", "--webview-exe-version=2125.12001.30.0", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "--no<PERSON><PERSON><PERSON>s", "--disable-features=msSmartScreenProtection", "--edge-webview-enable-mojo-ipcz", "--enable-features=msEdgeFluentOverlayScrollbar", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; Cortana 1.18.9.23723; 10.0.0.0.26100.4351) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.26100 IsWebView2/True (WebView2Version 137.0.3296.68)", "--lang=es-ES", "--mojo-named-platform-channel-pipe=9492.10616.3778586433353338538", "/pfhostedapp:a30987782552d014c8eda76c90663a3bc8644a62"]}, {"pid": 10860, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\??\\C:\\WINDOWS\\system32\\conhost.exe", "0x4"]}, {"pid": 10924, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 11120, "name": "helper.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["helper/helper.exe", "49736", "--", "ut_web/1.4.0.6042", "hval/e0fced9ad094ef1195b6bb54633403c2"]}, {"pid": 11196, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=30", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=11592346035", "--metrics-shmem-handle=7196,i,9991800943219883027,8054363864850745004,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=7028", "/prefetch:1"]}, {"pid": 11204, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=es", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=4492,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=2560", "/prefetch:14"]}, {"pid": 11340, "name": "msedgewebview2.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--type=renderer", "--no<PERSON><PERSON><PERSON>s", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; Cortana 1.18.9.23723; 10.0.0.0.26100.4351) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.26100 IsWebView2/True (WebView2Version 137.0.3296.68)", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "--webview-exe-name=SearchHost.exe", "--webview-exe-version=2125.12001.30.0", "--embedded-browser-webview=1", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=5", "--js-flags=--expose-gc --ms-user-locale=es_VE", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=24562033", "--always-read-main-dll", "--field-trial-handle=1832,i,1855096248032418072,761040596321207274,262144", "--enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msEdgeFluentOverlayScrollbar,msWebView2NoTabForScreenShare,msWebViewAppLifeCycleWorkaround", "--disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillAdvancedSuggestionsBasic,msEdgeAutofillOneClickAutocomplete,msEdgeAutofillShowDeployedPassword,msEdgeAutofillSs,msEdgeBrowserEssentialsShowUpdateSection,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPrintInWebView2,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSmartScreenProtection,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsTaskManager,msWindowsUserActivities,msZipPayVirtualCard", "--variations-seed-version", "--mojo-platform-channel-handle=3552", "/pfhostedapp:a30987782552d014c8eda76c90663a3bc8644a62", "/prefetch:1"]}, {"pid": 11444, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 11632, "name": "msedgewebview2.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--type=gpu-process", "--no<PERSON><PERSON><PERSON>s", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; Cortana 1.18.9.23723; 10.0.0.0.26100.4351) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.26100 IsWebView2/True (WebView2Version 137.0.3296.68)", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "--webview-exe-name=SearchHost.exe", "--webview-exe-version=2125.12001.30.0", "--embedded-browser-webview=1", "--gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--always-read-main-dll", "--field-trial-handle=1832,i,1855096248032418072,761040596321207274,262144", "--enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msEdgeFluentOverlayScrollbar,msWebView2NoTabForScreenShare,msWebViewAppLifeCycleWorkaround", "--disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillAdvancedSuggestionsBasic,msEdgeAutofillOneClickAutocomplete,msEdgeAutofillShowDeployedPassword,msEdgeAutofillSs,msEdgeBrowserEssentialsShowUpdateSection,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPrintInWebView2,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSmartScreenProtection,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsTaskManager,msWindowsUserActivities,msZipPayVirtualCard", "--variations-seed-version", "--mojo-platform-channel-handle=1820", "/prefetch:2", "/pfhostedapp:a30987782552d014c8eda76c90663a3bc8644a62"]}, {"pid": 11636, "name": "msedgewebview2.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=es", "--service-sandbox-type=none", "--no<PERSON><PERSON><PERSON>s", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; Cortana 1.18.9.23723; 10.0.0.0.26100.4351) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.26100 IsWebView2/True (WebView2Version 137.0.3296.68)", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "--webview-exe-name=SearchHost.exe", "--webview-exe-version=2125.12001.30.0", "--embedded-browser-webview=1", "--always-read-main-dll", "--field-trial-handle=1832,i,1855096248032418072,761040596321207274,262144", "--enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msEdgeFluentOverlayScrollbar,msWebView2NoTabForScreenShare,msWebViewAppLifeCycleWorkaround", "--disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillAdvancedSuggestionsBasic,msEdgeAutofillOneClickAutocomplete,msEdgeAutofillShowDeployedPassword,msEdgeAutofillSs,msEdgeBrowserEssentialsShowUpdateSection,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPrintInWebView2,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSmartScreenProtection,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsTaskManager,msWindowsUserActivities,msZipPayVirtualCard", "--variations-seed-version", "--mojo-platform-channel-handle=2008", "/prefetch:11", "/pfhostedapp:a30987782552d014c8eda76c90663a3bc8644a62"]}, {"pid": 11748, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe"]}, {"pid": 11808, "name": "ctfmon.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["/QuitInfo:000000000000027C;0000000000000280;"]}, {"pid": 11940, "name": "TextInputHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\SystemApps\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\TextInputHost.exe", "-ServerName:InputApp.AppXk0k6mrh4r2q0ct33a9wgbez0x7v9cz5y.mca"]}, {"pid": 12052, "name": "LockApp.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\SystemApps\\Microsoft.LockApp_cw5n1h2txyewy\\LockApp.exe", "-ServerName:WindowsDefaultLockScreen.AppX7y4nbzq37zn4ks9k7amqjywdat7d3j2z.mca"]}, {"pid": 12228, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 12276, "name": "msedgewebview2.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\msedgewebview2.exe", "--type=utility", "--utility-sub-type=storage.mojom.StorageService", "--lang=es", "--service-sandbox-type=service", "--no<PERSON><PERSON><PERSON>s", "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64; Cortana 1.18.9.23723; 10.0.0.0.26100.4351) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.26100 IsWebView2/True (WebView2Version 137.0.3296.68)", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Packages\\MicrosoftWindows.Client.CBS_cw5n1h2txyewy\\LocalState\\EBWebView", "--webview-exe-name=SearchHost.exe", "--webview-exe-version=2125.12001.30.0", "--embedded-browser-webview=1", "--always-read-main-dll", "--field-trial-handle=1832,i,1855096248032418072,761040596321207274,262144", "--enable-features=ForceSWDCompWhenDCompFallbackRequired,msAggressiveCacheTrimming,msCustomDataPartition,msEdgeFluentOverlayScrollbar,msWebView2NoTabForScreenShare,msWebViewAppLifeCycleWorkaround", "--disable-features=BackForwardCache,BackgroundTabLoadingFromPerformanceManager,CloseOmniboxPopupOnInactiveAreaClick,CollectAVProductsInfo,CollectCodeIntegrityInfo,EnableHangWatcher,FilterAdsOnAbusiveSites,GetWifiProtocol,LoginDetection,PreconnectToSearch,SafetyHub,SegmentationPlatform,SpareRendererForSitePerProcess,Ukm,WebPayments,msAITrackerClassification,msAbydosForWindowlessWV2,msAffirmVirtualCard,msAllowChromeWebstore,msAllowMSAPrtSSOForNonMSAProfile,msApplicationGuard,msAskBeforeClosingMultipleTabs,msAutoToggleAADPrtSSOForNonAADProfile,msAutofillEdgeCoupons,msAutofillEdgeCouponsAutoApply,msAutofillEdgeServiceRequest,msAutomaticTabFreeze,msBrowserSettingsSupported,msCoarseGeolocationService,msDataProtection,msDesktopMode,msDesktopRewards,msDisableVariationsSeedFetchThrottling,msEEProactiveHistory,msETFOffstoreExtensionFileDataCollection,msETFPasswordTheftDNRActionSignals,msEdgeAdPlatformUI,msEdgeAddWebCapturetoCollections,msEdgeAutofillAdvancedSuggestionsBasic,msEdgeAutofillOneClickAutocomplete,msEdgeAutofillShowDeployedPassword,msEdgeAutofillSs,msEdgeBrowserEssentialsShowUpdateSection,msEdgeCloudConfigService,msEdgeCloudConfigServiceV2,msEdgeCohorts,msEdgeCollectionsPrismExperiment1,msEdgeCollectionsPrismOverallMigration,msEdgeComposeNext,msEdgeEnableNurturingFramework,msEdgeEnclavePrefsBasic,msEdgeEnclavePrefsNotification,msEdgeFaviconService,msEdgeHJTelemetry,msEdgeHubAppSkype,msEdgeImageEditorUI,msEdgeLinkDoctor,msEdgeMouseGestureDefaultEnabled,msEdgeMouseGestureSupported,msEdgeNewDeviceFre,msEdgeOnRampFRE,msEdgeOnRampImport,msEdgePDFCMHighlightUX,msEdgePasswordIris,msEdgePasswordIrisSaveBubble,msEdgeProngPersonalization,msEdgeReadingView,msEdgeRose,msEdgeSendTabToSelf,msEdgeSettingsImport,msEdgeSettingsImportV2,msEdgeShoppingPersistentStorage,msEdgeShoppingUI,msEdgeSmartFind,msEdgeSuperDragDefaultEnabled,msEdgeSuperDragDropSupported,msEdgeTipping,msEdgeTranslate,msEdgeUseCaptivePortalService,msEdgeWebContentFilteringFeedback,msEdgeWorkSearchBanner,msEnableCustomJobMemoryLimitsOnXbox,msEnableMIPForPDF,msEnablePdfUpsell,msEnableThirdPartyScanning,msEnableWebSignInCta,msEnableWebToBrowserSignIn,msEndpointDlp,msEntityExtraction,msExtensionTelemetryFramework,msExternalTaskManager,msFileSystemAccessDirectoryIterationBlocklistCheck,msForceBrowserSignIn,msForeignSessionsPage,msGeolocationAccessService,msGeolocationOSLocationPermissionFallback,msGeolocationSQMService,msGeolocationService,msGrowthInfraLaunchSourceLogging,msGuidedSwitchAllowed,msHubPinPersist,msImplicitSignin,msIrm,msIrmv2,msKlarnaVirtualCard,msLoadStatistics,msLogIsEdgePinnedToTaskbarOnLaunch,msMIPCrossTenantPdfViewSupport,msMdatpWebSiteDlp,msNotificationPermissionForPWA,msOnHoverSearchInSidebar,msOpenOfficeDocumentsInWebViewer,msPasswordBreachDetection,msPdfAnnotationsVisibility,msPdfDataRecovery,msPdfDigitalSignatureRead,msPdfFreeText,msPdfFreeTextForCJK,msPdfHighlightMode,msPdfInking,msPdfKeyphraseSupport,msPdfOOUI,msPdfPopupMarkerRenderer,msPdfShare,msPdfSharedLibrary,msPdfTextNote,msPdfTextNoteMoreMenu,msPdfThumbnailCache,msPdfUnderside,msPdfViewRestore,msPersonalizationUMA,msPriceComparison,msPrintInWebView2,msPromptDefaultHandlerForPDF,msReactiveSearch,msReadAloud,msReadAloudPdf,msRedirectToShoreline,msRevokeExtensions,msSaasDlp,msShoppingTrigger,msShorelineSearch,msShorelineSearchFindOnPageWebUI,msShowOfflineGameEntrance,msShowReadAloudIconInAddressBar,msShowUXForAADPrtSSOForNonAADProfile,msSmartScreenProtection,msSuspendMessageForNewSessionWhenHavingPendingNavigation,msSyncEdgeCollections,msTabResourceStats,msTokenizationAutofillInlineEnabled,msTouchMode,msTriggeringSignalGenerator,msUserUnderstanding,msVideoSuperResolutionUI,msWalletBuyNow,msWalletCheckout,msWalletDiagnosticDataLogger,msWalletHubEntry,msWalletHubIntlP3,msWalletPartialCard,msWalletPasswordCategorization,msWalletPasswordCategorizationPlatformExpansion,msWalletTokenizationCardMetadata,msWalletTokenizedAutofill,msWebAssist,msWebAssistHistorySearchService,msWebOOUI,msWindowsTaskManager,msWindowsUserActivities,msZipPayVirtualCard", "--variations-seed-version", "--mojo-platform-channel-handle=1988", "/prefetch:13", "/pfhostedapp:a30987782552d014c8eda76c90663a3bc8644a62"]}, {"pid": 12600, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "LocalService", "-p", "-s", "NPSMSvc"]}, {"pid": 12800, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 12960, "name": "FnHotkeyCapsLKNumLK.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\DriverStore\\FileRepository\\lenovofnandfunctionkeys.inf_amd64_fa50a878363b0cec\\FnHotkeyCapsLKNumLK.exe"]}, {"pid": 12988, "name": "AnyDesk.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\AnyDesk\\AnyDesk.exe", "--control"]}, {"pid": 13012, "name": "FnHotkeyUtility.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\DriverStore\\FileRepository\\lenovofnandfunctionkeys.inf_amd64_fa50a878363b0cec\\FnHotkeyUtility.exe"]}, {"pid": 13092, "name": "unsecapp.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\wbem\\unsecapp.exe", "-Embedding"]}, {"pid": 13116, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=utility", "--utility-sub-type=audio.mojom.AudioService", "--lang=en-US", "--service-sandbox-type=audio", "--video-capture-use-gpu-memory-buffer", "--no-pre-read-main-dll", "--metrics-shmem-handle=3548,i,4903586054489105150,9365793676639219311,524288", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=6772", "/prefetch:12"]}, {"pid": 13180, "name": "backgroundTaskHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\backgroundTaskHost.exe", "-ServerName:Global.DesktopSpotlight.AppXz2j21w56bgxkgsjhtn7zkjsepq96erz2.mca"]}, {"pid": 13316, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 13404, "name": "PhoneExperienceHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\Microsoft.YourPhone_1.25042.96.0_x64__8wekyb3d8bbwe\\PhoneExperienceHost.exe", "-ComServer:Background", "-Embedding"]}, {"pid": 13464, "name": "WindowsPackageManagerServer.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\Microsoft.DesktopAppInstaller_1.25.390.0_x64__8wekyb3d8bbwe\\WindowsPackageManagerServer.exe", "-Embedding"]}, {"pid": 13524, "name": "OfficeClickToRun.exe", "username": null, "cmdline": null}, {"pid": 13532, "name": "Discord.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\Discord.exe"]}, {"pid": 13556, "name": "unsecapp.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\wbem\\unsecapp.exe", "-Embedding"]}, {"pid": 13572, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=es", "--service-sandbox-type=none", "--dns-result-order=ipv4first", "--inspect-port=0", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=3548,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3708", "/prefetch:14"]}, {"pid": 13648, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 13656, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=116", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=41910917988", "--metrics-shmem-handle=7812,i,5127057713670457071,5803213899107841182,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=8524", "/prefetch:1"]}, {"pid": 13724, "name": "MsMpEng.exe", "username": null, "cmdline": null}, {"pid": 13748, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "92", "--height", "27", "--signal", "0x3dc", "--server", "0x3c0"]}, {"pid": 13752, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 13888, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 14028, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--app-user-model-id=com.squirrel.Figma.Figma", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\resources\\app.asar", "--enable-sandbox", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=6", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=59691847220", "--field-trial-handle=2796,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=2792", "/prefetch:1"]}, {"pid": 14032, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--app-user-model-id=Microsoft.VisualStudioCode", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app", "--enable-sandbox", "--enable-blink-features=HighlightAPI", "--disable-blink-features=FontMatchingCTMigration,StandardizedBrowserZoom,", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=10", "--time-ticks-at-unix-epoch=-1749796178865395", "--launch-time-ticks=41254982620", "--field-trial-handle=4512,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4280", "--vscode-window-config=vscode:967ab6ea-c656-49d4-8ad6-8b9cd1ada6c8", "/prefetch:1"]}, {"pid": 14068, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=gpu-process", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--field-trial-handle=1696,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=1684", "/prefetch:2"]}, {"pid": 14196, "name": "SecurityHealthSystray.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\SecurityHealthSystray.exe"]}, {"pid": 14220, "name": "SecurityHealthService.exe", "username": null, "cmdline": null}, {"pid": 14296, "name": "RtkAudUService64.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\DriverStore\\FileRepository\\realtekservice.inf_amd64_32b266092fc6592d\\RtkAudUService64.exe", "-background"]}, {"pid": 14416, "name": "Discord.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\Discord.exe", "--type=gpu-process", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\discord", "--gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--field-trial-handle=1796,i,4839860862807515868,690319024569291535,262144", "--disable-features=AllowAggressiveThrottlingWithWebSocket,HardwareMediaKeyHandling,IntensiveWakeUpThrottling,MediaSessionService,SpareRendererForSitePerProcess,UseEcoQoSForBackgroundProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=1784", "/prefetch:2"]}, {"pid": 14440, "name": "Discord.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\Discord.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=es", "--service-sandbox-type=none", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\discord", "--standard-schemes=disclip", "--secure-schemes=disclip,sentry-ipc", "--bypasscsp-schemes=sentry-ipc", "--cors-schemes=sentry-ipc", "--fetch-schemes=disclip,sentry-ipc", "--streaming-schemes=disclip", "--field-trial-handle=1796,i,4839860862807515868,690319024569291535,262144", "--disable-features=AllowAggressiveThrottlingWithWebSocket,HardwareMediaKeyHandling,IntensiveWakeUpThrottling,MediaSessionService,SpareRendererForSitePerProcess,UseEcoQoSForBackgroundProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=2044", "/prefetch:11"]}, {"pid": 14652, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--extension-process", "--init-isolate-as-foreground", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=42", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=14212017625", "--metrics-shmem-handle=9580,i,12229827758534323286,9375607993972517939,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=8968", "/prefetch:9"]}, {"pid": 14708, "name": "CrossDeviceService.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\MicrosoftWindows.CrossDevice_1.25042.38.0_x64__cw5n1h2txyewy\\CrossDeviceService.exe"]}, {"pid": 14716, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=renderer", "--enable-distillability-service", "--origin-trial-public-key=bYUKPJoPnCxeNvu72j4EmPuK7tr1PAC7SHh8ld9Mw3E=,fMS4mpO6buLQ/QMd+zJmxzty/VQ6B1EUZqoCU04zoRU=", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=10", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=48791424", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=4956", "/prefetch:1"]}, {"pid": 14736, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "191", "--height", "27", "--signal", "0x580", "--server", "0x534"]}, {"pid": 14744, "name": "figma_agent.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\FigmaAgent\\figma_agent.exe"]}, {"pid": 15044, "name": "utweb.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Roaming\\uTorrent Web\\utweb.exe", "/MINIMIZED"]}, {"pid": 15064, "name": "Discord.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\Discord.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\discord", "--standard-schemes=disclip", "--secure-schemes=disclip,sentry-ipc", "--bypasscsp-schemes=sentry-ipc", "--cors-schemes=sentry-ipc", "--fetch-schemes=disclip,sentry-ipc", "--streaming-schemes=disclip", "--app-user-model-id=com.squirrel.Discord.Discord", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\resources\\app.asar", "--no-sandbox", "--no-zygote", "--enable-blink-features=EnumerateDevices,AudioOutputDevices", "--autoplay-policy=no-user-gesture-required", "--disable-background-timer-throttling", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=6", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=40246737", "--field-trial-handle=1796,i,4839860862807515868,690319024569291535,262144", "--disable-features=AllowAggressiveThrottlingWithWebSocket,HardwareMediaKeyHandling,IntensiveWakeUpThrottling,MediaSessionService,SpareRendererForSitePerProcess,UseEcoQoSForBackgroundProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3912", "--enable-node-leakage-in-renderers", "/prefetch:1"]}, {"pid": 15204, "name": "AdobeCollabSync.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Adobe\\Acrobat DC\\Acrobat\\AdobeCollabSync.exe"]}, {"pid": 15248, "name": "AdobeCollabSync.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Adobe\\Acrobat DC\\Acrobat\\AdobeCollabSync.exe", "--type=collab-renderer", "--proc=15204"]}, {"pid": 15384, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--no-startup-window", "/prefetch:5"]}, {"pid": 15404, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data", "/prefetch:4", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data\\Crashpad", "--url=https://cr.brave.com", "--annotation=plat=Win64", "--annotation=prod=Brave", "--annotation=ver=137.1.79.123", "--initial-client-data=0x11c,0x120,0x124,0xa4,0x128,0x7ffdda5cfef8,0x7ffdda5cff04,0x7ffdda5cff10"]}, {"pid": 15624, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=gpu-process", "--no-pre-read-main-dll", "--start-stack-profiler", "--gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=2020", "/prefetch:2"]}, {"pid": 15632, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=es", "--service-sandbox-type=none", "--no-pre-read-main-dll", "--start-stack-profiler", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=2220", "/prefetch:11"]}, {"pid": 15700, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=utility", "--utility-sub-type=storage.mojom.StorageService", "--lang=es", "--service-sandbox-type=service", "--no-pre-read-main-dll", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=2484", "/prefetch:13"]}, {"pid": 15744, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=renderer", "--extension-process", "--enable-distillability-service", "--origin-trial-public-key=bYUKPJoPnCxeNvu72j4EmPuK7tr1PAC7SHh8ld9Mw3E=,fMS4mpO6buLQ/QMd+zJmxzty/VQ6B1EUZqoCU04zoRU=", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=6", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=44134583", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=2964", "/prefetch:9"]}, {"pid": 16044, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 16176, "name": "SystemSettings.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\ImmersiveControlPanel\\SystemSettings.exe", "-ServerName:microsoft.windows.immersivecontrolpanel"]}, {"pid": 16192, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=renderer", "--extension-process", "--enable-distillability-service", "--origin-trial-public-key=bYUKPJoPnCxeNvu72j4EmPuK7tr1PAC7SHh8ld9Mw3E=,fMS4mpO6buLQ/QMd+zJmxzty/VQ6B1EUZqoCU04zoRU=", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=309", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=72941722967", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=3436", "/prefetch:9"]}, {"pid": 16208, "name": "WSHelper.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files (x86)\\Common Files\\Wondershare\\Wondershare Helper Compact\\WSHelper.exe"]}, {"pid": 16240, "name": "Discord.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Discord\\app-1.0.9195\\Discord.exe", "--type=utility", "--utility-sub-type=audio.mojom.AudioService", "--lang=es", "--service-sandbox-type=audio", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\discord", "--standard-schemes=disclip", "--secure-schemes=disclip,sentry-ipc", "--bypasscsp-schemes=sentry-ipc", "--cors-schemes=sentry-ipc", "--fetch-schemes=disclip,sentry-ipc", "--streaming-schemes=disclip", "--field-trial-handle=1796,i,4839860862807515868,690319024569291535,262144", "--disable-features=AllowAggressiveThrottlingWithWebSocket,HardwareMediaKeyHandling,IntensiveWakeUpThrottling,MediaSessionService,SpareRendererForSitePerProcess,UseEcoQoSForBackgroundProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3944", "/prefetch:12"]}, {"pid": 16416, "name": "Copilot.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\Microsoft.Copilot_1.25053.93.0_x64__8wekyb3d8bbwe\\Copilot.exe"]}, {"pid": 16540, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=renderer", "--enable-distillability-service", "--origin-trial-public-key=bYUKPJoPnCxeNvu72j4EmPuK7tr1PAC7SHh8ld9Mw3E=,fMS4mpO6buLQ/QMd+zJmxzty/VQ6B1EUZqoCU04zoRU=", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=12", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=51405320", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=6080", "/prefetch:1"]}, {"pid": 16636, "name": "RuntimeBroker.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Windows\\System32\\RuntimeBroker.exe", "-Embedding"]}, {"pid": 16684, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 16700, "name": "ShellExperienceHost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\SystemApps\\ShellExperienceHost_cw5n1h2txyewy\\ShellExperienceHost.exe", "-ServerName:App.AppXtk181tbxbce2qsex02s8tw7hfxa9xb3t.mca"]}, {"pid": 16716, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=47", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=14358051414", "--metrics-shmem-handle=7880,i,12897323944991267271,8031612876461484475,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=9444", "/prefetch:1"]}, {"pid": 16780, "name": "WsToastNotification.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Wondershare\\Wondershare NativePush\\WsToastNotification.exe"]}, {"pid": 16800, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 16932, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "80", "--height", "30", "--signal", "0x434", "--server", "0x454"]}, {"pid": 16972, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 17012, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\??\\C:\\WINDOWS\\system32\\conhost.exe", "0x4"]}, {"pid": 17040, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 17208, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "c:\\Users\\<USER>\\.vscode\\extensions\\kisstkondoros.vscode-gutter-preview-0.32.2\\dist\\server.js", "--node-ipc", "--clientProcessId=13572"]}, {"pid": 17324, "name": "WmiPrvSE.exe", "username": null, "cmdline": null}, {"pid": 17364, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=en-US", "--service-sandbox-type=none", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=2088,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=1952", "/prefetch:11"]}, {"pid": 17488, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "/prefetch:4", "--no-rate-limit", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Roaming\\Code\\Crashpad", "--url=appcenter://code?aid=a4e3233c-699c-46ec-b4f4-9c2a77254662&uid=94aae369-03cd-43e9-adea-c8532620c491&iid=94aae369-03cd-43e9-adea-c8532620c491&sid=94aae369-03cd-43e9-adea-c8532620c491", "--annotation=_companyName=Microsoft", "--annotation=_productName=VSCode", "--annotation=_version=1.100.3", "--annotation=plat=Win64", "--annotation=prod=Electron", "--annotation=ver=34.5.1", "--initial-client-data=0x420,0x498,0x49c,0x414,0x418,0x7ff7187286b4,0x7ff7187286c0,0x7ff7187286d0"]}, {"pid": 17520, "name": "TabTip.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["/QuitInfo:00000000000002E0;00000000000002F8;"]}, {"pid": 17624, "name": "svchost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\system32\\svchost.exe", "-k", "DevicesFlow", "-s", "DevicesFlowUserSvc"]}, {"pid": 17648, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=40", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=14211854556", "--metrics-shmem-handle=7724,i,13083102787333390250,10789901542284787944,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=9488", "/prefetch:1"]}, {"pid": 17716, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--restart", "--restore-last-session", "--flag-switches-begin", "--flag-switches-end"]}, {"pid": 17744, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=crashpad-handler", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data", "/prefetch:4", "--monitor-self-annotation=ptype=crashpad-handler", "--database=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Crashpad", "--metrics-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data", "--url=https://clients2.google.com/cr/report", "--annotation=channel=", "--annotation=plat=Win64", "--annotation=prod=Chrome", "--annotation=ver=137.0.7151.104", "--initial-client-data=0x11c,0x120,0x124,0xd8,0x128,0x7ffdabc49ce8,0x7ffdabc49cf4,0x7ffdabc49d00"]}, {"pid": 17752, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=utility", "--utility-sub-type=proxy_resolver.mojom.ProxyResolverFactory", "--lang=es", "--service-sandbox-type=service", "--video-capture-use-gpu-memory-buffer", "--no-pre-read-main-dll", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=6056", "/prefetch:14"]}, {"pid": 17908, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=gpu-process", "--no-pre-read-main-dll", "--gpu-preferences=UAAAAAAAAADgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA", "--metrics-shmem-handle=1744,i,11729718771756663202,3320195190557582426,262144", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=1952", "/prefetch:2"]}, {"pid": 17920, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=en-US", "--service-sandbox-type=none", "--no-pre-read-main-dll", "--metrics-shmem-handle=2200,i,12573267099935106628,4248370408122980521,524288", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=2288", "/prefetch:11"]}, {"pid": 17940, "name": "Notepad.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\WindowsApps\\Microsoft.WindowsNotepad_11.2503.16.0_x64__8wekyb3d8bbwe\\Notepad\\Notepad.exe", "C:\\Users\\<USER>\\Desktop\\NOTAS.txt"]}, {"pid": 17964, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=utility", "--utility-sub-type=storage.mojom.StorageService", "--lang=en-US", "--service-sandbox-type=service", "--no-pre-read-main-dll", "--metrics-shmem-handle=2344,i,2834075519612448312,14168548369588064471,524288", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=1856", "/prefetch:13"]}, {"pid": 18024, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.vscode-pylance-2024.8.1\\dist\\server.bundle.js", "--cancellationReceive=file:21085ae2d5c416f62d7e8d54cb655c96c28439b8f7", "--node-ipc", "--clientProcessId=19288"]}, {"pid": 18080, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 18384, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "--type=utility", "--utility-sub-type=network.mojom.NetworkService", "--lang=es", "--service-sandbox-type=none", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Code", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=1996,i,13576806171259502510,5305417802304854947,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync", "--disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=1864", "/prefetch:11"]}, {"pid": 18388, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--app-user-model-id=Anysphere.Cursor", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app", "--enable-sandbox", "--enable-blink-features=HighlightAPI", "--force-gpu-mem-available-mb=1024", "--disable-blink-features=FontMatchingCTMigration,StandardizedBrowserZoom,", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=4", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=41092724053", "--field-trial-handle=3212,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3208", "--vscode-window-config=vscode:db89e982-7661-4a30-9bb6-8ace0d0e19b7", "/prefetch:1"]}, {"pid": 18756, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\json-language-features\\server\\dist\\node\\jsonServerMain", "--node-ipc", "--clientProcessId=19288"]}, {"pid": 18760, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "191", "--height", "27", "--signal", "0x464", "--server", "0x4a0"]}, {"pid": 19024, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=128", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=59048957007", "--metrics-shmem-handle=8760,i,11011147614190174710,16214361832383075483,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=6880", "/prefetch:1"]}, {"pid": 19104, "name": "taskhostw.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["taskhostw.exe"]}, {"pid": 19180, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=en-US", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=4072,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4064", "/prefetch:14"]}, {"pid": 19192, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=en-US", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=2808,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4044", "/prefetch:14"]}, {"pid": 19212, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", "Import-Module 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.powershell-2025.0.0\\modules\\PowerShellEditorServices\\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.0.0' -BundledModulesPath 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-vscode.powershell-2025.0.0\\modules' -EnableConsoleRepl -StartupBanner \"PowerShell Extension v2025.0.0\nCopyright (c) Microsoft Corporation.\n\nhttps://aka.ms/vscode-powershell\nType 'help' to get help.\n\" -LogLevel 'Warning' -LogPath 'c:\\Users\\<USER>\\AppData\\Roaming\\Code\\logs\\20250613T135417\\window1\\exthost\\ms-vscode.powershell' -SessionDetailsPath 'c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\ms-vscode.powershell\\sessions\\PSES-VSCode-11748-373152.json' -FeatureFlags @() "]}, {"pid": 19288, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=en-US", "--service-sandbox-type=none", "--dns-result-order=ipv4first", "--inspect-port=0", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Cursor", "--standard-schemes=vscode-webview,vscode-file", "--enable-sandbox", "--secure-schemes=vscode-webview,vscode-file", "--cors-schemes=vscode-webview,vscode-file", "--fetch-schemes=vscode-webview,vscode-file", "--service-worker-schemes=vscode-webview", "--code-cache-schemes=vscode-webview,vscode-file", "--field-trial-handle=4100,i,2724277609039773225,12583497064095120374,262144", "--enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports", "--disable-features=CalculateNativeWinOcclusion,PlzDedicatedWorker,SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4048", "/prefetch:14"]}, {"pid": 19492, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=utility", "--utility-sub-type=audio.mojom.AudioService", "--lang=es", "--service-sandbox-type=audio", "--video-capture-use-gpu-memory-buffer", "--no-pre-read-main-dll", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=5812", "/prefetch:12"]}, {"pid": 19672, "name": "Code.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\markdown-language-features\\dist\\serverWorkerMain", "--node-ipc", "--clientProcessId=13572"]}, {"pid": 20296, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=115", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=41872255354", "--metrics-shmem-handle=8316,i,18126203724170483810,10197862549135578175,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=5904", "/prefetch:1"]}, {"pid": 20328, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--app-user-model-id=com.squirrel.Figma.Figma", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\resources\\app.asar", "--enable-sandbox", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=11", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=59719456334", "--field-trial-handle=4260,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4256", "/prefetch:1"]}, {"pid": 20544, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=renderer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--app-user-model-id=com.squirrel.Figma.Figma", "--app-path=C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\resources\\app.asar", "--enable-sandbox", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=10", "--time-ticks-at-unix-epoch=-1749796178865396", "--launch-time-ticks=59719573779", "--field-trial-handle=4400,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=4296", "/prefetch:1"]}, {"pid": 20652, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 20812, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe"]}, {"pid": 21020, "name": "Figma.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Figma\\app-125.4.9\\Figma.exe", "--type=utility", "--utility-sub-type=node.mojom.NodeService", "--lang=es", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--user-data-dir=C:\\Users\\<USER>\\AppData\\Roaming\\Figma\\DesktopProfile\\v34", "--secure-schemes=desktop-file", "--fetch-schemes=desktop-file", "--field-trial-handle=3796,i,3764287825081415170,17516264773720031217,262144", "--disable-features=SpareRendererForSitePerProcess,WinDelaySpellcheckServiceInit,WinRetrieveSuggestionsOnlyOnDemand", "--variations-seed-version", "--mojo-platform-channel-handle=3788", "/prefetch:14"]}, {"pid": 21128, "name": "SDXHelper.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Microsoft Office\\Root\\Office16\\SDXHelper.exe", "-Embedding"]}, {"pid": 21160, "name": "AppVShNotify.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\AppVShNotify.exe"]}, {"pid": 21168, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=renderer", "--enable-distillability-service", "--origin-trial-public-key=bYUKPJoPnCxeNvu72j4EmPuK7tr1PAC7SHh8ld9Mw3E=,fMS4mpO6buLQ/QMd+zJmxzty/VQ6B1EUZqoCU04zoRU=", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=238", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=61928987113", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=6876", "/prefetch:1"]}, {"pid": 21240, "name": "Cursor.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\markdown-language-features\\dist\\serverWorkerMain", "--node-ipc", "--clientProcessId=19288"]}, {"pid": 21332, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=utility", "--utility-sub-type=video_capture.mojom.VideoCaptureService", "--lang=es", "--service-sandbox-type=none", "--video-capture-use-gpu-memory-buffer", "--no-pre-read-main-dll", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=3220", "/prefetch:14"]}, {"pid": 21524, "name": "powershell.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "-noexit", "-command", "try { . \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1\" } catch {}"]}, {"pid": 21548, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "182", "--height", "11", "--signal", "0x4d8", "--server", "0x59c"]}, {"pid": 21844, "name": "conhost.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["\\\\?\\C:\\WINDOWS\\system32\\conhost.exe", "--headless", "--width", "202", "--height", "11", "--signal", "0x4ac", "--server", "0x64c"]}, {"pid": 22208, "name": "svchost.exe", "username": null, "cmdline": null}, {"pid": 22340, "name": "chrome.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "--type=renderer", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=en-US", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=137", "--time-ticks-at-unix-epoch=-1749796178866082", "--launch-time-ticks=60019938903", "--metrics-shmem-handle=3644,i,18105146850094352420,13879839072336538576,2097152", "--field-trial-handle=1956,i,13605653873725271328,3671125610876057327,262144", "--variations-seed-version=20250612-180010.549000", "--mojo-platform-channel-handle=6104", "/prefetch:1"]}, {"pid": 22412, "name": "brave.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe", "--type=renderer", "--enable-distillability-service", "--origin-trial-public-key=bYUKPJoPnCxeNvu72j4EmPuK7tr1PAC7SHh8ld9Mw3E=,fMS4mpO6buLQ/QMd+zJmxzty/VQ6B1EUZqoCU04zoRU=", "--no-pre-read-main-dll", "--video-capture-use-gpu-memory-buffer", "--lang=es", "--device-scale-factor=1.25", "--num-raster-threads=4", "--enable-main-frame-before-activation", "--renderer-client-id=278", "--time-ticks-at-unix-epoch=-1749796178866081", "--launch-time-ticks=66502358421", "--field-trial-handle=2024,i,2731165307221676525,17081639859277322478,262144", "--variations-seed-version=main@dd7b38d348e9122beb8ae4e68e83c12556f44b66", "--mojo-platform-channel-handle=5668", "/prefetch:1"]}, {"pid": 22436, "name": "python3.13.exe", "username": "DESKTOP-SA1RPJC\\IBERSOFT", "cmdline": ["C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps\\python.exe", "scripts/run_security.py"]}], "recommendations": ["El script no se está ejecutando como administrador. Algunas verificaciones pueden estar limitadas.", "Se detectaron 46 puertos abiertos. Verifique que todos sean necesarios."]}