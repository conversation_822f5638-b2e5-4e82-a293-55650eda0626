"""
Hamilton AI Assistant - Configuration Settings
Configuración principal para el asistente personal Hamilton
"""

import os
from pathlib import Path
from typing import Dict, Any
from pydantic import BaseSettings

class HamiltonSettings(BaseSettings):
    """Configuración principal de Hamilton"""
    
    # Información del proyecto
    PROJECT_NAME: str = "Hamilton AI Assistant"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Asistente personal de IA para señor Ibero"
    
    # Configuración del servidor
    HOST: str = "localhost"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Configuración de la base de datos
    DATABASE_URL: str = "sqlite:///./hamilton.db"
    REDIS_URL: str = "redis://localhost:6379"
    
    # Configuración de OpenAI
    OPENAI_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-4"
    
    # Configuración de reconocimiento facial
    FACE_RECOGNITION_TOLERANCE: float = 0.6
    FACE_DETECTION_MODEL: str = "hog"  # hog o cnn
    AUTHORIZED_USER: str = "señor_ibero"
    
    # Configuración de reconocimiento de voz
    SPEECH_RECOGNITION_LANGUAGE: str = "es-ES"
    SPEECH_RECOGNITION_TIMEOUT: int = 5
    SPEECH_RECOGNITION_PHRASE_TIMEOUT: float = 0.3
    
    # Configuración de síntesis de voz (TTS) - VOZ MASCULINA
    TTS_ENGINE: str = "pyttsx3"  # pyttsx3, azure, google
    TTS_VOICE_GENDER: str = "male"  # IMPORTANTE: Voz masculina para Hamilton
    TTS_VOICE_RATE: int = 180  # Velocidad de habla
    TTS_VOICE_VOLUME: float = 0.9  # Volumen
    
    # Configuraciones específicas para diferentes motores TTS
    TTS_AZURE_VOICE: str = "es-ES-AlvaroNeural"  # Voz masculina en español
    TTS_GOOGLE_VOICE: str = "es-ES-Standard-B"   # Voz masculina en español
    TTS_PYTTSX3_VOICE_ID: str = "spanish_male"   # ID de voz masculina
    
    # Configuración de seguridad
    SECRET_KEY: str = "hamilton-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # Configuración de Prolog
    PROLOG_ENGINE_PATH: str = "./logic/swipl"
    PROLOG_KNOWLEDGE_BASE: str = "./logic/reasoning.pl"
    
    # Configuración de almacenamiento
    UPLOAD_DIRECTORY: str = "./uploads"
    MODELS_DIRECTORY: str = "./models"
    LOGS_DIRECTORY: str = "./logs"
    
    # Configuración de aprendizaje
    LEARNING_ENABLED: bool = True
    MEMORY_RETENTION_DAYS: int = 365
    CONTEXT_WINDOW_SIZE: int = 10
    
    # Configuración de dispositivos
    ENABLE_MOBILE_API: bool = True
    ENABLE_WEB_INTERFACE: bool = True
    ENABLE_DESKTOP_GUI: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Instancia global de configuración
settings = HamiltonSettings()

# Configuración específica para voces masculinas por idioma
MALE_VOICES_CONFIG = {
    "es-ES": {
        "azure": "es-ES-AlvaroNeural",
        "google": "es-ES-Standard-B",
        "pyttsx3": "spanish_male"
    },
    "en-US": {
        "azure": "en-US-DavisNeural",
        "google": "en-US-Standard-D",
        "pyttsx3": "english_male"
    }
}

def get_male_voice_config(language: str = "es-ES", engine: str = "pyttsx3") -> str:
    """
    Obtiene la configuración de voz masculina para el idioma y motor especificado
    """
    return MALE_VOICES_CONFIG.get(language, {}).get(engine, "default_male")

def ensure_directories():
    """Crea los directorios necesarios si no existen"""
    directories = [
        settings.UPLOAD_DIRECTORY,
        settings.MODELS_DIRECTORY,
        settings.LOGS_DIRECTORY,
        "./storage/faces",
        "./storage/voices",
        "./storage/conversations"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# Crear directorios al importar
ensure_directories()
