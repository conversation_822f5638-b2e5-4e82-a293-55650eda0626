"""
Hamilton AI Assistant - Configuration Settings
Configuración global del sistema
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, Any, Optional
from pydantic_settings import BaseSettings

# Cargar variables de entorno
load_dotenv()

class HamiltonSettings(BaseSettings):
    """Configuración global de Hamilton"""
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = os.getenv('OPENAI_API_KEY', '')
    OPENAI_MODEL: str = os.getenv('OPENAI_MODEL', 'gpt-4')
    
    # Database Configuration
    DATABASE_URL: str = os.getenv('DATABASE_URL', 'sqlite:///hamilton.db')
    
    # Voice Recognition
    VOICE_RECOGNITION_ENABLED: bool = True
    VOICE_MODEL_PATH: Path = Path('models/voice_model')
    
    # Security Settings
    ENCRYPTION_KEY: str = os.getenv('ENCRYPTION_KEY', 'default-key-change-me')
    MAX_LOGIN_ATTEMPTS: int = 3
    SESSION_TIMEOUT: int = 3600  # 1 hour
    
    # Logging
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE: Path = Path('logs/hamilton.log')
    
    # Authorized Users
    AUTHORIZED_USER: str = os.getenv('AUTHORIZED_USER', 'admin')
    
    # API Endpoints
    API_HOST: str = os.getenv('API_HOST', 'localhost')
    API_PORT: int = int(os.getenv('API_PORT', '8000'))
    
    # Feature Flags
    ENABLE_GPT: bool = bool(os.getenv('ENABLE_GPT', 'True'))
    ENABLE_VOICE: bool = bool(os.getenv('ENABLE_VOICE', 'True'))
    ENABLE_FACE_RECOGNITION: bool = bool(os.getenv('ENABLE_FACE_RECOGNITION', 'True'))
    
    # Información del proyecto
    PROJECT_NAME: str = "Hamilton AI Assistant"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Asistente personal de IA para señor Ibero"
    
    # Configuración del servidor
    HOST: str = "localhost"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Configuración de la base de datos
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    
    # Configuración de reconocimiento facial
    FACE_RECOGNITION_TOLERANCE: float = 0.6
    FACE_DETECTION_MODEL: str = "hog"  # hog o cnn
    
    # Configuración de reconocimiento de voz
    SPEECH_RECOGNITION_LANGUAGE: str = "es-ES"
    SPEECH_RECOGNITION_TIMEOUT: int = 5
    SPEECH_RECOGNITION_PHRASE_TIMEOUT: float = 0.3
    
    # Configuración de síntesis de voz (TTS) - VOZ MASCULINA
    TTS_ENGINE: str = "pyttsx3"  # pyttsx3, azure, google
    TTS_VOICE_GENDER: str = "male"  # IMPORTANTE: Voz masculina para Hamilton
    TTS_VOICE_RATE: int = 180  # Velocidad de habla
    TTS_VOICE_VOLUME: float = 0.9  # Volumen
    
    # Configuraciones específicas para diferentes motores TTS
    TTS_AZURE_VOICE: str = "es-ES-AlvaroNeural"  # Voz masculina en español
    TTS_GOOGLE_VOICE: str = "es-ES-Standard-B"   # Voz masculina en español
    TTS_PYTTSX3_VOICE_ID: str = "spanish_male"   # ID de voz masculina
    
    # Configuración de seguridad
    SECRET_KEY: str = "hamilton-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # Configuración de Prolog
    PROLOG_ENGINE_PATH: str = "./logic/swipl"
    PROLOG_KNOWLEDGE_BASE: str = "./logic/reasoning.pl"
    
    # Configuración de almacenamiento
    UPLOAD_DIRECTORY: str = "./uploads"
    MODELS_DIRECTORY: str = "./models"
    LOGS_DIRECTORY: str = "./logs"
    
    # Configuración de aprendizaje
    LEARNING_ENABLED: bool = True
    MEMORY_RETENTION_DAYS: int = 365
    CONTEXT_WINDOW_SIZE: int = 10
    
    # Configuración de dispositivos
    ENABLE_MOBILE_API: bool = True
    ENABLE_WEB_INTERFACE: bool = True
    ENABLE_DESKTOP_GUI: bool = True
    
    TOTP_SECRET: str = "your_totp_secret_here"
    
    # Configuración de IA
    ENABLE_HUGGINGFACE: bool = True
    ENABLE_ANTHROPIC: bool = True
    ENABLE_LOCAL_LLM: bool = True
    
    # Hugging Face
    HUGGINGFACE_MODEL: str = "microsoft/DialoGPT-medium"
    HUGGINGFACE_TOKEN: str = ""  # Tu token de Hugging Face
    HUGGINGFACE_USE_GPU: bool = True
    
    # Anthropic
    ANTHROPIC_API_KEY: str = ""  # Tu clave API de Anthropic
    ANTHROPIC_MODEL: str = "claude-3-opus-20240229"
    
    # Modelo Local
    LOCAL_MODEL_PATH: str = "models/local_model"
    LOCAL_MODEL_TYPE: str = "gpt2"
    LOCAL_MODEL_SIZE: str = "medium"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Instancia global de configuración
settings = HamiltonSettings()

# Configuración específica para voces masculinas por idioma
MALE_VOICES_CONFIG = {
    "es-ES": {
        "azure": "es-ES-AlvaroNeural",
        "google": "es-ES-Standard-B",
        "pyttsx3": "spanish_male"
    },
    "en-US": {
        "azure": "en-US-DavisNeural",
        "google": "en-US-Standard-D",
        "pyttsx3": "english_male"
    }
}

def get_male_voice_config(language: str = "es-ES", engine: str = "pyttsx3") -> str:
    """
    Obtiene la configuración de voz masculina para el idioma y motor especificado
    """
    return MALE_VOICES_CONFIG.get(language, {}).get(engine, "default_male")

def ensure_directories():
    """Crea los directorios necesarios si no existen"""
    directories = [
        settings.UPLOAD_DIRECTORY,
        settings.MODELS_DIRECTORY,
        settings.LOGS_DIRECTORY,
        "./storage/faces",
        "./storage/voices",
        "./storage/conversations"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# Crear directorios al importar
ensure_directories()
