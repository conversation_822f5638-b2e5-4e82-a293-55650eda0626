% Hamilton AI Assistant - Base de Conocimientos
% Reglas de razonamiento y hechos para el asistente personal

% ===== HECHOS BÁSICOS =====

% Usuario autorizado
usuario_autorizado(senor_ibero).
usuario_principal(senor_ibero).

% Información personal del usuario
preferencia_idioma(senor_ibero, espanol).
preferencia_voz(hamilton, masculina).
relacion(hamilton, senor_ibero, asistente_personal).

% Capacidades de Hamilton
capacidad(hamilton, reconocimiento_facial).
capacidad(hamilton, reconocimiento_voz).
capacidad(hamilton, sintesis_voz).
capacidad(hamilton, razonamiento_logico).
capacidad(hamilton, aprendizaje_continuo).

% Estados del sistema
estado_valido(dormido).
estado_valido(activo).
estado_valido(escuchando).
estado_valido(procesando).
estado_valido(hablando).

% ===== REGLAS DE RAZONAMIENTO =====

% Reglas de autenticación
puede_activar(Usuario) :-
    usuario_autorizado(Usuario),
    usuario_principal(Usuario).

% Reglas de interacción
debe_responder(hamilton, Usuario) :-
    usuario_autorizado(Usuario),
    estado_actual(hamilton, activo).

% Reglas de cortesía
saludo_apropiado(Hora, 'Buenos días') :-
    Hora >= 5, Hora < 12.
saludo_apropiado(Hora, 'Buenas tardes') :-
    Hora >= 12, Hora < 18.
saludo_apropiado(Hora, 'Buenas noches') :-
    (Hora >= 18; Hora < 5).

% Reglas de prioridad de comandos
prioridad_alta(comando_emergencia).
prioridad_alta(comando_seguridad).
prioridad_media(comando_informacion).
prioridad_media(comando_asistencia).
prioridad_baja(comando_conversacion).

% Reglas de aprendizaje
debe_recordar(Informacion) :-
    tipo_informacion(Informacion, importante).
debe_recordar(Informacion) :-
    frecuencia_uso(Informacion, alta).

% Reglas de contexto
contexto_relevante(Tema, Conversacion) :-
    menciona_tema(Conversacion, Tema),
    tiempo_reciente(Conversacion).

% ===== REGLAS DE TOMA DE DECISIONES =====

% Decidir si interrumpir
puede_interrumpir(hamilton, Actividad) :-
    prioridad_alta(Actividad).
puede_interrumpir(hamilton, Actividad) :-
    prioridad_media(Actividad),
    not(estado_actual(hamilton, ocupado)).

% Decidir nivel de detalle en respuestas
respuesta_detallada(Usuario, Tema) :-
    usuario_autorizado(Usuario),
    interes_alto(Usuario, Tema).
respuesta_detallada(Usuario, Tema) :-
    usuario_autorizado(Usuario),
    primera_vez_pregunta(Usuario, Tema).

% Reglas de seguridad
acceso_permitido(Usuario, Funcion) :-
    usuario_autorizado(Usuario),
    funcion_publica(Funcion).
acceso_permitido(Usuario, Funcion) :-
    usuario_principal(Usuario),
    funcion_privada(Funcion).

% ===== HECHOS DINÁMICOS (se actualizan en tiempo real) =====

:- dynamic estado_actual/2.
:- dynamic conversacion_activa/3.
:- dynamic preferencia_usuario/3.
:- dynamic historial_comando/4.
:- dynamic contexto_actual/2.

% Estados iniciales
estado_actual(hamilton, dormido).
contexto_actual(hamilton, inicio_sesion).

% ===== REGLAS DE INFERENCIA AVANZADA =====

% Inferir intenciones del usuario
intencion(Usuario, obtener_informacion) :-
    comando_contiene(Usuario, pregunta),
    tipo_pregunta(informativa).

intencion(Usuario, solicitar_accion) :-
    comando_contiene(Usuario, imperativo),
    accion_valida(Accion).

intencion(Usuario, conversacion_casual) :-
    comando_contiene(Usuario, saludo),
    not(comando_contiene(Usuario, pregunta)).

% Inferir estado emocional (básico)
estado_emocional(Usuario, positivo) :-
    usa_palabras_positivas(Usuario),
    tono_amigable(Usuario).

estado_emocional(Usuario, neutral) :-
    not(usa_palabras_positivas(Usuario)),
    not(usa_palabras_negativas(Usuario)).

% Reglas de personalización
respuesta_personalizada(Usuario, Respuesta) :-
    preferencia_usuario(Usuario, estilo_comunicacion, formal),
    respuesta_formal(Respuesta).

respuesta_personalizada(Usuario, Respuesta) :-
    preferencia_usuario(Usuario, estilo_comunicacion, casual),
    respuesta_casual(Respuesta).

% ===== REGLAS ESPECÍFICAS PARA HAMILTON =====

% Reglas de conocimiento científico
es_ciencia(matematicas).
es_ciencia(fisica).
es_ciencia(quimica).
es_ciencia(historia).

puede_ayudar_con(hamilton, Tema) :-
    es_ciencia(Tema),
    capacidad(hamilton, razonamiento_logico).

% Reglas de dispositivos
dispositivo_compatible(portatil).
dispositivo_compatible(movil).
dispositivo_compatible(tablet).

puede_ejecutar_en(hamilton, Dispositivo) :-
    dispositivo_compatible(Dispositivo),
    capacidad(hamilton, multiplataforma).

% Reglas de tiempo y contexto
momento_apropiado(trabajo, Hora) :-
    Hora >= 9, Hora <= 17.
momento_apropiado(descanso, Hora) :-
    (Hora >= 18; Hora <= 8).

debe_sugerir_descanso(Usuario) :-
    tiempo_trabajo_continuo(Usuario, Tiempo),
    Tiempo > 120.  % más de 2 horas

% ===== REGLAS DE APRENDIZAJE AVANZADO =====

% Patrones de comportamiento del usuario
patron_usuario(Usuario, madrugador) :-
    frecuencia_activacion(Usuario, Hora),
    Hora >= 5, Hora <= 7.

patron_usuario(Usuario, noctambulo) :-
    frecuencia_activacion(Usuario, Hora),
    Hora >= 22.

% Preferencias inferidas
prefiere_respuestas_cortas(Usuario) :-
    historial_interrupciones(Usuario, Alto),
    Alto > 5.

prefiere_respuestas_detalladas(Usuario) :-
    historial_preguntas_seguimiento(Usuario, Alto),
    Alto > 3.

% ===== REGLAS DE RAZONAMIENTO CIENTÍFICO =====

% Matemáticas básicas
es_numero_primo(N) :-
    N > 1,
    not(tiene_divisor(N, 2, N)).

tiene_divisor(N, D, Max) :-
    D < Max,
    0 is N mod D.
tiene_divisor(N, D, Max) :-
    D < Max,
    D1 is D + 1,
    tiene_divisor(N, D1, Max).

% Física básica
unidad_fisica(metro, longitud).
unidad_fisica(kilogramo, masa).
unidad_fisica(segundo, tiempo).
unidad_fisica(newton, fuerza).

formula_fisica(fuerza, 'F = m * a').
formula_fisica(energia_cinetica, 'Ec = 1/2 * m * v^2').
formula_fisica(velocidad, 'v = d / t').

% ===== UTILIDADES =====

% Verificar tiempo
tiempo_reciente(Timestamp) :-
    get_time(Now),
    Diff is Now - Timestamp,
    Diff < 300.  % 5 minutos

% Contar elementos
contar_hechos(Tipo, Cantidad) :-
    findall(X, call(Tipo, X), Lista),
    length(Lista, Cantidad).

% Verificar si es fin de semana
es_fin_de_semana :-
    get_time(Now),
    format_time(atom(Day), '%u', Now),
    atom_number(Day, DayNum),
    (DayNum = 6; DayNum = 7).

% ===== REGLAS DE PERSONALIDAD DE HAMILTON =====

% Características de personalidad
personalidad(hamilton, profesional).
personalidad(hamilton, servicial).
personalidad(hamilton, inteligente).
personalidad(hamilton, respetuoso).

% Estilo de comunicación
estilo_comunicacion(hamilton, formal_pero_amigable).
usa_tratamiento(hamilton, senor_ibero, 'señor Ibero').

% Reglas de respuesta según personalidad
debe_ser_formal(hamilton, Usuario) :-
    personalidad(hamilton, profesional),
    usuario_principal(Usuario).

debe_mostrar_respeto(hamilton, Usuario) :-
    personalidad(hamilton, respetuoso),
    usuario_autorizado(Usuario).

% ===== REGLAS DE CONTEXTO CONVERSACIONAL =====

% Mantener contexto de conversación
tema_actual(Tema) :-
    contexto_actual(hamilton, Tema),
    Tema \= inicio_sesion.

cambio_tema_apropiado(TemaAnterior, TemaNuevo) :-
    not(tema_relacionado(TemaAnterior, TemaNuevo)),
    tiempo_transcurrido_tema(TemaAnterior, Tiempo),
    Tiempo > 60.  % más de 1 minuto

tema_relacionado(matematicas, fisica).
tema_relacionado(fisica, quimica).
tema_relacionado(historia, cultura).

% ===== REGLAS DE OPTIMIZACIÓN =====

% Optimizar respuestas según recursos
respuesta_optimizada(corta) :-
    recursos_limitados(cpu),
    recursos_limitados(memoria).

respuesta_optimizada(completa) :-
    recursos_suficientes(cpu),
    recursos_suficientes(memoria).

% Gestión de recursos
recursos_suficientes(cpu) :-
    uso_cpu(Porcentaje),
    Porcentaje < 70.

recursos_suficientes(memoria) :-
    uso_memoria(Porcentaje),
    Porcentaje < 80.
