#!/usr/bin/env python3
"""
Hamilton AI Assistant - IoT Device Setup
Configurador interactivo para dispositivos IoT reales
"""

import json
import asyncio
import logging
from pathlib import Path
import requests
from typing import Dict, List, Any

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IoTDeviceSetup:
    """Configurador de dispositivos IoT reales"""
    
    def __init__(self):
        self.config_file = Path("config/iot_real_devices.json")
        self.discovered_devices = []
    
    async def setup_philips_hue(self):
        """Configura dispositivos Philips Hue reales"""
        print("\n🔍 CONFIGURANDO PHILIPS HUE")
        print("-" * 40)
        
        # Buscar bridge automáticamente
        print("Buscando Philips Hue Bridge en la red...")
        
        try:
            # Descubrimiento automático del bridge
            discovery_url = "https://discovery.meethue.com/"
            response = requests.get(discovery_url, timeout=5)
            
            if response.status_code == 200:
                bridges = response.json()
                if bridges:
                    bridge_ip = bridges[0]["internalipaddress"]
                    print(f"✅ Bridge encontrado en: {bridge_ip}")
                    
                    # Solicitar presionar botón del bridge
                    print("\n🔘 PRESIONE EL BOTÓN DEL BRIDGE PHILIPS HUE AHORA")
                    print("Tiene 30 segundos para presionar el botón...")
                    
                    input("Presione Enter después de presionar el botón del bridge...")
                    
                    # Crear usuario
                    create_user_url = f"http://{bridge_ip}/api"
                    user_data = {"devicetype": "hamilton_ai_assistant"}
                    
                    user_response = requests.post(create_user_url, json=user_data, timeout=5)
                    
                    if user_response.status_code == 200:
                        result = user_response.json()
                        if "success" in result[0]:
                            username = result[0]["success"]["username"]
                            print(f"✅ Usuario creado: {username}")
                            
                            # Obtener dispositivos
                            devices_url = f"http://{bridge_ip}/api/{username}/lights"
                            devices_response = requests.get(devices_url, timeout=5)
                            
                            if devices_response.status_code == 200:
                                lights = devices_response.json()
                                print(f"✅ {len(lights)} luces encontradas:")
                                
                                for light_id, light_info in lights.items():
                                    print(f"  - {light_info['name']} (ID: {light_id})")
                                
                                # Guardar configuración
                                hue_config = {
                                    "bridge_ip": bridge_ip,
                                    "username": username,
                                    "lights": lights,
                                    "enabled": True
                                }
                                
                                return hue_config
                        else:
                            print("❌ Error: No se pudo crear usuario. ¿Presionó el botón?")
                    else:
                        print("❌ Error conectando con el bridge")
                else:
                    print("❌ No se encontraron bridges Philips Hue")
            else:
                print("❌ Error en el servicio de descubrimiento")
                
        except Exception as e:
            print(f"❌ Error configurando Philips Hue: {e}")
        
        return None
    
    async def setup_lifx(self):
        """Configura dispositivos LIFX"""
        print("\n💡 CONFIGURANDO LIFX")
        print("-" * 40)
        
        print("Para configurar LIFX necesita un token de API.")
        print("1. Vaya a https://cloud.lifx.com/settings")
        print("2. Genere un nuevo token")
        print("3. Ingrese el token aquí")
        
        token = input("Token de LIFX (o Enter para omitir): ").strip()
        
        if not token:
            print("⏭️ Configuración de LIFX omitida")
            return None
        
        try:
            # Probar token
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get("https://api.lifx.com/v1/lights/all", headers=headers, timeout=5)
            
            if response.status_code == 200:
                lights = response.json()
                print(f"✅ {len(lights)} dispositivos LIFX encontrados:")
                
                for light in lights:
                    print(f"  - {light['label']} ({light['product']['name']})")
                
                lifx_config = {
                    "api_token": token,
                    "lights": lights,
                    "enabled": True
                }
                
                return lifx_config
            else:
                print("❌ Token inválido o error de conexión")
                
        except Exception as e:
            print(f"❌ Error configurando LIFX: {e}")
        
        return None
    
    async def setup_tplink_kasa(self):
        """Configura dispositivos TP-Link Kasa"""
        print("\n🔌 CONFIGURANDO TP-LINK KASA")
        print("-" * 40)
        
        print("Buscando dispositivos TP-Link Kasa en la red local...")
        
        try:
            # Intentar importar python-kasa
            try:
                from kasa import Discover
                
                devices = await Discover.discover()
                
                if devices:
                    print(f"✅ {len(devices)} dispositivos encontrados:")
                    
                    kasa_devices = []
                    for ip, device in devices.items():
                        await device.update()
                        device_info = {
                            "ip": ip,
                            "alias": device.alias,
                            "model": device.model,
                            "device_type": device.device_type.name
                        }
                        kasa_devices.append(device_info)
                        print(f"  - {device.alias} ({device.model}) en {ip}")
                    
                    kasa_config = {
                        "devices": kasa_devices,
                        "enabled": True
                    }
                    
                    return kasa_config
                else:
                    print("❌ No se encontraron dispositivos TP-Link Kasa")
                    
            except ImportError:
                print("⚠️ Librería python-kasa no instalada")
                print("Instalar con: pip install python-kasa")
                
        except Exception as e:
            print(f"❌ Error configurando TP-Link Kasa: {e}")
        
        return None
    
    async def setup_generic_devices(self):
        """Configura dispositivos genéricos"""
        print("\n🔧 CONFIGURANDO DISPOSITIVOS GENÉRICOS")
        print("-" * 40)
        
        devices = []
        
        while True:
            print("\nTipos de dispositivos disponibles:")
            print("1. Luz/Lámpara")
            print("2. Enchufe inteligente")
            print("3. Termostato")
            print("4. Sensor")
            print("5. Cámara")
            print("0. Terminar")
            
            choice = input("Seleccione tipo de dispositivo (0-5): ").strip()
            
            if choice == "0":
                break
            elif choice in ["1", "2", "3", "4", "5"]:
                device_types = {
                    "1": "light",
                    "2": "switch", 
                    "3": "thermostat",
                    "4": "sensor",
                    "5": "camera"
                }
                
                device_type = device_types[choice]
                name = input(f"Nombre del {device_type}: ").strip()
                ip = input("IP del dispositivo (opcional): ").strip()
                room = input("Habitación: ").strip()
                
                device = {
                    "name": name,
                    "type": device_type,
                    "ip": ip if ip else None,
                    "room": room,
                    "protocol": "http",
                    "enabled": True
                }
                
                devices.append(device)
                print(f"✅ Dispositivo {name} agregado")
            else:
                print("❌ Opción inválida")
        
        if devices:
            return {"devices": devices, "enabled": True}
        
        return None
    
    async def run_setup(self):
        """Ejecuta configuración completa de IoT"""
        print("🏠 HAMILTON - CONFIGURACIÓN DE DISPOSITIVOS IOT")
        print("=" * 50)
        
        config = {
            "last_updated": "2024-06-14",
            "auto_discovery": True,
            "integrations": {}
        }
        
        # Configurar Philips Hue
        hue_config = await self.setup_philips_hue()
        if hue_config:
            config["integrations"]["philips_hue"] = hue_config
        
        # Configurar LIFX
        lifx_config = await self.setup_lifx()
        if lifx_config:
            config["integrations"]["lifx"] = lifx_config
        
        # Configurar TP-Link Kasa
        kasa_config = await self.setup_tplink_kasa()
        if kasa_config:
            config["integrations"]["tplink_kasa"] = kasa_config
        
        # Configurar dispositivos genéricos
        print("\n¿Desea agregar dispositivos genéricos? (s/n): ", end="")
        if input().lower().startswith('s'):
            generic_config = await self.setup_generic_devices()
            if generic_config:
                config["integrations"]["generic"] = generic_config
        
        # Guardar configuración
        if config["integrations"]:
            self.config_file.parent.mkdir(exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ Configuración guardada en: {self.config_file}")
            print(f"📊 Total de integraciones configuradas: {len(config['integrations'])}")
            
            # Mostrar resumen
            print("\n📋 RESUMEN DE CONFIGURACIÓN:")
            for integration, details in config["integrations"].items():
                if details.get("enabled"):
                    device_count = 0
                    if "lights" in details:
                        device_count = len(details["lights"])
                    elif "devices" in details:
                        device_count = len(details["devices"])
                    
                    print(f"  ✅ {integration}: {device_count} dispositivos")
        else:
            print("\n⚠️ No se configuraron dispositivos IoT")
        
        return config

async def main():
    """Función principal"""
    try:
        setup = IoTDeviceSetup()
        await setup.run_setup()
        
        print("\n🎉 ¡Configuración de IoT completada!")
        print("Ahora puede usar Hamilton para controlar sus dispositivos reales.")
        
    except KeyboardInterrupt:
        print("\n👋 Configuración cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")

if __name__ == "__main__":
    asyncio.run(main())
