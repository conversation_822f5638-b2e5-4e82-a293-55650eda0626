{"version": 1, "disable_existing_loggers": false, "formatters": {"standard": {"format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"}, "detailed": {"format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "standard", "stream": "ext://sys.stdout"}, "file": {"class": "logging.FileHandler", "level": "DEBUG", "formatter": "detailed", "filename": "logs/hamilton.log", "mode": "a"}}, "loggers": {"": {"handlers": ["console", "file"], "level": "DEBUG", "propagate": false}}}