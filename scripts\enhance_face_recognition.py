#!/usr/bin/env python3
"""
Hamilton AI Assistant - Face Recognition Enhancement
Script para mejorar la precisión del reconocimiento facial
"""

import os
import sys
import cv2
import json
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import time
from datetime import datetime

# Agregar directorio actual al path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FaceRecognitionEnhancer:
    """Mejorador de reconocimiento facial para Hamilton"""
    
    def __init__(self):
        self.encodings_dir = Path("data/face_encodings")
        self.enhanced_dir = Path("data/enhanced_encodings")
        self.training_images_dir = Path("data/training_images")
        
        # Crear directorios
        self.encodings_dir.mkdir(parents=True, exist_ok=True)
        self.enhanced_dir.mkdir(parents=True, exist_ok=True)
        self.training_images_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_encodings = []
        self.enhancement_config = {
            "min_encodings": 10,
            "target_encodings": 25,
            "quality_threshold": 0.6,
            "diversity_threshold": 0.3
        }
    
    def analyze_current_encodings(self):
        """Analiza los encodings faciales actuales"""
        print("🔍 ANALIZANDO ENCODINGS ACTUALES")
        print("=" * 35)
        
        try:
            from recognition.face_recognition import hamilton_face
            
            # Cargar encodings existentes
            encodings_data = hamilton_face._load_face_encodings()
            
            if not encodings_data or 'senor_ibero' not in encodings_data:
                print("❌ No se encontraron encodings para señor Ibero")
                return False
            
            user_encodings = encodings_data['senor_ibero']
            self.current_encodings = user_encodings
            
            print(f"📊 Encodings actuales: {len(user_encodings)}")
            
            # Analizar calidad y diversidad
            quality_analysis = self._analyze_encoding_quality(user_encodings)
            diversity_analysis = self._analyze_encoding_diversity(user_encodings)
            
            print(f"📈 Calidad promedio: {quality_analysis['average_quality']:.2f}")
            print(f"🎯 Diversidad: {diversity_analysis['diversity_score']:.2f}")
            
            # Recomendaciones
            recommendations = self._generate_recommendations(quality_analysis, diversity_analysis)
            
            print("\n💡 Recomendaciones:")
            for rec in recommendations:
                print(f"   • {rec}")
            
            return len(user_encodings) < self.enhancement_config["target_encodings"]
            
        except Exception as e:
            print(f"❌ Error analizando encodings: {e}")
            return False
    
    def _analyze_encoding_quality(self, encodings: List) -> Dict[str, Any]:
        """Analiza la calidad de los encodings"""
        if not encodings:
            return {"average_quality": 0.0, "quality_scores": []}
        
        # Calcular métricas de calidad basadas en la consistencia
        quality_scores = []
        
        for i, encoding in enumerate(encodings):
            # Calcular similitud con otros encodings del mismo usuario
            similarities = []
            for j, other_encoding in enumerate(encodings):
                if i != j:
                    # Distancia euclidiana (menor = más similar)
                    distance = np.linalg.norm(np.array(encoding) - np.array(other_encoding))
                    similarity = max(0, 1 - distance)  # Convertir a similitud
                    similarities.append(similarity)
            
            if similarities:
                # Calidad basada en consistencia con otros encodings
                avg_similarity = np.mean(similarities)
                quality_scores.append(avg_similarity)
            else:
                quality_scores.append(0.5)  # Valor neutral para encoding único
        
        return {
            "average_quality": np.mean(quality_scores) if quality_scores else 0.0,
            "quality_scores": quality_scores,
            "min_quality": min(quality_scores) if quality_scores else 0.0,
            "max_quality": max(quality_scores) if quality_scores else 0.0
        }
    
    def _analyze_encoding_diversity(self, encodings: List) -> Dict[str, Any]:
        """Analiza la diversidad de los encodings"""
        if len(encodings) < 2:
            return {"diversity_score": 0.0, "cluster_count": 1}
        
        # Calcular matriz de distancias
        distances = []
        for i, enc1 in enumerate(encodings):
            for j, enc2 in enumerate(encodings[i+1:], i+1):
                distance = np.linalg.norm(np.array(enc1) - np.array(enc2))
                distances.append(distance)
        
        # Diversidad basada en la varianza de distancias
        diversity_score = np.std(distances) if distances else 0.0
        
        return {
            "diversity_score": diversity_score,
            "average_distance": np.mean(distances) if distances else 0.0,
            "distance_variance": np.var(distances) if distances else 0.0
        }
    
    def _generate_recommendations(self, quality_analysis: Dict, diversity_analysis: Dict) -> List[str]:
        """Genera recomendaciones basadas en el análisis"""
        recommendations = []
        
        current_count = len(self.current_encodings)
        target_count = self.enhancement_config["target_encodings"]
        
        if current_count < self.enhancement_config["min_encodings"]:
            recommendations.append(f"Agregar al menos {self.enhancement_config['min_encodings'] - current_count} encodings más")
        
        if quality_analysis["average_quality"] < self.enhancement_config["quality_threshold"]:
            recommendations.append("Mejorar calidad de imagen durante captura")
            recommendations.append("Asegurar buena iluminación y enfoque")
        
        if diversity_analysis["diversity_score"] < self.enhancement_config["diversity_threshold"]:
            recommendations.append("Capturar desde diferentes ángulos")
            recommendations.append("Variar expresiones faciales")
            recommendations.append("Usar diferentes condiciones de iluminación")
        
        if current_count < target_count:
            recommendations.append(f"Capturar {target_count - current_count} encodings adicionales para óptima precisión")
        
        return recommendations
    
    def capture_enhanced_encodings(self):
        """Captura encodings adicionales con diferentes condiciones"""
        print("\n📸 CAPTURANDO ENCODINGS MEJORADOS")
        print("=" * 35)
        
        try:
            import face_recognition
            
            # Inicializar cámara
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                print("❌ No se pudo acceder a la cámara")
                return False
            
            captured_encodings = []
            capture_scenarios = [
                {"name": "Frontal con buena luz", "instructions": "Mira directamente a la cámara"},
                {"name": "Ligero ángulo izquierdo", "instructions": "Gira ligeramente la cabeza a la izquierda"},
                {"name": "Ligero ángulo derecho", "instructions": "Gira ligeramente la cabeza a la derecha"},
                {"name": "Sonriendo", "instructions": "Sonríe naturalmente"},
                {"name": "Expresión seria", "instructions": "Mantén expresión neutra"},
                {"name": "Con lentes (si usas)", "instructions": "Ponte los lentes si los usas"},
                {"name": "Sin lentes", "instructions": "Quítate los lentes si los tienes"},
                {"name": "Iluminación lateral", "instructions": "Muévete para que la luz venga del lado"},
            ]
            
            print("📋 Vamos a capturar encodings en diferentes condiciones:")
            print("   Presiona ESPACIO para capturar, ESC para saltar, Q para terminar")
            
            for i, scenario in enumerate(capture_scenarios):
                if len(captured_encodings) >= self.enhancement_config["target_encodings"]:
                    break
                
                print(f"\n🎯 Escenario {i+1}: {scenario['name']}")
                print(f"   {scenario['instructions']}")
                input("   Presiona ENTER cuando estés listo...")
                
                encoding = self._capture_single_encoding(cap, scenario['name'])
                if encoding:
                    captured_encodings.append({
                        'encoding': encoding,
                        'scenario': scenario['name'],
                        'timestamp': datetime.now().isoformat()
                    })
                    print(f"   ✅ Encoding capturado ({len(captured_encodings)}/{self.enhancement_config['target_encodings']})")
                else:
                    print("   ❌ No se pudo capturar encoding")
            
            cap.release()
            cv2.destroyAllWindows()
            
            if captured_encodings:
                self._save_enhanced_encodings(captured_encodings)
                print(f"\n✅ {len(captured_encodings)} encodings mejorados capturados")
                return True
            else:
                print("\n❌ No se capturaron encodings")
                return False
                
        except Exception as e:
            print(f"❌ Error capturando encodings: {e}")
            return False
    
    def _capture_single_encoding(self, cap, scenario_name: str):
        """Captura un solo encoding con validación de calidad"""
        attempts = 0
        max_attempts = 10
        
        while attempts < max_attempts:
            ret, frame = cap.read()
            if not ret:
                continue
            
            # Mostrar frame
            cv2.putText(frame, f"Escenario: {scenario_name}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, "ESPACIO: Capturar | ESC: Saltar | Q: Terminar", (10, frame.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            cv2.imshow('Hamilton - Captura de Encodings', frame)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord(' '):  # Espacio para capturar
                # Detectar rostros
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                face_locations = face_recognition.face_locations(rgb_frame)
                
                if len(face_locations) == 1:
                    # Exactamente un rostro detectado
                    face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)
                    if face_encodings:
                        # Validar calidad del encoding
                        if self._validate_encoding_quality(face_encodings[0], frame):
                            # Guardar imagen de entrenamiento
                            self._save_training_image(frame, scenario_name)
                            return face_encodings[0].tolist()
                        else:
                            print("   ⚠️ Calidad insuficiente, intenta de nuevo")
                    else:
                        print("   ❌ No se pudo generar encoding")
                elif len(face_locations) == 0:
                    print("   ❌ No se detectó rostro")
                else:
                    print("   ❌ Múltiples rostros detectados")
                
            elif key == 27:  # ESC para saltar
                return None
            elif key == ord('q'):  # Q para terminar
                return None
            
            attempts += 1
        
        print("   ⏰ Tiempo agotado para este escenario")
        return None
    
    def _validate_encoding_quality(self, encoding, frame) -> bool:
        """Valida la calidad del encoding capturado"""
        try:
            # Verificar que el encoding no sea muy similar a los existentes
            for existing_encoding in self.current_encodings:
                distance = np.linalg.norm(np.array(encoding) - np.array(existing_encoding))
                if distance < 0.1:  # Muy similar
                    return False
            
            # Verificar calidad de la imagen
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Calcular nitidez (Laplacian variance)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            if laplacian_var < 100:  # Imagen borrosa
                return False
            
            # Verificar iluminación
            mean_brightness = np.mean(gray)
            if mean_brightness < 50 or mean_brightness > 200:  # Muy oscura o muy clara
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validando calidad: {e}")
            return False
    
    def _save_training_image(self, frame, scenario_name: str):
        """Guarda imagen de entrenamiento para referencia"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"senor_ibero_{scenario_name.replace(' ', '_')}_{timestamp}.jpg"
            filepath = self.training_images_dir / filename
            
            cv2.imwrite(str(filepath), frame)
            
        except Exception as e:
            logger.warning(f"No se pudo guardar imagen de entrenamiento: {e}")
    
    def _save_enhanced_encodings(self, new_encodings: List[Dict]):
        """Guarda los encodings mejorados"""
        try:
            from recognition.face_recognition import hamilton_face
            from security.encryption import hamilton_encryption
            
            # Cargar encodings existentes
            existing_data = hamilton_face._load_face_encodings()
            if not existing_data:
                existing_data = {}
            
            if 'senor_ibero' not in existing_data:
                existing_data['senor_ibero'] = []
            
            # Agregar nuevos encodings
            for enc_data in new_encodings:
                existing_data['senor_ibero'].append(enc_data['encoding'])
            
            # Guardar encodings actualizados
            hamilton_face._save_face_encodings(existing_data)
            
            # Guardar metadata de mejora
            enhancement_metadata = {
                'enhancement_date': datetime.now().isoformat(),
                'new_encodings_count': len(new_encodings),
                'total_encodings': len(existing_data['senor_ibero']),
                'scenarios': [enc['scenario'] for enc in new_encodings]
            }
            
            metadata_file = self.enhanced_dir / "enhancement_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(enhancement_metadata, f, indent=2)
            
            print(f"✅ Encodings guardados. Total: {len(existing_data['senor_ibero'])}")
            
        except Exception as e:
            print(f"❌ Error guardando encodings: {e}")

def main():
    """Función principal"""
    print("🚀 HAMILTON - MEJORADOR DE RECONOCIMIENTO FACIAL")
    print("=" * 55)
    
    enhancer = FaceRecognitionEnhancer()
    
    # Paso 1: Analizar encodings actuales
    needs_enhancement = enhancer.analyze_current_encodings()
    
    if not needs_enhancement:
        print("\n✅ Los encodings actuales son suficientes")
        improve_anyway = input("¿Quieres mejorarlos de todas formas? (s/n): ").lower()
        if improve_anyway != 's':
            return True
    
    # Paso 2: Capturar encodings mejorados
    print("\n📸 Iniciando captura de encodings mejorados...")
    print("💡 Consejos para mejor captura:")
    print("   • Asegúrate de tener buena iluminación")
    print("   • Mantén la cara centrada en el frame")
    print("   • Evita movimientos bruscos")
    print("   • Sigue las instrucciones para cada escenario")
    
    input("\nPresiona ENTER para comenzar...")
    
    if enhancer.capture_enhanced_encodings():
        print("\n🎉 ¡MEJORA DE RECONOCIMIENTO FACIAL COMPLETADA!")
        print("Hamilton ahora tiene mejor precisión de reconocimiento facial.")
        print("\n📋 Próximos pasos:")
        print("1. Reinicia Hamilton para cargar los nuevos encodings")
        print("2. Prueba el reconocimiento facial")
        print("3. Si hay problemas, ejecuta este script nuevamente")
        return True
    else:
        print("\n❌ No se pudo completar la mejora")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
