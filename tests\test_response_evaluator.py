import pytest
from ai.response_evaluator import ResponseEvaluator

@pytest.fixture
def evaluator():
    """Fixture para crear una instancia del evaluador"""
    return ResponseEvaluator()

@pytest.fixture
def sample_context():
    """Fixture para crear un contexto de ejemplo"""
    return {
        "user_mood": "happy",
        "previous_topic": "tecnología",
        "user_preferences": ["programación", "IA", "robótica"]
    }

def test_evaluate_response_basic(evaluator):
    """Prueba la evaluación básica de una respuesta"""
    query = "¿Qué es la inteligencia artificial?"
    response = "La inteligencia artificial es un campo de la informática que se enfoca en crear sistemas capaces de realizar tareas que normalmente requieren inteligencia humana."
    
    metrics = evaluator.evaluate_response(query, response)
    
    assert isinstance(metrics, dict)
    assert all(key in metrics for key in ["relevance", "coherence", "completeness", "clarity", "context_usage"])
    assert all(0 <= value <= 1 for value in metrics.values())

def test_evaluate_response_with_context(evaluator, sample_context):
    """Prueba la evaluación de una respuesta con contexto"""
    query = "¿Qué opinas sobre la IA?"
    response = "La inteligencia artificial es fascinante, especialmente en el campo de la programación y la robótica. Es increíble ver cómo evoluciona."
    
    metrics = evaluator.evaluate_response(query, response, sample_context)
    
    assert metrics["context_usage"] > 0
    assert metrics["relevance"] > 0.5

def test_evaluate_response_coherence(evaluator):
    """Prueba la evaluación de coherencia"""
    # Respuesta coherente
    coherent_response = "Primero, analizaremos el problema. Segundo, propondremos una solución. Finalmente, implementaremos los cambios."
    
    # Respuesta incoherente
    incoherent_response = "Manzanas. Programación. Python. IA. Robótica."
    
    coherent_metrics = evaluator.evaluate_response("test", coherent_response)
    incoherent_metrics = evaluator.evaluate_response("test", incoherent_response)
    
    assert coherent_metrics["coherence"] > incoherent_metrics["coherence"]

def test_evaluate_response_completeness(evaluator):
    """Prueba la evaluación de completitud"""
    # Respuesta completa
    complete_response = "Primero, el problema. Segundo, la solución. Finalmente, la implementación."
    
    # Respuesta incompleta
    incomplete_response = "El problema es complejo."
    
    complete_metrics = evaluator.evaluate_response("test", complete_response)
    incomplete_metrics = evaluator.evaluate_response("test", incomplete_response)
    
    assert complete_metrics["completeness"] > incomplete_metrics["completeness"]

def test_evaluate_response_clarity(evaluator):
    """Prueba la evaluación de claridad"""
    # Respuesta clara
    clear_response = "La solución es simple: primero, analizamos el problema. Segundo, implementamos los cambios. Finalmente, verificamos los resultados."
    
    # Respuesta confusa
    confusing_response = "El problema es que la implementación de la solución requiere un análisis profundo de los factores involucrados en el proceso de toma de decisiones que afectan directamente a los resultados finales."
    
    clear_metrics = evaluator.evaluate_response("test", clear_response)
    confusing_metrics = evaluator.evaluate_response("test", confusing_response)
    
    assert clear_metrics["clarity"] > confusing_metrics["clarity"]

def test_evaluate_response_relevance(evaluator):
    """Prueba la evaluación de relevancia"""
    query = "¿Cómo funciona Python?"
    
    # Respuesta relevante
    relevant_response = "Python es un lenguaje de programación interpretado que se ejecuta línea por línea."
    
    # Respuesta no relevante
    irrelevant_response = "El clima está soleado hoy."
    
    relevant_metrics = evaluator.evaluate_response(query, relevant_response)
    irrelevant_metrics = evaluator.evaluate_response(query, irrelevant_response)
    
    assert relevant_metrics["relevance"] > irrelevant_metrics["relevance"]

def test_quality_metrics_update(evaluator):
    """Prueba la actualización de métricas de calidad"""
    initial_metrics = evaluator.get_quality_metrics()
    
    # Realizar varias evaluaciones
    for _ in range(5):
        evaluator.evaluate_response("test", "test response")
    
    updated_metrics = evaluator.get_quality_metrics()
    
    # Verificar que las métricas se han actualizado
    assert any(initial_metrics[key] != updated_metrics[key] for key in initial_metrics)

def test_evaluation_history(evaluator):
    """Prueba el historial de evaluaciones"""
    # Realizar algunas evaluaciones
    for i in range(3):
        evaluator.evaluate_response(f"query {i}", f"response {i}")
    
    history = evaluator.get_evaluation_history()
    
    assert len(history) == 3
    assert all("query" in entry and "response" in entry and "metrics" in entry for entry in history) 