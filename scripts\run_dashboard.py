#!/usr/bin/env python3
"""
Script para ejecutar el dashboard de monitoreo.
"""

import os
import sys
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/dashboard.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'interfaces/dashboard']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivo de métricas
        metrics_file = Path('logs/metrics.json')
        if not metrics_file.exists():
            with open(metrics_file, 'w') as f:
                f.write('[]')
            logger.info("Archivo de métricas creado")

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def run_dashboard():
    """Ejecuta el dashboard."""
    try:
        from interfaces.dashboard.app import app
        
        # Configurar el servidor
        host = os.getenv('DASHBOARD_HOST', 'localhost')
        port = int(os.getenv('DASHBOARD_PORT', '8050'))
        debug = os.getenv('DASHBOARD_DEBUG', 'False').lower() == 'true'
        
        logger.info(f"Iniciando dashboard en http://{host}:{port}")
        app.run_server(host=host, port=port, debug=debug)
        
    except Exception as e:
        logger.error(f"Error al ejecutar el dashboard: {str(e)}")
        sys.exit(1)

def main():
    """Función principal."""
    try:
        logger.info("Iniciando verificación del entorno...")
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            sys.exit(1)
            
        logger.info("Iniciando dashboard...")
        run_dashboard()
        
    except KeyboardInterrupt:
        logger.info("Dashboard detenido por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 