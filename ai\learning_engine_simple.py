"""
Hamilton AI Assistant - Learning Engine (Simplified)
Motor de aprendizaje incremental y adaptación personalizada
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter

from storage.database_manager import hamilton_db
from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class HamiltonLearningEngine:
    """
    Motor de aprendizaje de Hamilton
    Analiza patrones, aprende preferencias y adapta comportamiento
    """
    
    def __init__(self):
        self.pattern_threshold = 3  # Mínimo de ocurrencias para considerar un patrón
        self.confidence_decay = 0.95  # Factor de decaimiento de confianza
        self.learning_rate = 0.1  # Tasa de aprendizaje
    
    def learn_from_interaction(self, username: str, user_message: str, hamilton_response: str):
        """
        Aprende de una interacción usuario-Hamilton
        
        Args:
            username: Nombre del usuario
            user_message: Mensaje del usuario
            hamilton_response: Respuesta de Hamilton
        """
        try:
            # Analizar el mensaje del usuario
            message_analysis = self._analyze_message(user_message)
            
            # Inferir preferencias
            preferences = self._infer_preferences(user_message, hamilton_response)
            
            # Guardar preferencias aprendidas
            for pref_key, pref_data in preferences.items():
                hamilton_db.save_user_preference(
                    username=username,
                    key=pref_key,
                    value=pref_data['value'],
                    category=pref_data['category'],
                    learned_from='inferred',
                    confidence=pref_data['confidence']
                )
            
            logger.info(f"Aprendizaje completado para {username}: {len(preferences)} preferencias")
            
        except Exception as e:
            logger.error(f"Error en aprendizaje de interacción: {e}")
    
    def _analyze_message(self, message: str) -> Dict[str, Any]:
        """
        Analiza un mensaje del usuario para extraer características
        
        Args:
            message: Mensaje a analizar
            
        Returns:
            Diccionario con características del mensaje
        """
        message_lower = message.lower()
        
        analysis = {
            'length': len(message),
            'word_count': len(message.split()),
            'politeness_markers': [],
            'urgency_markers': [],
            'topic_keywords': [],
            'question_type': None
        }
        
        # Detectar marcadores de cortesía
        politeness_words = ['por favor', 'gracias', 'disculpa', 'perdón', 'amablemente']
        for word in politeness_words:
            if word in message_lower:
                analysis['politeness_markers'].append(word)
        
        # Detectar urgencia
        urgency_words = ['urgente', 'rápido', 'inmediatamente', 'ahora', 'ya']
        for word in urgency_words:
            if word in message_lower:
                analysis['urgency_markers'].append(word)
        
        # Detectar tipo de pregunta
        if '¿' in message or '?' in message:
            if any(word in message_lower for word in ['qué', 'que']):
                analysis['question_type'] = 'what'
            elif any(word in message_lower for word in ['cómo', 'como']):
                analysis['question_type'] = 'how'
            elif any(word in message_lower for word in ['cuándo', 'cuando']):
                analysis['question_type'] = 'when'
            elif any(word in message_lower for word in ['dónde', 'donde']):
                analysis['question_type'] = 'where'
            elif any(word in message_lower for word in ['por qué', 'porque']):
                analysis['question_type'] = 'why'
        
        # Detectar temas
        topic_keywords = {
            'tiempo': ['hora', 'tiempo', 'fecha', 'día'],
            'información': ['información', 'info', 'datos', 'detalles'],
            'ayuda': ['ayuda', 'asistencia', 'apoyo', 'auxilio'],
            'saludo': ['hola', 'buenos', 'buenas', 'saludos']
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                analysis['topic_keywords'].append(topic)
        
        return analysis
    
    def _infer_preferences(self, user_message: str, hamilton_response: str, feedback: str = None) -> Dict[str, Dict]:
        """
        Infiere preferencias del usuario basándose en la interacción
        
        Args:
            user_message: Mensaje del usuario
            hamilton_response: Respuesta de Hamilton
            feedback: Feedback opcional del usuario
            
        Returns:
            Diccionario con preferencias inferidas
        """
        preferences = {}
        
        # Inferir preferencia de longitud de respuesta
        if feedback:
            if any(word in feedback.lower() for word in ['corto', 'breve', 'resumido']):
                preferences['response_length'] = {
                    'value': 'short',
                    'confidence': 0.8,
                    'category': 'response_style'
                }
            elif any(word in feedback.lower() for word in ['detallado', 'completo', 'más información']):
                preferences['response_length'] = {
                    'value': 'detailed',
                    'confidence': 0.8,
                    'category': 'response_style'
                }
        
        # Inferir desde el mensaje del usuario también
        if any(word in user_message.lower() for word in ['breve', 'corto', 'resumido']):
            preferences['response_length'] = {
                'value': 'short',
                'confidence': 0.6,
                'category': 'response_style'
            }
        
        # Inferir preferencia de formalidad
        message_analysis = self._analyze_message(user_message)
        if message_analysis['politeness_markers']:
            preferences['communication_style'] = {
                'value': 'formal',
                'confidence': 0.7,
                'category': 'communication'
            }
        
        # Inferir temas de interés
        if message_analysis['topic_keywords']:
            for topic in message_analysis['topic_keywords']:
                preferences[f'interest_{topic}'] = {
                    'value': 'high',
                    'confidence': 0.5,
                    'category': 'interests'
                }
        
        return preferences
    
    def analyze_conversation_patterns(self, username: str, days_back: int = 30) -> Dict[str, Any]:
        """
        Analiza patrones en las conversaciones del usuario
        
        Args:
            username: Nombre del usuario
            days_back: Días hacia atrás para analizar
            
        Returns:
            Diccionario con patrones encontrados
        """
        try:
            user_id = hamilton_db.get_user_id(username)
            if not user_id:
                return {}
            
            # Obtener conversaciones recientes
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            with hamilton_db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT c.timestamp, c.speaker, c.message_text, c.intention
                    FROM conversations c
                    JOIN sessions s ON c.session_id = s.id
                    WHERE s.user_id = ? AND c.timestamp > ?
                    ORDER BY c.timestamp
                """, (user_id, cutoff_date))
                
                conversations = cursor.fetchall()
            
            patterns = {
                'time_patterns': self._analyze_time_patterns(conversations),
                'topic_patterns': self._analyze_topic_patterns(conversations),
                'command_patterns': self._analyze_command_patterns(conversations),
                'response_preferences': self._analyze_response_preferences(conversations)
            }
            
            logger.info(f"Análisis de patrones completado para {username}")
            return patterns
            
        except Exception as e:
            logger.error(f"Error analizando patrones de conversación: {e}")
            return {}
    
    def _analyze_time_patterns(self, conversations: List) -> Dict[str, Any]:
        """Analiza patrones temporales de uso"""
        if not conversations:
            return {}
        
        hours = []
        days = []
        
        for conv in conversations:
            try:
                timestamp = datetime.fromisoformat(conv[0])
                hours.append(timestamp.hour)
                days.append(timestamp.weekday())
            except:
                continue
        
        hour_counter = Counter(hours)
        day_counter = Counter(days)
        
        return {
            'most_active_hours': hour_counter.most_common(3),
            'most_active_days': day_counter.most_common(3),
            'total_interactions': len(conversations)
        }
    
    def _analyze_topic_patterns(self, conversations: List) -> Dict[str, Any]:
        """Analiza patrones de temas de conversación"""
        topics = []
        
        for conv in conversations:
            if conv[3]:  # intention field
                topics.append(conv[3])
        
        topic_counter = Counter(topics)
        
        return {
            'favorite_topics': topic_counter.most_common(5),
            'topic_diversity': len(set(topics))
        }
    
    def _analyze_command_patterns(self, conversations: List) -> Dict[str, Any]:
        """Analiza patrones de comandos"""
        user_messages = [conv for conv in conversations if conv[1] == 'user']
        
        if not user_messages:
            return {}
        
        command_lengths = [len(conv[2]) if conv[2] else 0 for conv in user_messages]
        avg_length = sum(command_lengths) / len(command_lengths) if command_lengths else 0
        
        return {
            'average_command_length': avg_length,
            'total_commands': len(user_messages),
            'command_frequency': len(user_messages) / max(1, len(conversations))
        }
    
    def _analyze_response_preferences(self, conversations: List) -> Dict[str, Any]:
        """Analiza preferencias de respuesta"""
        hamilton_responses = [conv for conv in conversations if conv[1] == 'hamilton']
        
        if not hamilton_responses:
            return {}
        
        response_lengths = [len(conv[2]) if conv[2] else 0 for conv in hamilton_responses]
        avg_response_length = sum(response_lengths) / len(response_lengths) if response_lengths else 0
        
        return {
            'average_response_length': avg_response_length,
            'total_responses': len(hamilton_responses)
        }
    
    def get_personalized_response_style(self, username: str) -> Dict[str, Any]:
        """
        Obtiene el estilo de respuesta personalizado para el usuario
        
        Args:
            username: Nombre del usuario
            
        Returns:
            Diccionario con configuración de estilo personalizado
        """
        try:
            preferences = hamilton_db.get_user_preferences(username)
            
            style = {
                'length': 'medium',
                'formality': 'formal',
                'detail_level': 'standard'
            }
            
            # Aplicar preferencias aprendidas
            if 'response_length' in preferences:
                style['length'] = preferences['response_length']['value']
            
            if 'communication_style' in preferences:
                style['formality'] = preferences['communication_style']['value']
            
            return style
            
        except Exception as e:
            logger.error(f"Error obteniendo estilo personalizado: {e}")
            return {'length': 'medium', 'formality': 'formal', 'detail_level': 'standard'}

# Instancia global
hamilton_learning = HamiltonLearningEngine()
