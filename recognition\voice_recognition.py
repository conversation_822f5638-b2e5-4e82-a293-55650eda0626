"""
Hamilton AI Assistant - Voice Recognition Module
Módulo de reconocimiento de voz y síntesis de voz masculina para Hamilton
"""

import speech_recognition as sr
import pyttsx3
import asyncio
import logging
from typing import Optional, Dict, Any
import threading
import time
from config.settings import settings, get_male_voice_config

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HamiltonVoiceEngine:
    """
    Motor de voz de Hamilton con reconocimiento de voz y síntesis de voz masculina
    """
    
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.tts_engine = None
        self.is_listening = False
        self.voice_thread = None
        self._setup_tts_engine()
        self._calibrate_microphone()
        
    def _setup_tts_engine(self):
        """Configura el motor de síntesis de voz con voz masculina"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configurar voz masculina
            voices = self.tts_engine.getProperty('voices')
            male_voice = None
            
            # Buscar voz masculina en español
            for voice in voices:
                voice_name = voice.name.lower()
                voice_id = voice.id.lower()
                
                # Priorizar voces masculinas en español
                if any(keyword in voice_name for keyword in ['male', 'masculino', 'hombre', 'alvaro', 'diego']):
                    if any(lang in voice_id for lang in ['es', 'spanish', 'español']):
                        male_voice = voice.id
                        break
                        
                # Fallback: cualquier voz masculina
                elif 'male' in voice_name or 'masculino' in voice_name:
                    male_voice = voice.id
            
            if male_voice:
                self.tts_engine.setProperty('voice', male_voice)
                logger.info(f"Voz masculina configurada: {male_voice}")
            else:
                logger.warning("No se encontró voz masculina específica, usando voz por defecto")
            
            # Configurar propiedades de voz
            self.tts_engine.setProperty('rate', settings.TTS_VOICE_RATE)
            self.tts_engine.setProperty('volume', settings.TTS_VOICE_VOLUME)
            
            logger.info("Motor TTS configurado con voz masculina")
            
        except Exception as e:
            logger.error(f"Error configurando motor TTS: {e}")
            self.tts_engine = None
    
    def _calibrate_microphone(self):
        """Calibra el micrófono para el ruido ambiente"""
        try:
            with self.microphone as source:
                logger.info("Calibrando micrófono para ruido ambiente...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                logger.info("Calibración completada")
        except Exception as e:
            logger.error(f"Error calibrando micrófono: {e}")
    
    def speak(self, text: str, interrupt_current: bool = True):
        """
        Hamilton habla con voz masculina
        
        Args:
            text: Texto a pronunciar
            interrupt_current: Si interrumpir el habla actual
        """
        if not self.tts_engine:
            logger.error("Motor TTS no disponible")
            return
            
        try:
            if interrupt_current:
                self.tts_engine.stop()
            
            logger.info(f"Hamilton dice: {text}")
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
            
        except Exception as e:
            logger.error(f"Error en síntesis de voz: {e}")
    
    def listen_once(self, timeout: int = 5) -> Optional[str]:
        """
        Escucha una vez y retorna el texto reconocido
        
        Args:
            timeout: Tiempo límite en segundos
            
        Returns:
            Texto reconocido o None si no se reconoce nada
        """
        try:
            with self.microphone as source:
                logger.info("Escuchando...")
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout,
                    phrase_time_limit=settings.SPEECH_RECOGNITION_PHRASE_TIMEOUT
                )
            
            logger.info("Procesando audio...")
            text = self.recognizer.recognize_google(
                audio, 
                language=settings.SPEECH_RECOGNITION_LANGUAGE
            )
            
            logger.info(f"Texto reconocido: {text}")
            return text.lower().strip()
            
        except sr.WaitTimeoutError:
            logger.info("Tiempo de espera agotado")
            return None
        except sr.UnknownValueError:
            logger.info("No se pudo entender el audio")
            return None
        except sr.RequestError as e:
            logger.error(f"Error en el servicio de reconocimiento: {e}")
            return None
        except Exception as e:
            logger.error(f"Error inesperado en reconocimiento: {e}")
            return None
    
    def start_continuous_listening(self, callback_function):
        """
        Inicia escucha continua en un hilo separado
        
        Args:
            callback_function: Función a llamar cuando se reconoce texto
        """
        if self.is_listening:
            logger.warning("Ya se está escuchando continuamente")
            return
        
        self.is_listening = True
        self.voice_thread = threading.Thread(
            target=self._continuous_listen_worker,
            args=(callback_function,),
            daemon=True
        )
        self.voice_thread.start()
        logger.info("Escucha continua iniciada")
    
    def stop_continuous_listening(self):
        """Detiene la escucha continua"""
        self.is_listening = False
        if self.voice_thread and self.voice_thread.is_alive():
            self.voice_thread.join(timeout=2)
        logger.info("Escucha continua detenida")
    
    def _continuous_listen_worker(self, callback_function):
        """Worker para escucha continua"""
        while self.is_listening:
            try:
                text = self.listen_once(timeout=1)
                if text and callback_function:
                    callback_function(text)
                time.sleep(0.1)  # Pequeña pausa para evitar sobrecarga
            except Exception as e:
                logger.error(f"Error en escucha continua: {e}")
                time.sleep(1)
    
    def is_wake_word(self, text: str) -> bool:
        """
        Verifica si el texto contiene la palabra de activación para Hamilton
        
        Args:
            text: Texto a verificar
            
        Returns:
            True si contiene palabra de activación
        """
        wake_words = [
            "hamilton",
            "hey hamilton",
            "hola hamilton",
            "hamilton ayuda",
            "hamilton asistente"
        ]
        
        text_lower = text.lower().strip()
        return any(wake_word in text_lower for wake_word in wake_words)
    
    def get_voice_info(self) -> Dict[str, Any]:
        """Retorna información sobre la configuración de voz actual"""
        if not self.tts_engine:
            return {"error": "Motor TTS no disponible"}
        
        try:
            current_voice = self.tts_engine.getProperty('voice')
            rate = self.tts_engine.getProperty('rate')
            volume = self.tts_engine.getProperty('volume')
            
            return {
                "current_voice": current_voice,
                "rate": rate,
                "volume": volume,
                "gender": "male",
                "language": settings.SPEECH_RECOGNITION_LANGUAGE
            }
        except Exception as e:
            return {"error": f"Error obteniendo información de voz: {e}"}

# Instancia global del motor de voz
hamilton_voice = HamiltonVoiceEngine()
