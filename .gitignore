# Hamilton AI Assistant - Git Ignore

# Environment variables
.env
.env.local
.env.production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
hamilton_env/
venv/
env/
ENV/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Storage directories (contienen datos sensibles)
storage/faces/
storage/voices/
storage/conversations/
uploads/
models/

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp

# Node.js (para futuras integraciones web)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebooks
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Backup files
*.bak
*.backup

# API Keys y credenciales
credentials.json
*.pem
*.key

# Archivos de configuración específicos del usuario
config/local_settings.py

# Archivos de prueba temporales
test_images/
test_audio/

# Archivos de modelo entrenados (pueden ser grandes)
*.pkl
*.joblib
*.h5
*.pt
*.pth

# Documentación generada
docs/_build/
