#!/usr/bin/env python3
"""
Hamilton AI Assistant - De<PERSON> Script
Demostración de las capacidades de Hamilton
"""

import sys
import time
from pathlib import Path

# Agregar directorio actual al path
sys.path.insert(0, str(Path(__file__).parent))

from core.hamilton_core import hamilton
from recognition.voice_recognition import hamilton_voice
from logic.prolog_engine import hamilton_prolog

def print_header():
    """Imprime el header de la demostración"""
    print("="*60)
    print("🤖 HAMILTON AI ASSISTANT - DEMOSTRACIÓN")
    print("="*60)
    print("Asistente personal de IA para señor Ibero")
    print("Con reconocimiento facial, voz masculina y razonamiento avanzado")
    print()

def demo_voice_synthesis():
    """Demuestra la síntesis de voz masculina"""
    print("🔊 DEMOSTRACIÓN DE VOZ MASCULINA")
    print("-" * 40)
    
    messages = [
        "Hola señor <PERSON>, soy <PERSON>, su asistente personal.",
        "Mi voz ha sido configurada específicamente para ser masculina.",
        "Puedo ayudarle con diversas tareas y consultas.",
        "¿En qué puedo asistirle hoy?"
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"{i}. Hamilton dice: {message}")
        hamilton_voice.speak(message)
        time.sleep(1)
    
    print("✅ Demostración de voz completada")

def demo_command_processing():
    """Demuestra el procesamiento de comandos"""
    print("\n💬 DEMOSTRACIÓN DE PROCESAMIENTO DE COMANDOS")
    print("-" * 50)
    
    test_commands = [
        "hola hamilton",
        "¿qué hora es?",
        "¿cómo estás?",
        "¿qué día es?",
        "hamilton ayuda",
        "hamilton información",
        "¿puedes ayudarme con matemáticas?",
        "buenos días hamilton"
    ]
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n{i}. 👤 Usuario: {command}")
        response = hamilton._process_command(command)
        print(f"   🤖 Hamilton: {response}")
        
        # Síntesis de voz para algunas respuestas
        if i <= 4:  # Solo las primeras 4 para no hacer muy larga la demo
            hamilton_voice.speak(response)
            time.sleep(0.5)
    
    print("\n✅ Demostración de comandos completada")

def demo_system_status():
    """Demuestra el estado del sistema"""
    print("\n📊 ESTADO DEL SISTEMA HAMILTON")
    print("-" * 40)
    
    status = hamilton.get_status()
    
    print(f"Estado actual: {status['status']}")
    print(f"Usuario autenticado: {status['current_user'] or 'Ninguno'}")
    print(f"Conversaciones: {status['conversation_entries']}")
    
    # Información del motor Prolog
    prolog_info = status.get('prolog_engine', {})
    prolog_status = "✅ Activo" if prolog_info.get('available') else "⚠️ No disponible"
    print(f"Motor de razonamiento: {prolog_status}")
    
    if prolog_info.get('available'):
        print(f"Base de conocimientos: {'✅ Cargada' if prolog_info.get('knowledge_base_loaded') else '❌ No cargada'}")
        print(f"Hechos en memoria: {prolog_info.get('facts_count', 0)}")
    
    # Información del motor de voz
    voice_info = hamilton_voice.get_voice_info()
    print(f"Voz configurada: {voice_info.get('gender', 'unknown')} ({voice_info.get('language', 'unknown')})")
    
    print("✅ Estado del sistema mostrado")

def demo_reasoning_capabilities():
    """Demuestra las capacidades de razonamiento"""
    print("\n🧠 DEMOSTRACIÓN DE RAZONAMIENTO")
    print("-" * 40)
    
    prolog_info = hamilton_prolog.get_system_info()
    
    if prolog_info['prolog_available']:
        print("✅ Motor Prolog disponible - Razonamiento avanzado activo")
        
        # Demostrar consultas Prolog
        print("\n🔍 Consultas de razonamiento:")
        
        # Verificar autorización
        can_activate = hamilton_prolog.can_user_activate("senor_ibero")
        print(f"¿Puede señor_ibero activar Hamilton? {'✅ Sí' if can_activate else '❌ No'}")
        
        # Saludo apropiado
        from datetime import datetime
        hour = datetime.now().hour
        greeting = hamilton_prolog.get_appropriate_greeting(hour)
        print(f"Saludo apropiado para hora {hour}: {greeting}")
        
        # Análisis de intención
        intention = hamilton_prolog.analyze_user_intention("senor_ibero", "¿qué hora es?")
        print(f"Intención de '¿qué hora es?': {intention}")
        
    else:
        print("⚠️ Motor Prolog no disponible - Usando razonamiento simplificado")
        print("Para habilitar razonamiento avanzado:")
        print("1. Instalar SWI-Prolog: https://www.swi-prolog.org/download/stable")
        print("2. Reiniciar Hamilton")
        
        # Demostrar fallbacks
        print("\n🔄 Razonamiento simplificado:")
        can_activate = hamilton_prolog.can_user_activate("senor_ibero")
        print(f"¿Puede señor_ibero activar Hamilton? {'✅ Sí' if can_activate else '❌ No'}")
        
        from datetime import datetime
        hour = datetime.now().hour
        greeting = hamilton_prolog.get_appropriate_greeting(hour)
        print(f"Saludo apropiado para hora {hour}: {greeting}")
    
    print("✅ Demostración de razonamiento completada")

def demo_conversation_flow():
    """Demuestra un flujo de conversación completo"""
    print("\n💭 DEMOSTRACIÓN DE CONVERSACIÓN COMPLETA")
    print("-" * 50)
    
    conversation = [
        ("Usuario", "Hamilton"),
        ("Hamilton", "Buenas noches, señor Ibero. Soy Hamilton, su asistente personal. ¿En qué puedo asistirle?"),
        ("Usuario", "¿Cómo estás?"),
        ("Hamilton", None),  # Respuesta generada
        ("Usuario", "¿Qué hora es?"),
        ("Hamilton", None),  # Respuesta generada
        ("Usuario", "Hamilton información"),
        ("Hamilton", None),  # Respuesta generada
        ("Usuario", "Gracias Hamilton"),
        ("Hamilton", "De nada, señor Ibero. Siempre es un placer ayudarle.")
    ]
    
    for speaker, message in conversation:
        if speaker == "Usuario":
            print(f"\n👤 {speaker}: {message}")
            if message != "Hamilton":  # No procesar la palabra de activación
                time.sleep(1)
        else:  # Hamilton
            if message is None:
                # Generar respuesta
                prev_user_msg = conversation[conversation.index((speaker, message)) - 1][1]
                response = hamilton._process_command(prev_user_msg)
                print(f"🤖 {speaker}: {response}")
                hamilton_voice.speak(response)
            else:
                print(f"🤖 {speaker}: {message}")
                hamilton_voice.speak(message)
            time.sleep(1)
    
    print("\n✅ Demostración de conversación completada")

def main():
    """Función principal de la demostración"""
    print_header()
    
    try:
        # Secuencia de demostraciones
        demos = [
            ("Síntesis de Voz", demo_voice_synthesis),
            ("Estado del Sistema", demo_system_status),
            ("Capacidades de Razonamiento", demo_reasoning_capabilities),
            ("Procesamiento de Comandos", demo_command_processing),
            ("Conversación Completa", demo_conversation_flow)
        ]
        
        for demo_name, demo_func in demos:
            print(f"\n{'='*20} {demo_name.upper()} {'='*20}")
            demo_func()
            
            # Pausa entre demostraciones
            input("\nPresione ENTER para continuar a la siguiente demostración...")
        
        # Resumen final
        print("\n" + "="*60)
        print("🎉 DEMOSTRACIÓN COMPLETADA")
        print("="*60)
        print("Hamilton ha demostrado sus capacidades principales:")
        print("✅ Síntesis de voz masculina")
        print("✅ Procesamiento de comandos inteligente")
        print("✅ Razonamiento lógico (con/sin Prolog)")
        print("✅ Conversación natural")
        print("✅ Información del sistema")
        
        print("\n💡 Para usar Hamilton completamente:")
        print("1. Registre su cara: python register_face.py")
        print("2. Ejecute Hamilton: python main.py")
        print("3. Diga 'Hamilton' para activarlo")
        
        if not hamilton_prolog.get_system_info()['prolog_available']:
            print("\n🔧 Para habilitar razonamiento avanzado:")
            print("1. Instale SWI-Prolog")
            print("2. Reinicie Hamilton")
        
        print("\n🤖 ¡Hamilton está listo para ser su asistente personal!")
        
    except KeyboardInterrupt:
        print("\n\nDemostración interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error en demostración: {e}")
    finally:
        print("\nCerrando demostración...")

if __name__ == "__main__":
    main()
