"""
Hamilton AI Assistant - Natural Response Generator
Generador de respuestas más naturales y contextuales
"""

import logging
import json
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, time
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class ResponseStyle(Enum):
    """Estilos de respuesta"""
    FORMAL = "formal"
    CASUAL = "casual"
    ENTHUSIASTIC = "enthusiastic"
    EMPATHETIC = "empathetic"
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"

class ResponseTone(Enum):
    """Tonos de respuesta"""
    NEUTRAL = "neutral"
    WARM = "warm"
    CHEERFUL = "cheerful"
    CALM = "calm"
    CONFIDENT = "confident"
    GENTLE = "gentle"

@dataclass
class ResponseContext:
    """Contexto para generación de respuesta"""
    user_name: str
    user_emotion: Optional[str] = None
    time_of_day: Optional[str] = None
    conversation_history: List[Dict] = None
    user_preferences: Dict[str, Any] = None
    current_activity: Optional[str] = None

class NaturalResponseGenerator:
    """Generador de respuestas naturales para Hamilton"""
    
    def __init__(self):
        self.response_templates = {}
        self.personality_traits = {}
        self.contextual_modifiers = {}
        self.config_file = Path("config/response_generation.json")
        
        self.load_configuration()
        self._setup_hamilton_personality()
        self._setup_response_templates()
    
    def load_configuration(self):
        """Carga configuración del generador de respuestas"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de respuestas: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.config = {
            "hamilton_personality": {
                "base_traits": {
                    "formal": 0.8,
                    "helpful": 0.9,
                    "respectful": 0.9,
                    "professional": 0.8,
                    "warm": 0.7,
                    "intelligent": 0.9,
                    "reliable": 0.9
                },
                "voice_characteristics": {
                    "gender": "masculine",
                    "formality_level": "formal_but_warm",
                    "preferred_address": "señor_ibero",
                    "signature_phrases": [
                        "A su servicio",
                        "Por supuesto",
                        "Será un placer",
                        "Entendido perfectamente"
                    ]
                },
                "cultural_context": {
                    "language": "spanish",
                    "region": "mexico",
                    "formality_preference": "high",
                    "time_awareness": True
                }
            },
            "response_adaptation": {
                "adapt_to_emotion": True,
                "adapt_to_time": True,
                "adapt_to_context": True,
                "maintain_consistency": True,
                "learning_enabled": True
            },
            "natural_language_features": {
                "use_contractions": False,  # Mantener formalidad
                "use_filler_words": True,
                "vary_sentence_structure": True,
                "add_personality_touches": True,
                "contextual_references": True
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def _setup_hamilton_personality(self):
        """Configura la personalidad de Hamilton"""
        personality_config = self.config["hamilton_personality"]
        
        self.personality_traits = {
            "greeting_style": "formal_warm",
            "response_length": "appropriate",
            "humor_level": "subtle",
            "empathy_level": "high",
            "technical_depth": "adaptive",
            "cultural_sensitivity": "high"
        }
        
        # Frases características de Hamilton
        self.signature_phrases = personality_config["voice_characteristics"]["signature_phrases"]
        
        # Modificadores contextuales
        self.contextual_modifiers = {
            "morning": {
                "greetings": ["Buenos días", "Muy buenos días"],
                "energy": "fresh",
                "tone": "alert"
            },
            "afternoon": {
                "greetings": ["Buenas tardes", "Muy buenas tardes"],
                "energy": "steady",
                "tone": "professional"
            },
            "evening": {
                "greetings": ["Buenas noches", "Muy buenas noches"],
                "energy": "calm",
                "tone": "relaxed"
            },
            "late_night": {
                "greetings": ["Buenas noches"],
                "energy": "gentle",
                "tone": "soft",
                "suggestions": ["¿No debería descansar?"]
            }
        }
    
    def _setup_response_templates(self):
        """Configura plantillas de respuesta"""
        self.response_templates = {
            "greeting": {
                "formal": [
                    "{greeting}, {user_name}. Soy Hamilton, su asistente personal. {availability}",
                    "{greeting}, {user_name}. Hamilton a su servicio. {availability}",
                    "{greeting}, {user_name}. Es un placer atenderle. {availability}"
                ],
                "warm": [
                    "{greeting}, {user_name}. Me alegra saludarle. {availability}",
                    "{greeting}, {user_name}. ¿Cómo se encuentra hoy? {availability}"
                ]
            },
            "acknowledgment": {
                "understanding": [
                    "Entendido perfectamente, {user_name}.",
                    "Por supuesto, {user_name}.",
                    "Comprendo su solicitud, {user_name}.",
                    "Perfectamente claro, {user_name}."
                ],
                "processing": [
                    "Permítame procesar su solicitud, {user_name}.",
                    "Un momento mientras analizo esto, {user_name}.",
                    "Procesando su petición, {user_name}."
                ]
            },
            "information_delivery": {
                "direct": [
                    "La información que solicita es la siguiente: {content}",
                    "Según mis datos: {content}",
                    "Puedo informarle que: {content}"
                ],
                "contextual": [
                    "Basándome en su consulta, {content}",
                    "En relación a lo que pregunta, {content}",
                    "Respecto a su solicitud, {content}"
                ]
            },
            "assistance_offer": {
                "general": [
                    "¿En qué más puedo asistirle, {user_name}?",
                    "¿Hay algo más en lo que pueda ayudarle?",
                    "¿Necesita alguna otra cosa, {user_name}?"
                ],
                "specific": [
                    "¿Le gustaría que profundice en algún aspecto específico?",
                    "¿Desea que le proporcione información adicional sobre este tema?",
                    "¿Hay algún detalle particular que le interese conocer?"
                ]
            },
            "error_handling": {
                "clarification": [
                    "Disculpe, {user_name}, ¿podría aclarar su solicitud?",
                    "Perdón, no estoy seguro de haber comprendido. ¿Podría reformular?",
                    "Me temo que necesito más información para ayudarle adecuadamente."
                ],
                "limitation": [
                    "Lamento informarle que no puedo realizar esa acción en este momento.",
                    "Me disculpo, pero esa función no está disponible actualmente.",
                    "Por el momento, no tengo acceso a esa información."
                ]
            },
            "farewell": {
                "standard": [
                    "Hasta luego, {user_name}. Ha sido un placer asistirle.",
                    "Que tenga un excelente {time_period}, {user_name}.",
                    "Hasta la próxima, {user_name}. Hamilton entrando en modo dormido."
                ],
                "encouraging": [
                    "Que tenga un día productivo, {user_name}.",
                    "Espero haber sido de ayuda. Hasta pronto, {user_name}."
                ]
            }
        }
    
    def generate_response(self, intent: str, content: str, context: ResponseContext) -> str:
        """Genera respuesta natural basada en intención y contexto"""
        try:
            # Determinar estilo y tono basado en contexto
            style, tone = self._determine_style_and_tone(context)
            
            # Seleccionar plantilla apropiada
            template = self._select_template(intent, style, context)
            
            # Generar respuesta base
            base_response = self._build_base_response(template, content, context)
            
            # Aplicar modificadores contextuales
            enhanced_response = self._apply_contextual_enhancements(base_response, context, tone)
            
            # Agregar toques de personalidad
            final_response = self._add_personality_touches(enhanced_response, context)
            
            logger.info(f"Respuesta generada para intención '{intent}': {len(final_response)} caracteres")
            return final_response
            
        except Exception as e:
            logger.error(f"Error generando respuesta: {e}")
            return self._get_fallback_response(context)
    
    def _determine_style_and_tone(self, context: ResponseContext) -> tuple[ResponseStyle, ResponseTone]:
        """Determina estilo y tono basado en contexto"""
        # Estilo base: formal para señor Ibero
        style = ResponseStyle.FORMAL
        tone = ResponseTone.WARM
        
        # Ajustar según emoción del usuario
        if context.user_emotion:
            emotion = context.user_emotion.lower()
            if emotion in ['sad', 'frustrated', 'angry']:
                style = ResponseStyle.EMPATHETIC
                tone = ResponseTone.GENTLE
            elif emotion in ['happy', 'excited']:
                style = ResponseStyle.FRIENDLY
                tone = ResponseTone.CHEERFUL
            elif emotion in ['tired', 'stressed']:
                style = ResponseStyle.EMPATHETIC
                tone = ResponseTone.CALM
        
        # Ajustar según hora del día
        if context.time_of_day:
            if context.time_of_day == 'late_night':
                tone = ResponseTone.GENTLE
            elif context.time_of_day == 'morning':
                tone = ResponseTone.CHEERFUL
        
        return style, tone
    
    def _select_template(self, intent: str, style: ResponseStyle, context: ResponseContext) -> str:
        """Selecciona plantilla apropiada"""
        # Mapear intenciones a categorías de plantillas
        intent_mapping = {
            'greeting': 'greeting',
            'question_time': 'information_delivery',
            'question_weather': 'information_delivery',
            'command_lights': 'acknowledgment',
            'request_help': 'assistance_offer',
            'information_request': 'information_delivery',
            'unknown': 'error_handling'
        }
        
        template_category = intent_mapping.get(intent, 'information_delivery')
        
        # Seleccionar subcategoría basada en estilo
        if template_category in self.response_templates:
            templates = self.response_templates[template_category]
            
            if style == ResponseStyle.FORMAL and 'formal' in templates:
                return random.choice(templates['formal'])
            elif style == ResponseStyle.EMPATHETIC and 'warm' in templates:
                return random.choice(templates['warm'])
            elif 'general' in templates:
                return random.choice(templates['general'])
            else:
                # Usar primera opción disponible
                first_key = list(templates.keys())[0]
                return random.choice(templates[first_key])
        
        # Plantilla por defecto
        return "{content}"
    
    def _build_base_response(self, template: str, content: str, context: ResponseContext) -> str:
        """Construye respuesta base usando plantilla"""
        # Determinar saludo apropiado
        greeting = self._get_appropriate_greeting(context.time_of_day)
        
        # Determinar disponibilidad
        availability = self._get_availability_phrase(context)
        
        # Determinar período del día para despedidas
        time_period = self._get_time_period(context.time_of_day)
        
        # Formatear plantilla
        formatted_response = template.format(
            user_name=context.user_name,
            content=content,
            greeting=greeting,
            availability=availability,
            time_period=time_period
        )
        
        return formatted_response
    
    def _get_appropriate_greeting(self, time_of_day: Optional[str]) -> str:
        """Obtiene saludo apropiado según hora"""
        if not time_of_day:
            return "Hola"
        
        modifiers = self.contextual_modifiers.get(time_of_day, {})
        greetings = modifiers.get('greetings', ['Hola'])
        
        return random.choice(greetings)
    
    def _get_availability_phrase(self, context: ResponseContext) -> str:
        """Obtiene frase de disponibilidad"""
        availability_phrases = [
            "¿En qué puedo asistirle?",
            "¿Cómo puedo ayudarle?",
            "¿Qué necesita?",
            "A su disposición."
        ]
        
        # Ajustar según contexto
        if context.user_emotion == 'tired':
            availability_phrases = [
                "¿En qué puedo ayudarle? Noto que podría estar cansado.",
                "¿Necesita algo específico? Estoy aquí para asistirle."
            ]
        
        return random.choice(availability_phrases)
    
    def _get_time_period(self, time_of_day: Optional[str]) -> str:
        """Obtiene período del día para despedidas"""
        time_periods = {
            'morning': 'día',
            'afternoon': 'tarde',
            'evening': 'noche',
            'late_night': 'descanso'
        }
        
        return time_periods.get(time_of_day, 'día')
    
    def _apply_contextual_enhancements(self, response: str, context: ResponseContext, tone: ResponseTone) -> str:
        """Aplica mejoras contextuales"""
        enhanced = response
        
        # Agregar referencias contextuales
        if context.conversation_history and len(context.conversation_history) > 0:
            last_topic = self._extract_last_topic(context.conversation_history)
            if last_topic and "información" in response.lower():
                enhanced = enhanced.replace("información", f"información sobre {last_topic}")
        
        # Ajustar según tono
        if tone == ResponseTone.GENTLE:
            enhanced = enhanced.replace(".", ", si me permite.")
            enhanced = enhanced.replace("?", " por favor?")
        elif tone == ResponseTone.CHEERFUL:
            enhanced = enhanced.replace(".", "!")
        
        return enhanced
    
    def _extract_last_topic(self, history: List[Dict]) -> Optional[str]:
        """Extrae último tema de conversación"""
        if not history:
            return None
        
        last_message = history[-1]
        content = last_message.get('content', '').lower()
        
        # Buscar temas comunes
        topics = {
            'tiempo': ['hora', 'tiempo', 'fecha'],
            'clima': ['clima', 'lluvia', 'sol', 'temperatura'],
            'casa': ['luz', 'luces', 'termostato', 'casa'],
            'música': ['música', 'canción', 'reproducir']
        }
        
        for topic, keywords in topics.items():
            if any(keyword in content for keyword in keywords):
                return topic
        
        return None
    
    def _add_personality_touches(self, response: str, context: ResponseContext) -> str:
        """Agrega toques de personalidad de Hamilton"""
        enhanced = response
        
        # Agregar frases características ocasionalmente
        if random.random() < 0.3:  # 30% de probabilidad
            signature = random.choice(self.signature_phrases)
            if not enhanced.endswith('.'):
                enhanced += f". {signature}."
            else:
                enhanced = enhanced[:-1] + f". {signature}."
        
        # Agregar referencias temporales apropiadas
        if context.time_of_day == 'late_night' and random.random() < 0.5:
            enhanced += " ¿No sería conveniente que descansara?"
        
        return enhanced
    
    def _get_fallback_response(self, context: ResponseContext) -> str:
        """Obtiene respuesta de respaldo en caso de error"""
        fallbacks = [
            f"Disculpe, {context.user_name}, permítame procesar mejor su solicitud.",
            f"Un momento, {context.user_name}, estoy analizando su petición.",
            f"Por favor, {context.user_name}, ¿podría reformular su consulta?"
        ]
        
        return random.choice(fallbacks)
    
    def get_response_style_for_user(self, username: str) -> Dict[str, Any]:
        """Obtiene estilo de respuesta personalizado para usuario"""
        # En implementación real, obtener de base de datos de preferencias
        return {
            'formality': 'high',
            'warmth': 'medium',
            'verbosity': 'appropriate',
            'technical_level': 'adaptive'
        }

# Instancia global
hamilton_response_generator = NaturalResponseGenerator()
