#!/usr/bin/env python3
"""
Script para ejecutar el análisis de rendimiento del sistema.
"""

import os
import sys
import json
import time
import logging
import psutil
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/performance.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'analysis']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivo de métricas
        metrics_file = Path('logs/metrics.json')
        if not metrics_file.exists():
            logger.error("Archivo de métricas no encontrado")
            return False

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def load_metrics(hours=24):
    """Carga las métricas del archivo."""
    try:
        metrics_file = Path('logs/metrics.json')
        
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
            
        # Filtrar por tiempo
        cutoff_time = datetime.now() - timedelta(hours=hours)
        filtered_metrics = [
            m for m in metrics
            if datetime.fromisoformat(m['timestamp']) > cutoff_time
        ]
        
        return filtered_metrics
    except Exception as e:
        logger.error(f"Error al cargar métricas: {str(e)}")
        return None

def prepare_data(metrics):
    """Prepara los datos para análisis."""
    try:
        # Convertir a DataFrame
        df = pd.DataFrame(metrics)
        
        # Convertir timestamp a datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Extraer métricas del sistema
        system_metrics = pd.DataFrame([
            {
                'timestamp': row['timestamp'],
                'cpu_percent': row['cpu']['percent'],
                'memory_percent': row['memory']['percent'],
                'disk_percent': row['disk']['percent'],
                'swap_percent': row['swap']['percent'],
                'processes': row['processes']
            }
            for row in metrics
        ])
        
        # Extraer métricas de procesos
        process_metrics = []
        for row in metrics:
            for proc in row['processes']:
                process_metrics.append({
                    'timestamp': row['timestamp'],
                    'pid': proc['pid'],
                    'name': proc['name'],
                    'cpu_percent': proc['cpu_percent'],
                    'memory_percent': proc['memory_percent']
                })
                
        process_df = pd.DataFrame(process_metrics)
        
        return system_metrics, process_df
    except Exception as e:
        logger.error(f"Error al preparar datos: {str(e)}")
        return None, None

def analyze_system_metrics(system_metrics):
    """Analiza las métricas del sistema."""
    try:
        # Estadísticas básicas
        stats = {
            'cpu': {
                'mean': system_metrics['cpu_percent'].mean(),
                'std': system_metrics['cpu_percent'].std(),
                'max': system_metrics['cpu_percent'].max(),
                'min': system_metrics['cpu_percent'].min()
            },
            'memory': {
                'mean': system_metrics['memory_percent'].mean(),
                'std': system_metrics['memory_percent'].std(),
                'max': system_metrics['memory_percent'].max(),
                'min': system_metrics['memory_percent'].min()
            },
            'disk': {
                'mean': system_metrics['disk_percent'].mean(),
                'std': system_metrics['disk_percent'].std(),
                'max': system_metrics['disk_percent'].max(),
                'min': system_metrics['disk_percent'].min()
            },
            'swap': {
                'mean': system_metrics['swap_percent'].mean(),
                'std': system_metrics['swap_percent'].std(),
                'max': system_metrics['swap_percent'].max(),
                'min': system_metrics['swap_percent'].min()
            },
            'processes': {
                'mean': system_metrics['processes'].mean(),
                'std': system_metrics['processes'].std(),
                'max': system_metrics['processes'].max(),
                'min': system_metrics['processes'].min()
            }
        }
        
        # Detectar anomalías
        anomalies = {
            'cpu': system_metrics[
                system_metrics['cpu_percent'] > stats['cpu']['mean'] + 2 * stats['cpu']['std']
            ],
            'memory': system_metrics[
                system_metrics['memory_percent'] > stats['memory']['mean'] + 2 * stats['memory']['std']
            ],
            'disk': system_metrics[
                system_metrics['disk_percent'] > stats['disk']['mean'] + 2 * stats['disk']['std']
            ],
            'swap': system_metrics[
                system_metrics['swap_percent'] > stats['swap']['mean'] + 2 * stats['swap']['std']
            ]
        }
        
        # Generar recomendaciones
        recommendations = []
        
        if stats['cpu']['mean'] > 70:
            recommendations.append(
                "El uso promedio de CPU es alto. Considere optimizar los procesos o agregar más recursos."
            )
            
        if stats['memory']['mean'] > 80:
            recommendations.append(
                "El uso promedio de memoria es alto. Considere aumentar la memoria o optimizar el uso."
            )
            
        if stats['disk']['mean'] > 85:
            recommendations.append(
                "El uso promedio del disco es alto. Considere limpiar archivos temporales o aumentar el espacio."
            )
            
        if stats['swap']['mean'] > 50:
            recommendations.append(
                "El uso promedio de swap es alto. Considere aumentar la memoria RAM."
            )
            
        return {
            'stats': stats,
            'anomalies': {
                k: v.to_dict('records') for k, v in anomalies.items()
            },
            'recommendations': recommendations
        }
    except Exception as e:
        logger.error(f"Error al analizar métricas del sistema: {str(e)}")
        return None

def analyze_process_metrics(process_df):
    """Analiza las métricas de procesos."""
    try:
        # Agrupar por proceso
        process_stats = process_df.groupby('name').agg({
            'cpu_percent': ['mean', 'std', 'max'],
            'memory_percent': ['mean', 'std', 'max']
        }).reset_index()
        
        # Detectar procesos problemáticos
        problematic_processes = process_stats[
            (process_stats[('cpu_percent', 'mean')] > 50) |
            (process_stats[('memory_percent', 'mean')] > 50)
        ]
        
        # Generar recomendaciones
        recommendations = []
        
        for _, proc in problematic_processes.iterrows():
            if proc[('cpu_percent', 'mean')] > 50:
                recommendations.append(
                    f"El proceso {proc['name']} tiene un uso promedio de CPU alto ({proc[('cpu_percent', 'mean')]:.1f}%). "
                    "Considere optimizarlo o limitar su uso."
                )
                
            if proc[('memory_percent', 'mean')] > 50:
                recommendations.append(
                    f"El proceso {proc['name']} tiene un uso promedio de memoria alto ({proc[('memory_percent', 'mean')]:.1f}%). "
                    "Considere optimizarlo o limitar su uso."
                )
                
        return {
            'process_stats': process_stats.to_dict('records'),
            'problematic_processes': problematic_processes.to_dict('records'),
            'recommendations': recommendations
        }
    except Exception as e:
        logger.error(f"Error al analizar métricas de procesos: {str(e)}")
        return None

def save_analysis_results(results):
    """Guarda los resultados del análisis."""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = Path(f'analysis/performance_{timestamp}.json')
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Resultados guardados en {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error al guardar resultados: {str(e)}")
        return False

def run_performance_analysis(hours=24):
    """Ejecuta el análisis de rendimiento."""
    try:
        # Cargar métricas
        metrics = load_metrics(hours)
        if not metrics:
            logger.error("No se pudieron cargar las métricas")
            return False
            
        # Preparar datos
        system_metrics, process_df = prepare_data(metrics)
        if system_metrics is None or process_df is None:
            logger.error("No se pudieron preparar los datos")
            return False
            
        # Analizar métricas
        system_analysis = analyze_system_metrics(system_metrics)
        process_analysis = analyze_process_metrics(process_df)
        
        if system_analysis is None or process_analysis is None:
            logger.error("Error al analizar las métricas")
            return False
            
        # Combinar resultados
        results = {
            'timestamp': datetime.now().isoformat(),
            'hours_analyzed': hours,
            'system_analysis': system_analysis,
            'process_analysis': process_analysis
        }
        
        # Guardar resultados
        if save_analysis_results(results):
            logger.info("Análisis completado exitosamente")
            
            # Mostrar resumen
            print("\nResumen del Análisis de Rendimiento:")
            print("-" * 50)
            
            print("\nMétricas del Sistema:")
            for metric, stats in system_analysis['stats'].items():
                print(f"\n{metric.upper()}:")
                for stat, value in stats.items():
                    print(f"- {stat}: {value:.2f}")
                    
            print("\nProcesos Problemáticos:")
            for proc in process_analysis['problematic_processes']:
                print(f"\n{proc['name']}:")
                print(f"- CPU: {proc[('cpu_percent', 'mean')]:.1f}% (máx: {proc[('cpu_percent', 'max')]:.1f}%)")
                print(f"- Memoria: {proc[('memory_percent', 'mean')]:.1f}% (máx: {proc[('memory_percent', 'max')]:.1f}%)")
                
            print("\nRecomendaciones:")
            for rec in system_analysis['recommendations'] + process_analysis['recommendations']:
                print(f"- {rec}")
                
        else:
            logger.error("Error al guardar los resultados")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"Error al ejecutar el análisis: {str(e)}")
        return False

def main():
    """Función principal."""
    try:
        logger.info("Iniciando análisis de rendimiento...")
        
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            sys.exit(1)
            
        # Obtener horas de los argumentos
        hours = 24  # 24 horas por defecto
        if len(sys.argv) > 1:
            try:
                hours = int(sys.argv[1])
            except ValueError:
                print("Error: Las horas deben ser un número entero")
                sys.exit(1)
                
        if run_performance_analysis(hours):
            logger.info("Análisis de rendimiento completado exitosamente")
            sys.exit(0)
        else:
            logger.error("Error en el análisis de rendimiento")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Análisis detenido por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 