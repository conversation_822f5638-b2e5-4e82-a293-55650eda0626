"""
Hamilton AI Assistant - Face Recognition Module
Módulo de reconocimiento facial para autenticación del señor Ibero
"""

import cv2
import face_recognition
import numpy as np
import pickle
import os
import logging
from typing import List, Tuple, Optional, Dict
from pathlib import Path
from deepface import DeepFace
from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HamiltonFaceRecognition:
    """
    Sistema de reconocimiento facial de Hamilton para autenticar al señor Ibero
    """
    
    def __init__(self):
        self.known_face_encodings = []
        self.known_face_names = []
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.authorized_user = settings.AUTHORIZED_USER
        self.tolerance = settings.FACE_RECOGNITION_TOLERANCE
        self.encodings_file = "./storage/faces/face_encodings.pkl"
        self.camera = None
        self._load_known_faces()
    
    def _load_known_faces(self):
        """Carga las caras conocidas desde el archivo de encodings"""
        try:
            if os.path.exists(self.encodings_file):
                with open(self.encodings_file, 'rb') as f:
                    data = pickle.load(f)
                    self.known_face_encodings = data.get('encodings', [])
                    self.known_face_names = data.get('names', [])
                logger.info(f"Cargadas {len(self.known_face_encodings)} caras conocidas")
            else:
                logger.info("No se encontró archivo de encodings, iniciando con base vacía")
        except Exception as e:
            logger.error(f"Error cargando caras conocidas: {e}")
            self.known_face_encodings = []
            self.known_face_names = []
    
    def _save_known_faces(self):
        """Guarda las caras conocidas en el archivo de encodings"""
        try:
            os.makedirs(os.path.dirname(self.encodings_file), exist_ok=True)
            data = {
                'encodings': self.known_face_encodings,
                'names': self.known_face_names
            }
            with open(self.encodings_file, 'wb') as f:
                pickle.dump(data, f)
            logger.info("Caras conocidas guardadas exitosamente")
        except Exception as e:
            logger.error(f"Error guardando caras conocidas: {e}")
    
    def register_user_face(self, image_path: str, user_name: str = None) -> bool:
        """
        Registra la cara del usuario desde una imagen
        
        Args:
            image_path: Ruta a la imagen del usuario
            user_name: Nombre del usuario (por defecto señor_ibero)
            
        Returns:
            True si el registro fue exitoso
        """
        if user_name is None:
            user_name = self.authorized_user
            
        try:
            # Cargar imagen
            image = face_recognition.load_image_file(image_path)
            
            # Encontrar caras en la imagen
            face_locations = face_recognition.face_locations(image)
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            if not face_encodings:
                logger.error("No se encontraron caras en la imagen")
                return False
            
            if len(face_encodings) > 1:
                logger.warning("Se encontraron múltiples caras, usando la primera")
            
            # Agregar encoding a la base de conocimiento
            face_encoding = face_encodings[0]
            self.known_face_encodings.append(face_encoding)
            self.known_face_names.append(user_name)
            
            # Guardar cambios
            self._save_known_faces()
            
            logger.info(f"Cara registrada exitosamente para {user_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registrando cara: {e}")
            return False
    
    def register_user_from_camera(self, user_name: str = None, num_samples: int = 5) -> bool:
        """
        Registra la cara del usuario tomando fotos desde la cámara
        
        Args:
            user_name: Nombre del usuario
            num_samples: Número de muestras a tomar
            
        Returns:
            True si el registro fue exitoso
        """
        if user_name is None:
            user_name = self.authorized_user
            
        try:
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                logger.error("No se pudo abrir la cámara")
                return False
            
            samples_taken = 0
            logger.info(f"Iniciando registro facial para {user_name}")
            logger.info("Presiona ESPACIO para tomar una muestra, ESC para cancelar")
            
            while samples_taken < num_samples:
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # Mostrar frame
                cv2.putText(frame, f"Muestras: {samples_taken}/{num_samples}", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame, "ESPACIO: Capturar, ESC: Cancelar", 
                           (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                # Detectar caras
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
                
                for (x, y, w, h) in faces:
                    cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 0, 0), 2)
                
                cv2.imshow('Registro Facial - Hamilton', frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == 27:  # ESC
                    logger.info("Registro cancelado")
                    cap.release()
                    cv2.destroyAllWindows()
                    return False
                elif key == 32:  # ESPACIO
                    if len(faces) > 0:
                        # Convertir frame a RGB para face_recognition
                        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        face_encodings = face_recognition.face_encodings(rgb_frame)
                        
                        if face_encodings:
                            self.known_face_encodings.append(face_encodings[0])
                            self.known_face_names.append(user_name)
                            samples_taken += 1
                            logger.info(f"Muestra {samples_taken} capturada")
                        else:
                            logger.warning("No se pudo procesar la cara detectada")
                    else:
                        logger.warning("No se detectó ninguna cara")
            
            cap.release()
            cv2.destroyAllWindows()
            
            # Guardar encodings
            self._save_known_faces()
            logger.info(f"Registro facial completado para {user_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error en registro desde cámara: {e}")
            return False
    
    def authenticate_user(self, image_path: str = None) -> Tuple[bool, str, float]:
        """
        Autentica al usuario comparando con caras conocidas
        
        Args:
            image_path: Ruta a imagen (opcional, usa cámara si no se proporciona)
            
        Returns:
            Tupla (autenticado, nombre_usuario, confianza)
        """
        try:
            if image_path:
                # Autenticar desde imagen
                image = face_recognition.load_image_file(image_path)
            else:
                # Autenticar desde cámara
                cap = cv2.VideoCapture(0)
                if not cap.isOpened():
                    return False, "Error: No se pudo abrir la cámara", 0.0
                
                ret, frame = cap.read()
                cap.release()
                
                if not ret:
                    return False, "Error: No se pudo capturar imagen", 0.0
                
                image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Encontrar caras en la imagen
            face_locations = face_recognition.face_locations(image)
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            if not face_encodings:
                return False, "No se detectó ninguna cara", 0.0
            
            # Comparar con caras conocidas
            for face_encoding in face_encodings:
                matches = face_recognition.compare_faces(
                    self.known_face_encodings, 
                    face_encoding, 
                    tolerance=self.tolerance
                )
                
                face_distances = face_recognition.face_distance(
                    self.known_face_encodings, 
                    face_encoding
                )
                
                if len(face_distances) > 0:
                    best_match_index = np.argmin(face_distances)
                    
                    if matches[best_match_index]:
                        name = self.known_face_names[best_match_index]
                        confidence = 1 - face_distances[best_match_index]
                        
                        # Verificar si es el usuario autorizado
                        if name == self.authorized_user:
                            logger.info(f"Usuario autenticado: {name} (confianza: {confidence:.2f})")
                            return True, name, confidence
                        else:
                            logger.warning(f"Usuario reconocido pero no autorizado: {name}")
                            return False, f"Usuario no autorizado: {name}", confidence
            
            logger.warning("No se reconoció al usuario")
            return False, "Usuario no reconocido", 0.0
            
        except Exception as e:
            logger.error(f"Error en autenticación: {e}")
            return False, f"Error: {e}", 0.0
    
    def start_live_authentication(self, callback_function):
        """
        Inicia autenticación en vivo desde la cámara
        
        Args:
            callback_function: Función a llamar cuando se autentica (success, name, confidence)
        """
        try:
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                logger.error("No se pudo abrir la cámara")
                return
            
            logger.info("Iniciando autenticación en vivo. Presiona 'q' para salir")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # Convertir a RGB
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Encontrar caras
                face_locations = face_recognition.face_locations(rgb_frame)
                face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)
                
                # Procesar cada cara encontrada
                for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
                    matches = face_recognition.compare_faces(self.known_face_encodings, face_encoding)
                    name = "Desconocido"
                    confidence = 0.0
                    
                    face_distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)
                    
                    if len(face_distances) > 0:
                        best_match_index = np.argmin(face_distances)
                        if matches[best_match_index]:
                            name = self.known_face_names[best_match_index]
                            confidence = 1 - face_distances[best_match_index]
                            
                            # Llamar callback si es usuario autorizado
                            if name == self.authorized_user and callback_function:
                                callback_function(True, name, confidence)
                    
                    # Dibujar rectángulo y nombre
                    color = (0, 255, 0) if name == self.authorized_user else (0, 0, 255)
                    cv2.rectangle(frame, (left, top), (right, bottom), color, 2)
                    cv2.putText(frame, f"{name} ({confidence:.2f})", 
                               (left, top - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 2)
                
                cv2.imshow('Hamilton - Autenticación Facial', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()
            
        except Exception as e:
            logger.error(f"Error en autenticación en vivo: {e}")

# Instancia global del reconocimiento facial
hamilton_face = HamiltonFaceRecognition()
