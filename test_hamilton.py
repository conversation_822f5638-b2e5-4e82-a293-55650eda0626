#!/usr/bin/env python3
"""
Hamilton AI Assistant - Test Script
Script de pruebas para verificar funcionalidad básica
"""

import sys
import logging
from pathlib import Path

# Agregar el directorio actual al path
sys.path.append(str(Path(__file__).parent))

from recognition.voice_recognition import hamilton_voice
from recognition.face_recognition import hamilton_face
from core.hamilton_core import hamilton
from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_voice_engine():
    """Prueba el motor de voz"""
    print("\n🔊 PRUEBA DEL MOTOR DE VOZ")
    print("-" * 40)
    
    # Obtener información de voz
    voice_info = hamilton_voice.get_voice_info()
    print(f"Información de voz: {voice_info}")
    
    # Prueba de síntesis de voz
    print("Probando síntesis de voz masculina...")
    hamilton_voice.speak("<PERSON><PERSON>, so<PERSON> Hamilton. Esta es una prueba de mi voz masculina.")
    
    # Prueba de reconocimiento de voz
    print("\nPrueba de reconocimiento de voz (5 segundos)...")
    print("Diga algo ahora:")
    
    text = hamilton_voice.listen_once(timeout=5)
    if text:
        print(f"Texto reconocido: '{text}'")
        
        # Probar detección de palabra de activación
        if hamilton_voice.is_wake_word(text):
            print("✅ Palabra de activación detectada!")
        else:
            print("ℹ️ No es una palabra de activación")
    else:
        print("❌ No se reconoció ningún texto")
    
    return True

def test_face_recognition():
    """Prueba el reconocimiento facial"""
    print("\n👤 PRUEBA DE RECONOCIMIENTO FACIAL")
    print("-" * 40)
    
    num_faces = len(hamilton_face.known_face_encodings)
    print(f"Caras registradas: {num_faces}")
    
    if num_faces == 0:
        print("⚠️ No hay caras registradas. Ejecute 'python register_face.py' primero.")
        return False
    
    print("Probando autenticación facial...")
    print("Mire a la cámara cuando esté listo.")
    input("Presione ENTER para continuar...")
    
    success, user_name, confidence = hamilton_face.authenticate_user()
    
    print(f"Resultado:")
    print(f"  Éxito: {'✅' if success else '❌'}")
    print(f"  Usuario: {user_name}")
    print(f"  Confianza: {confidence:.2f}")
    
    return success

def test_hamilton_core():
    """Prueba el núcleo de Hamilton"""
    print("\n🤖 PRUEBA DEL NÚCLEO DE HAMILTON")
    print("-" * 40)
    
    # Estado inicial
    status = hamilton.get_status()
    print(f"Estado inicial: {status}")
    
    # Prueba de síntesis de voz
    print("Probando síntesis de voz de Hamilton...")
    hamilton.speak("Hola señor Ibero, soy Hamilton. Esta es una prueba de mi sistema.")
    
    # Prueba de procesamiento de comandos
    print("\nProbando procesamiento de comandos...")
    test_commands = [
        "hola hamilton",
        "¿qué hora es?",
        "¿cómo estás?",
        "hamilton ayuda"
    ]
    
    for command in test_commands:
        print(f"\nComando: '{command}'")
        response = hamilton._process_command(command)
        print(f"Respuesta: '{response}'")
    
    return True

def test_configuration():
    """Prueba la configuración"""
    print("\n⚙️ PRUEBA DE CONFIGURACIÓN")
    print("-" * 40)
    
    print(f"Proyecto: {settings.PROJECT_NAME}")
    print(f"Versión: {settings.VERSION}")
    print(f"Usuario autorizado: {settings.AUTHORIZED_USER}")
    print(f"Motor TTS: {settings.TTS_ENGINE}")
    print(f"Género de voz: {settings.TTS_VOICE_GENDER}")
    print(f"Idioma: {settings.SPEECH_RECOGNITION_LANGUAGE}")
    print(f"Debug: {settings.DEBUG}")
    
    return True

def main():
    """Función principal de pruebas"""
    print("="*60)
    print("🧪 HAMILTON AI ASSISTANT - PRUEBAS DEL SISTEMA")
    print("="*60)
    
    tests = [
        ("Configuración", test_configuration),
        ("Motor de Voz", test_voice_engine),
        ("Reconocimiento Facial", test_face_recognition),
        ("Núcleo de Hamilton", test_hamilton_core)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name.upper()} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            print(f"✅ {test_name}: {'EXITOSO' if result else 'FALLIDO'}")
        except Exception as e:
            logger.error(f"Error en {test_name}: {e}")
            results[test_name] = False
            print(f"❌ {test_name}: ERROR - {e}")
    
    # Resumen de resultados
    print("\n" + "="*60)
    print("📊 RESUMEN DE PRUEBAS")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ EXITOSO" if result else "❌ FALLIDO"
        print(f"{test_name:.<30} {status}")
    
    print(f"\nTotal: {passed_tests}/{total_tests} pruebas exitosas")
    
    if passed_tests == total_tests:
        print("\n🎉 ¡Todas las pruebas pasaron! Hamilton está listo para usar.")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} prueba(s) fallaron. Revise la configuración.")
    
    print("\n💡 Para usar Hamilton:")
    print("1. Registre su cara: python register_face.py")
    print("2. Ejecute Hamilton: python main.py")
    print("3. Diga 'Hamilton' para activarlo")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nPruebas canceladas por el usuario")
    except Exception as e:
        logger.error(f"Error en pruebas: {e}")
        print(f"❌ Error inesperado: {e}")
    finally:
        print("\nCerrando pruebas...")
