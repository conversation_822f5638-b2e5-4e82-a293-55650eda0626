import os
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
import dash
from dash import dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import numpy as np

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Inicializar la aplicación Dash
app = dash.Dash(__name__)
app.title = "Hamilton Dashboard"

# Cargar datos
def load_metrics(hours=24):
    """Carga las métricas de las últimas N horas"""
    try:
        metrics = []
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with open('logs/metrics.json', 'r') as f:
            for line in f:
                data = json.loads(line)
                timestamp = datetime.fromisoformat(data['timestamp'])
                
                if timestamp >= cutoff_time:
                    metrics.append(data)
                    
        return metrics
    except Exception as e:
        logger.error(f"Error cargando métricas: {e}")
        return []

# Layout del dashboard
app.layout = html.Div([
    # Encabezado
    html.H1("Hamilton Dashboard", className="header"),
    
    # Selector de tiempo
    html.Div([
        html.Label("Período de tiempo:"),
        dcc.Dropdown(
            id='time-range',
            options=[
                {'label': 'Última hora', 'value': 1},
                {'label': 'Últimas 6 horas', 'value': 6},
                {'label': 'Últimas 24 horas', 'value': 24},
                {'label': 'Última semana', 'value': 168}
            ],
            value=24
        )
    ], className="time-selector"),
    
    # Métricas del sistema
    html.Div([
        html.H2("Métricas del Sistema"),
        dcc.Graph(id='system-metrics')
    ], className="metric-section"),
    
    # Métricas de IA
    html.Div([
        html.H2("Métricas de IA"),
        dcc.Graph(id='ai-metrics')
    ], className="metric-section"),
    
    # Alertas
    html.Div([
        html.H2("Alertas Activas"),
        html.Div(id='active-alerts')
    ], className="alert-section"),
    
    # Actualización automática
    dcc.Interval(
        id='interval-component',
        interval=30*1000,  # 30 segundos
        n_intervals=0
    )
])

# Callbacks
@app.callback(
    [Output('system-metrics', 'figure'),
     Output('ai-metrics', 'figure'),
     Output('active-alerts', 'children')],
    [Input('interval-component', 'n_intervals'),
     Input('time-range', 'value')]
)
def update_metrics(n, hours):
    """Actualiza las métricas del dashboard"""
    metrics = load_metrics(hours)
    
    if not metrics:
        return {}, {}, "No hay datos disponibles"
        
    # Preparar datos del sistema
    system_df = pd.DataFrame([
        {
            'timestamp': datetime.fromisoformat(m['timestamp']),
            'cpu': m['system']['cpu_percent'],
            'memory': m['system']['memory_percent'],
            'disk': m['system']['disk_percent']
        }
        for m in metrics
    ])
    
    # Gráfico de métricas del sistema
    system_fig = go.Figure()
    system_fig.add_trace(go.Scatter(
        x=system_df['timestamp'],
        y=system_df['cpu'],
        name='CPU',
        line=dict(color='red')
    ))
    system_fig.add_trace(go.Scatter(
        x=system_df['timestamp'],
        y=system_df['memory'],
        name='Memoria',
        line=dict(color='blue')
    ))
    system_fig.add_trace(go.Scatter(
        x=system_df['timestamp'],
        y=system_df['disk'],
        name='Disco',
        line=dict(color='green')
    ))
    
    system_fig.update_layout(
        title='Uso de Recursos del Sistema',
        xaxis_title='Tiempo',
        yaxis_title='Porcentaje',
        hovermode='x unified'
    )
    
    # Preparar datos de IA
    ai_metrics = []
    for m in metrics:
        for engine, data in m['ai'].items():
            if 'quality_metrics' in data:
                ai_metrics.append({
                    'timestamp': datetime.fromisoformat(m['timestamp']),
                    'engine': engine,
                    **data['quality_metrics']
                })
                
    if ai_metrics:
        ai_df = pd.DataFrame(ai_metrics)
        
        # Gráfico de métricas de IA
        ai_fig = px.line(
            ai_df,
            x='timestamp',
            y=['relevance', 'coherence', 'completeness', 'clarity', 'context_usage'],
            color='engine',
            title='Métricas de Calidad de IA'
        )
        
        ai_fig.update_layout(
            xaxis_title='Tiempo',
            yaxis_title='Puntuación',
            hovermode='x unified'
        )
    else:
        ai_fig = go.Figure()
        
    # Obtener alertas activas
    alerts = []
    for m in metrics[-10:]:  # Últimas 10 métricas
        if 'alerts' in m:
            alerts.extend(m['alerts'])
            
    # Formatear alertas
    alert_elements = []
    for alert in alerts:
        alert_elements.append(
            html.Div([
                html.H4(alert['type']),
                html.P(alert['message']),
                html.Small(f"Severidad: {alert['severity']}")
            ], className=f"alert alert-{alert['severity']}")
        )
        
    return system_fig, ai_fig, alert_elements

# Estilos CSS
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>Hamilton Dashboard</title>
        {%favicon%}
        {%css%}
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .header {
                color: #2c3e50;
                text-align: center;
                margin-bottom: 30px;
            }
            .time-selector {
                background-color: white;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .metric-section {
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .alert-section {
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .alert {
                padding: 10px;
                margin: 10px 0;
                border-radius: 3px;
            }
            .alert-critical {
                background-color: #ffebee;
                border-left: 4px solid #f44336;
            }
            .alert-warning {
                background-color: #fff3e0;
                border-left: 4px solid #ff9800;
            }
            .alert-info {
                background-color: #e3f2fd;
                border-left: 4px solid #2196f3;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050) 