"""
Hamilton AI Assistant - Local LLM Integration
Integración con modelo de lenguaje local como respaldo
"""

import logging
from typing import Dict, Any, Optional, List
import json
from datetime import datetime
import random
import re
from pathlib import Path
import time

from config.settings import settings
from security.encryption import security_log

# Configurar logging
logger = logging.getLogger(__name__)

class HamiltonLocalLLM:
    """Motor de lenguaje local para Hamilton"""
    
    def __init__(self):
        """Inicializa el modelo local de Hamilton"""
        self.model = None
        self.tokenizer = None
        self.conversation_history = []
        self.learning_stats = {
            "accuracy": 0.0,
            "response_time": 0.0,
            "user_satisfaction": 0.0,
            "context_usage": 0.0,
            "response_types": {
                "informative": 0,
                "creative": 0,
                "analytical": 0,
                "emotional": 0,
                "technical": 0
            }
        }
        self.response_templates = {
            "informative": [
                "Basado en la información disponible, {response}",
                "Seg<PERSON> los datos, {response}",
                "La evidencia sugiere que {response}"
            ],
            "creative": [
                "Imaginemos que {response}",
                "¿Qué tal si {response}?",
                "Podríamos explorar la idea de {response}"
            ],
            "analytical": [
                "Analizando los factores involucrados, {response}",
                "Considerando las variables, {response}",
                "Desde un punto de vista analítico, {response}"
            ],
            "emotional": [
                "Entiendo cómo te sientes. {response}",
                "Es comprensible que {response}",
                "Me alegra que {response}"
            ],
            "technical": [
                "Técnicamente hablando, {response}",
                "Desde una perspectiva técnica, {response}",
                "El análisis técnico indica que {response}"
            ]
        }
        self.initialize_model()
        self.logger = logging.getLogger(__name__)
    
    def _load_learning_data(self) -> Dict[str, Any]:
        """Carga datos de aprendizaje previos"""
        try:
            data_file = Path('storage/learning_data.json')
            if data_file.exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"patterns": {}, "responses": {}, "user_preferences": {}}
        except Exception as e:
            logger.error(f"Error cargando datos de aprendizaje: {e}")
            return {"patterns": {}, "responses": {}, "user_preferences": {}}
    
    def _save_learning_data(self):
        """Guarda datos de aprendizaje"""
        try:
            data_file = Path('storage/learning_data.json')
            data_file.parent.mkdir(exist_ok=True)
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.learning_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error guardando datos de aprendizaje: {e}")
    
    def process_query(self, query: str, context: Dict[str, Any] = None) -> str:
        """Procesa una consulta usando el modelo local"""
        try:
            start_time = time.time()
            
            # Determinar el tipo de respuesta basado en el contexto y la consulta
            response_type = self._determine_response_type(query, context)
            
            # Generar respuesta base
            inputs = self.tokenizer(query, return_tensors="pt", padding=True, truncation=True)
            outputs = self.model.generate(
                **inputs,
                max_length=100,
                num_return_sequences=1,
                temperature=0.7,
                top_p=0.9,
                do_sample=True
            )
            base_response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Aplicar template según el tipo de respuesta
            template = random.choice(self.response_templates[response_type])
            response = template.format(response=base_response)
            
            # Actualizar estadísticas
            self._update_stats(response_type, time.time() - start_time)
            
            # Actualizar historial
            self._update_history(query, response, response_type)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error procesando consulta: {e}")
            return f"Lo siento, hubo un error procesando tu consulta: {str(e)}"
    
    def _determine_response_type(self, query: str, context: Dict[str, Any] = None) -> str:
        """Determina el tipo de respuesta más apropiado"""
        # Palabras clave para cada tipo de respuesta
        keywords = {
            "informative": ["qué", "cómo", "cuándo", "dónde", "quién", "por qué"],
            "creative": ["imagina", "crea", "diseña", "inventa", "explora"],
            "analytical": ["analiza", "compara", "evalúa", "considera", "examina"],
            "emotional": ["siento", "emocionado", "triste", "feliz", "preocupado"],
            "technical": ["técnico", "especificación", "implementación", "código", "sistema"]
        }
        
        # Contar coincidencias de palabras clave
        scores = {rtype: 0 for rtype in keywords}
        query_lower = query.lower()
        
        for rtype, words in keywords.items():
            scores[rtype] = sum(1 for word in words if word in query_lower)
        
        # Considerar el contexto si está disponible
        if context and "user_mood" in context:
            if context["user_mood"] in ["happy", "excited"]:
                scores["emotional"] += 2
            elif context["user_mood"] in ["analytical", "focused"]:
                scores["analytical"] += 2
        
        # Seleccionar el tipo con mayor puntuación
        return max(scores.items(), key=lambda x: x[1])[0]
    
    def _update_stats(self, response_type: str, response_time: float):
        """Actualiza las estadísticas de aprendizaje"""
        self.learning_stats["response_time"] = (
            self.learning_stats["response_time"] * 0.9 + response_time * 0.1
        )
        self.learning_stats["response_types"][response_type] += 1
        
        # Calcular precisión basada en la distribución de tipos de respuesta
        total_responses = sum(self.learning_stats["response_types"].values())
        if total_responses > 0:
            self.learning_stats["accuracy"] = (
                self.learning_stats["response_types"][response_type] / total_responses * 100
            )
    
    def _update_history(self, query: str, response: str, response_type: str):
        """Actualiza el historial de conversación"""
        self.conversation_history.append({
            "query": query,
            "response": response,
            "type": response_type,
            "timestamp": time.time()
        })
        
        # Mantener solo las últimas 10 interacciones
        if len(self.conversation_history) > 10:
            self.conversation_history.pop(0)
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """Obtiene las estadísticas de aprendizaje actuales"""
        return self.learning_stats
    
    def clear_history(self):
        """Limpia el historial de conversación"""
        self.conversation_history = []

# Instancia global
hamilton_local = HamiltonLocalLLM() 