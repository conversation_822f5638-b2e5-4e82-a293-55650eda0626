import os
import sys
import logging
import subprocess
from pathlib import Path
import pytest
from dotenv import load_dotenv

def setup_logging():
    """Configura el sistema de logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/verification.log')
        ]
    )
    return logging.getLogger(__name__)

def run_tests():
    """Ejecuta las pruebas unitarias"""
    logger = setup_logging()
    logger.info("Ejecutando pruebas unitarias...")
    
    try:
        result = pytest.main([
            'tests/',
            '-v',
            '--disable-warnings',
            '--cov=ai',
            '--cov-report=term-missing'
        ])
        
        if result == 0:
            logger.info("Todas las pruebas pasaron exitosamente")
        else:
            logger.error(f"Las pruebas fallaron con código {result}")
            return False
            
        return True
    except Exception as e:
        logger.error(f"Error ejecutando pruebas: {e}")
        return False

def test_system_components():
    """Prueba los componentes principales del sistema"""
    logger = setup_logging()
    logger.info("Probando componentes del sistema...")
    
    test_queries = [
        "¿Qué es la inteligencia artificial?",
        "¿Cómo funciona Python?",
        "¿Cuál es la capital de Francia?",
        "¿Qué tiempo hace hoy?",
        "¿Puedes ayudarme con mi código?"
    ]
    
    try:
        from main import hamilton
        
        for query in test_queries:
            logger.info(f"Probando consulta: {query}")
            response = hamilton.process_input(query)
            
            if not response or "error" in response.lower():
                logger.error(f"Error procesando consulta: {query}")
                return False
                
            logger.info(f"Respuesta recibida: {response[:100]}...")
            
        return True
    except Exception as e:
        logger.error(f"Error probando componentes: {e}")
        return False

def verify_metrics():
    """Verifica que las métricas se actualicen correctamente"""
    logger = setup_logging()
    logger.info("Verificando métricas...")
    
    try:
        from main import hamilton
        
        # Obtener estado inicial
        initial_status = hamilton.get_ai_status()
        
        # Realizar algunas consultas
        for _ in range(3):
            hamilton.process_input("test query")
        
        # Obtener estado final
        final_status = hamilton.get_ai_status()
        
        # Verificar que las métricas han cambiado
        if initial_status == final_status:
            logger.warning("Las métricas no han cambiado después de las consultas")
            return False
            
        logger.info("Métricas actualizadas correctamente")
        return True
    except Exception as e:
        logger.error(f"Error verificando métricas: {e}")
        return False

def check_logs():
    """Verifica los logs del sistema"""
    logger = setup_logging()
    logger.info("Verificando logs...")
    
    log_files = [
        'logs/setup.log',
        'logs/verification.log',
        'logs/security.log',
        'logs/ai.log'
    ]
    
    for log_file in log_files:
        path = Path(log_file)
        if not path.exists():
            logger.warning(f"Archivo de log no encontrado: {log_file}")
            continue
            
        try:
            with open(log_file, 'r') as f:
                content = f.read()
                
            if "error" in content.lower():
                logger.warning(f"Errores encontrados en {log_file}")
                return False
                
            logger.info(f"Log {log_file} verificado correctamente")
        except Exception as e:
            logger.error(f"Error verificando log {log_file}: {e}")
            return False
            
    return True

def main():
    """Función principal de verificación"""
    logger = setup_logging()
    logger.info("Iniciando verificación del sistema...")
    
    # Cargar variables de entorno
    load_dotenv()
    
    # Ejecutar verificaciones
    checks = [
        ("Pruebas unitarias", run_tests),
        ("Componentes del sistema", test_system_components),
        ("Métricas", verify_metrics),
        ("Logs", check_logs)
    ]
    
    all_passed = True
    for name, check in checks:
        logger.info(f"\nEjecutando verificación: {name}")
        if not check():
            logger.error(f"Verificación falló: {name}")
            all_passed = False
    
    if all_passed:
        logger.info("\nTodas las verificaciones completadas exitosamente")
        sys.exit(0)
    else:
        logger.error("\nAlgunas verificaciones fallaron")
        sys.exit(1)

if __name__ == "__main__":
    main() 