"""
Hamilton AI Assistant - Security & Encryption Module
Módulo de seguridad y cifrado para proteger datos biométricos y conversaciones
"""

import os
import json
import pickle
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import hashlib
import pyotp
import qrcode
from ratelimit import limits, sleep_and_retry
import redis
import uuid

from config.settings import settings

# Configurar logging de seguridad
security_logger = logging.getLogger('hamilton.security')
security_handler = logging.FileHandler('./logs/security.log')
security_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

# Configuración de rate limiting
ONE_MINUTE = 60
MAX_REQUESTS_PER_MINUTE = 5

class RateLimiter:
    """Implementa rate limiting usando Redis"""
    
    def __init__(self):
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=0
        )
    
    @sleep_and_retry
    @limits(calls=MAX_REQUESTS_PER_MINUTE, period=ONE_MINUTE)
    def check_rate_limit(self, user_id: str) -> bool:
        """Verifica si el usuario ha excedido el límite de intentos"""
        key = f"rate_limit:{user_id}"
        current = self.redis_client.get(key)
        
        if current is None:
            self.redis_client.setex(key, ONE_MINUTE, 1)
            return True
        
        if int(current) >= MAX_REQUESTS_PER_MINUTE:
            security_logger.warning(f"Rate limit excedido para usuario: {user_id}")
            return False
        
        self.redis_client.incr(key)
        return True

class TwoFactorAuth:
    """Implementa autenticación de dos factores"""
    
    def __init__(self):
        self.totp = pyotp.TOTP(settings.TOTP_SECRET)
    
    def generate_secret(self) -> str:
        """Genera un nuevo secreto TOTP"""
        return pyotp.random_base32()
    
    def generate_qr_code(self, username: str, secret: str) -> str:
        """Genera código QR para configuración 2FA"""
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            username,
            issuer_name="Hamilton AI"
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)
        
        qr_path = f"./storage/2fa/{username}_qr.png"
        os.makedirs(os.path.dirname(qr_path), exist_ok=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img.save(qr_path)
        
        return qr_path
    
    def verify_code(self, code: str) -> bool:
        """Verifica código TOTP"""
        return self.totp.verify(code)

class KeyRotation:
    """Maneja la rotación de claves de cifrado"""
    
    def __init__(self, encryption: 'HamiltonEncryption'):
        self.encryption = encryption
        self.rotation_period = timedelta(days=30)
        self.last_rotation = self._get_last_rotation()
    
    def _get_last_rotation(self) -> datetime:
        """Obtiene la fecha de última rotación"""
        try:
            with open("./security/last_rotation.txt", "r") as f:
                return datetime.fromisoformat(f.read().strip())
        except:
            return datetime.min
    
    def _update_rotation_date(self):
        """Actualiza la fecha de última rotación"""
        with open("./security/last_rotation.txt", "w") as f:
            f.write(datetime.now().isoformat())
    
    def check_rotation_needed(self) -> bool:
        """Verifica si se necesita rotación de claves"""
        return datetime.now() - self.last_rotation > self.rotation_period
    
    def rotate_keys(self):
        """Realiza la rotación de claves"""
        if not self.check_rotation_needed():
            return
        
        try:
            # Generar nueva clave
            new_key = Fernet.generate_key()
            
            # Cifrar datos existentes con nueva clave
            old_fernet = self.encryption._fernet
            self.encryption._fernet = Fernet(new_key)
            
            # Guardar nueva clave
            with open(self.encryption.key_file, 'wb') as f:
                f.write(new_key)
            
            # Actualizar fecha de rotación
            self._update_rotation_date()
            self.last_rotation = datetime.now()
            
            security_logger.info("Rotación de claves completada exitosamente")
            
        except Exception as e:
            security_logger.error(f"Error en rotación de claves: {e}")
            raise

class HamiltonEncryption:
    """
    Sistema de cifrado para Hamilton
    Protege datos biométricos, conversaciones y configuraciones sensibles
    """
    
    def __init__(self):
        self.key_file = Path("./security/master.key")
        self.salt_file = Path("./security/salt.bin")
        self._fernet = None
        self._ensure_security_directory()
        self._initialize_encryption()
        self.rate_limiter = RateLimiter()
        self.two_factor = TwoFactorAuth()
        self.key_rotation = KeyRotation(self)
    
    def _ensure_security_directory(self):
        """Crea el directorio de seguridad si no existe"""
        Path("./security").mkdir(exist_ok=True)
        Path("./logs").mkdir(exist_ok=True)
    
    def _initialize_encryption(self):
        """Inicializa el sistema de cifrado"""
        try:
            if not self.key_file.exists() or not self.salt_file.exists():
                self._generate_master_key()
            else:
                self._load_master_key()
            
            security_logger.info("Sistema de cifrado inicializado")
        except Exception as e:
            security_logger.error(f"Error inicializando cifrado: {e}")
            raise
    
    def _generate_master_key(self):
        """Genera una nueva clave maestra"""
        try:
            # Generar salt aleatorio
            salt = os.urandom(16)
            
            # Usar configuración del sistema como base para la clave
            password = f"{settings.SECRET_KEY}_{settings.AUTHORIZED_USER}".encode()
            
            # Derivar clave usando PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            
            # Guardar salt y clave
            with open(self.salt_file, 'wb') as f:
                f.write(salt)
            
            with open(self.key_file, 'wb') as f:
                f.write(key)
            
            # Inicializar Fernet
            self._fernet = Fernet(key)
            
            security_logger.info("Nueva clave maestra generada")
            
        except Exception as e:
            security_logger.error(f"Error generando clave maestra: {e}")
            raise
    
    def _load_master_key(self):
        """Carga la clave maestra existente"""
        try:
            with open(self.salt_file, 'rb') as f:
                salt = f.read()
            
            password = f"{settings.SECRET_KEY}_{settings.AUTHORIZED_USER}".encode()
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            
            # Verificar que la clave coincide
            with open(self.key_file, 'rb') as f:
                stored_key = f.read()
            
            if key != stored_key:
                security_logger.warning("Clave maestra no coincide - regenerando")
                self._generate_master_key()
                return
            
            self._fernet = Fernet(key)
            security_logger.info("Clave maestra cargada exitosamente")
            
        except Exception as e:
            security_logger.error(f"Error cargando clave maestra: {e}")
            self._generate_master_key()
    
    def encrypt_data(self, data: Any) -> bytes:
        """
        Cifra datos usando Fernet
        
        Args:
            data: Datos a cifrar (serializables)
            
        Returns:
            Datos cifrados en bytes
        """
        try:
            if isinstance(data, (dict, list)):
                serialized = json.dumps(data).encode()
            elif isinstance(data, str):
                serialized = data.encode()
            else:
                serialized = pickle.dumps(data)
            
            encrypted = self._fernet.encrypt(serialized)
            security_logger.debug("Datos cifrados exitosamente")
            return encrypted
            
        except Exception as e:
            security_logger.error(f"Error cifrando datos: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: bytes, data_type: str = 'auto') -> Any:
        """
        Descifra datos
        
        Args:
            encrypted_data: Datos cifrados
            data_type: Tipo de datos ('json', 'pickle', 'string', 'auto')
            
        Returns:
            Datos descifrados
        """
        try:
            decrypted = self._fernet.decrypt(encrypted_data)
            
            if data_type == 'json':
                return json.loads(decrypted.decode())
            elif data_type == 'string':
                return decrypted.decode()
            elif data_type == 'pickle':
                return pickle.loads(decrypted)
            else:  # auto
                try:
                    return json.loads(decrypted.decode())
                except:
                    try:
                        return pickle.loads(decrypted)
                    except:
                        return decrypted.decode()
            
        except Exception as e:
            security_logger.error(f"Error descifrando datos: {e}")
            raise
    
    def encrypt_file(self, file_path: str, output_path: str = None):
        """Cifra un archivo completo"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"Archivo no encontrado: {file_path}")
            
            if output_path is None:
                output_path = f"{file_path}.encrypted"
            
            with open(file_path, 'rb') as f:
                data = f.read()
            
            encrypted_data = self._fernet.encrypt(data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            security_logger.info(f"Archivo cifrado: {file_path} -> {output_path}")
            
        except Exception as e:
            security_logger.error(f"Error cifrando archivo {file_path}: {e}")
            raise
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str = None):
        """Descifra un archivo"""
        try:
            encrypted_file_path = Path(encrypted_file_path)
            if not encrypted_file_path.exists():
                raise FileNotFoundError(f"Archivo cifrado no encontrado: {encrypted_file_path}")
            
            if output_path is None:
                output_path = str(encrypted_file_path).replace('.encrypted', '')
            
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self._fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            security_logger.info(f"Archivo descifrado: {encrypted_file_path} -> {output_path}")
            
        except Exception as e:
            security_logger.error(f"Error descifrando archivo {encrypted_file_path}: {e}")
            raise

    def authenticate_user(self, user_id: str, code: str = None) -> bool:
        """
        Autentica usuario con rate limiting y 2FA opcional
        """
        if not self.rate_limiter.check_rate_limit(user_id):
            return False
        
        if settings.ENABLE_2FA and code:
            if not self.two_factor.verify_code(code):
                security_logger.warning(f"2FA fallido para usuario: {user_id}")
                return False
        
        return True

class SecurityLogger:
    """
    Logger especializado para eventos de seguridad
    """
    security_logger = security_logger
    log_path = "logs/security.log"

    @staticmethod
    def log_authentication_attempt(user: str, success: bool, method: str, confidence: float) -> None:
        """Registra un intento de autenticación"""
        log_message = {
            "timestamp": datetime.now().isoformat(),
            "event": "authentication_attempt",
            "user": user,
            "success": success,
            "method": method,
            "confidence": confidence,
            "ip": "local",
            "session_id": str(uuid.uuid4())
        }
        
        # Asegurar que el directorio logs existe
        os.makedirs("logs", exist_ok=True)
        
        # Usar log_path si está presente
        log_path = getattr(SecurityLogger, 'log_path', "logs/security.log")
        with open(log_path, "a") as f:
            f.write(json.dumps(log_message) + "\n")
        
        # Registrar también en el logger
        if success:
            SecurityLogger.security_logger.info(f"Autenticación exitosa: {user} ({method})")
        else:
            SecurityLogger.security_logger.warning(f"Intento de autenticación fallido: {user} ({method})")

    @staticmethod
    def log_activation_attempt(user: str, success: bool, trigger: str = "voice"):
        """Registra intento de activación"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": "activation_attempt",
            "user": user,
            "success": success,
            "trigger": trigger
        }
        
        if success:
            security_logger.info(f"Activación exitosa: {json.dumps(event)}")
        else:
            security_logger.warning(f"Fallo de activación: {json.dumps(event)}")
    
    @staticmethod
    def log_unauthorized_access(user: str, attempted_action: str):
        """Registra intento de acceso no autorizado"""
        log_message = {
            "timestamp": datetime.now().isoformat(),
            "event": "unauthorized_access",
            "user": user,
            "attempted_action": attempted_action,
            "severity": "high",
            "ip": "local",
            "session_id": str(uuid.uuid4())
        }
        os.makedirs("logs", exist_ok=True)
        log_path = getattr(SecurityLogger, 'log_path', "logs/security.log")
        with open(log_path, "a") as f:
            f.write(json.dumps(log_message) + "\n")
        SecurityLogger.security_logger.error(f"Acceso no autorizado: {json.dumps(log_message)}")
    
    @staticmethod
    def log_data_access(user: str, data_type: str, action: str):
        """Registra acceso a datos"""
        log_message = {
            "timestamp": datetime.now().isoformat(),
            "event": "data_access",
            "user": user,
            "data_type": data_type,
            "action": action,
            "ip": "local",
            "session_id": str(uuid.uuid4())
        }
        os.makedirs("logs", exist_ok=True)
        log_path = getattr(SecurityLogger, 'log_path', "logs/security.log")
        with open(log_path, "a") as f:
            f.write(json.dumps(log_message) + "\n")
        SecurityLogger.security_logger.info(f"Acceso a datos: {json.dumps(log_message)}")

class DataCleaner:
    """
    Limpieza automática de datos antiguos
    """
    
    @staticmethod
    def clean_old_conversations(days_to_keep: int = 365):
        """Limpia conversaciones antiguas"""
        try:
            conversations_dir = Path("./storage/conversations")
            if not conversations_dir.exists():
                return
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cleaned_count = 0
            
            for file_path in conversations_dir.glob("session_*.json"):
                try:
                    # Extraer timestamp del nombre del archivo
                    timestamp = int(file_path.stem.split('_')[1])
                    file_date = datetime.fromtimestamp(timestamp)
                    
                    if file_date < cutoff_date:
                        file_path.unlink()
                        cleaned_count += 1
                        
                except (ValueError, IndexError):
                    continue
            
            security_logger.info(f"Limpieza completada: {cleaned_count} conversaciones eliminadas")
            
        except Exception as e:
            security_logger.error(f"Error en limpieza de conversaciones: {e}")
    
    @staticmethod
    def anonymize_old_logs(days_to_keep: int = 90):
        """Anonimiza logs antiguos"""
        try:
            logs_dir = Path("./logs")
            if not logs_dir.exists():
                return
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in logs_dir.glob("*.log"):
                # Implementar anonimización de logs antiguos
                # Por ahora, solo registramos la acción
                security_logger.info(f"Revisando log para anonimización: {log_file}")
            
        except Exception as e:
            security_logger.error(f"Error en anonimización de logs: {e}")

# Instancias globales
hamilton_encryption = HamiltonEncryption()
security_log = SecurityLogger()
data_cleaner = DataCleaner()
