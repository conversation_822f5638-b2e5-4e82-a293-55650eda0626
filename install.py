#!/usr/bin/env python3
"""
Hamilton AI Assistant - Installation Script
Script de instalación automatizada para Hamilton
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def print_header():
    """Imprime el header de instalación"""
    print("="*60)
    print("🤖 HAMILTON AI ASSISTANT - INSTALACIÓN")
    print("="*60)
    print("Instalando el asistente personal de IA para señor Ibero")
    print()

def check_python_version():
    """Verifica la versión de Python"""
    print("📋 Verificando versión de Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Error: Se requiere Python 3.8 o superior")
        print(f"   Versión actual: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_system_requirements():
    """Verifica requisitos del sistema"""
    print("\n🔍 Verificando requisitos del sistema...")
    
    system = platform.system()
    print(f"Sistema operativo: {system}")
    
    # Verificar si pip está disponible
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip disponible")
    except subprocess.CalledProcessError:
        print("❌ Error: pip no está disponible")
        return False
    
    return True

def install_python_dependencies():
    """Instala las dependencias de Python"""
    print("\n📦 Instalando dependencias de Python...")
    
    try:
        # Actualizar pip
        print("Actualizando pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        
        # Instalar dependencias
        print("Instalando dependencias desde requirements.txt...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        
        print("✅ Dependencias de Python instaladas exitosamente")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def check_swi_prolog():
    """Verifica si SWI-Prolog está instalado"""
    print("\n🧠 Verificando SWI-Prolog...")
    
    # Intentar ejecutar swipl
    try:
        result = subprocess.run(["swipl", "--version"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ SWI-Prolog está instalado")
            print(f"   Versión: {result.stdout.strip().split()[0]}")
            return True
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("⚠️ SWI-Prolog no está instalado")
    return False

def install_swi_prolog():
    """Proporciona instrucciones para instalar SWI-Prolog"""
    print("\n📥 INSTALACIÓN DE SWI-PROLOG")
    print("-" * 40)
    
    system = platform.system()
    
    if system == "Windows":
        print("Para Windows:")
        print("1. Visite: https://www.swi-prolog.org/download/stable")
        print("2. Descargue el instalador para Windows")
        print("3. Ejecute el instalador como administrador")
        print("4. O use Chocolatey: choco install swi-prolog")
        
    elif system == "Darwin":  # macOS
        print("Para macOS:")
        print("1. Instale Homebrew si no lo tiene: https://brew.sh")
        print("2. Ejecute: brew install swi-prolog")
        
    elif system == "Linux":
        print("Para Linux (Ubuntu/Debian):")
        print("1. Ejecute: sudo apt-get update")
        print("2. Ejecute: sudo apt-get install swi-prolog")
        print("\nPara otras distribuciones:")
        print("- Fedora: sudo dnf install pl")
        print("- Arch: sudo pacman -S swi-prolog")
    
    print("\n⚠️ SWI-Prolog es opcional pero recomendado para capacidades avanzadas")
    print("Hamilton funcionará con capacidades limitadas sin Prolog")

def setup_environment():
    """Configura el entorno"""
    print("\n⚙️ Configurando entorno...")
    
    # Crear archivo .env si no existe
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        try:
            shutil.copy(env_example, env_file)
            print("✅ Archivo .env creado desde .env.example")
        except Exception as e:
            print(f"⚠️ No se pudo crear .env: {e}")
    
    # Crear directorios necesarios
    directories = [
        "storage/faces",
        "storage/voices", 
        "storage/conversations",
        "logs",
        "uploads",
        "models"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directorios de almacenamiento creados")
    return True

def test_installation():
    """Prueba la instalación"""
    print("\n🧪 Probando instalación...")
    
    try:
        # Probar importaciones básicas
        print("Probando importaciones...")
        
        # Test básico de importación
        test_script = """
import sys
sys.path.append('.')

try:
    from config.settings import settings
    from recognition.voice_recognition import hamilton_voice
    from recognition.face_recognition import hamilton_face
    from core.hamilton_core import hamilton
    from logic.prolog_engine import hamilton_prolog
    print("✅ Todas las importaciones exitosas")
except ImportError as e:
    print(f"❌ Error de importación: {e}")
    sys.exit(1)
"""
        
        result = subprocess.run([sys.executable, "-c", test_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout.strip())
            return True
        else:
            print(f"❌ Error en prueba: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error ejecutando pruebas: {e}")
        return False

def main():
    """Función principal de instalación"""
    print_header()
    
    # Lista de pasos de instalación
    steps = [
        ("Verificar Python", check_python_version),
        ("Verificar Sistema", check_system_requirements),
        ("Instalar Dependencias", install_python_dependencies),
        ("Configurar Entorno", setup_environment),
        ("Probar Instalación", test_installation)
    ]
    
    results = {}
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name.upper()} {'='*20}")
        try:
            result = step_func()
            results[step_name] = result
            
            if not result:
                print(f"❌ Fallo en: {step_name}")
                break
                
        except Exception as e:
            print(f"❌ Error en {step_name}: {e}")
            results[step_name] = False
            break
    
    # Verificar SWI-Prolog por separado (opcional)
    print(f"\n{'='*20} VERIFICAR PROLOG {'='*20}")
    prolog_available = check_swi_prolog()
    
    if not prolog_available:
        install_swi_prolog()
    
    # Resumen final
    print("\n" + "="*60)
    print("📊 RESUMEN DE INSTALACIÓN")
    print("="*60)
    
    all_success = all(results.values())
    
    for step_name, result in results.items():
        status = "✅ EXITOSO" if result else "❌ FALLIDO"
        print(f"{step_name:.<30} {status}")
    
    print(f"SWI-Prolog:{'.'*19} {'✅ INSTALADO' if prolog_available else '⚠️ NO INSTALADO'}")
    
    if all_success:
        print("\n🎉 ¡Instalación completada exitosamente!")
        print("\n📋 PRÓXIMOS PASOS:")
        print("1. Registre su cara: python register_face.py")
        print("2. Pruebe el sistema: python test_hamilton.py")
        print("3. Ejecute Hamilton: python main.py")
        
        if not prolog_available:
            print("\n💡 RECOMENDACIÓN:")
            print("Instale SWI-Prolog para habilitar capacidades de razonamiento avanzado")
    else:
        print("\n❌ La instalación tuvo problemas")
        print("Revise los errores anteriores y vuelva a intentar")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nInstalación cancelada por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
