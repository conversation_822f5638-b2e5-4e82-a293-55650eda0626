#!/usr/bin/env python3
"""
Hamilton AI Assistant - SWI-Prolog Installation Script
Script para instalar y configurar SWI-Prolog automáticamente
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SWIPrologInstaller:
    """Instalador automático de SWI-Prolog"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.install_dir = Path("./prolog")
        
    def install(self):
        """Instala SWI-Prolog según el sistema operativo"""
        logger.info(f"🔧 Instalando SWI-Prolog para {self.system} ({self.architecture})")
        
        try:
            if self.system == "windows":
                return self._install_windows()
            elif self.system == "linux":
                return self._install_linux()
            elif self.system == "darwin":  # macOS
                return self._install_macos()
            else:
                logger.error(f"Sistema operativo no soportado: {self.system}")
                return False
        except Exception as e:
            logger.error(f"Error durante la instalación: {e}")
            return False
    
    def _install_windows(self):
        """Instala SWI-Prolog en Windows"""
        logger.info("📦 Instalando SWI-Prolog para Windows...")
        
        # URL de descarga para Windows
        if "64" in self.architecture or "amd64" in self.architecture:
            download_url = "https://www.swi-prolog.org/download/stable/bin/swipl-9.0.4-1.x64.exe"
        else:
            download_url = "https://www.swi-prolog.org/download/stable/bin/swipl-9.0.4-1.x86.exe"
        
        installer_path = "swipl_installer.exe"
        
        try:
            # Descargar instalador
            logger.info("⬇️ Descargando instalador...")
            urllib.request.urlretrieve(download_url, installer_path)
            
            # Ejecutar instalador silencioso
            logger.info("🔧 Ejecutando instalador...")
            result = subprocess.run([
                installer_path, 
                "/S",  # Instalación silenciosa
                "/D=C:\\Program Files\\swipl"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ SWI-Prolog instalado exitosamente")
                
                # Agregar al PATH
                self._add_to_path_windows("C:\\Program Files\\swipl\\bin")
                
                # Limpiar
                os.remove(installer_path)
                return True
            else:
                logger.error(f"Error en instalación: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error descargando/instalando: {e}")
            return False
    
    def _install_linux(self):
        """Instala SWI-Prolog en Linux"""
        logger.info("🐧 Instalando SWI-Prolog para Linux...")
        
        try:
            # Detectar distribución
            if self._command_exists("apt"):
                # Ubuntu/Debian
                commands = [
                    ["sudo", "apt", "update"],
                    ["sudo", "apt", "install", "-y", "swi-prolog"]
                ]
            elif self._command_exists("yum"):
                # CentOS/RHEL
                commands = [
                    ["sudo", "yum", "install", "-y", "swi-prolog"]
                ]
            elif self._command_exists("dnf"):
                # Fedora
                commands = [
                    ["sudo", "dnf", "install", "-y", "swi-prolog"]
                ]
            elif self._command_exists("pacman"):
                # Arch Linux
                commands = [
                    ["sudo", "pacman", "-S", "--noconfirm", "swi-prolog"]
                ]
            else:
                logger.error("Gestor de paquetes no soportado")
                return False
            
            # Ejecutar comandos
            for cmd in commands:
                logger.info(f"Ejecutando: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    logger.error(f"Error: {result.stderr}")
                    return False
            
            logger.info("✅ SWI-Prolog instalado exitosamente")
            return True
            
        except Exception as e:
            logger.error(f"Error en instalación Linux: {e}")
            return False
    
    def _install_macos(self):
        """Instala SWI-Prolog en macOS"""
        logger.info("🍎 Instalando SWI-Prolog para macOS...")
        
        try:
            if self._command_exists("brew"):
                # Usar Homebrew
                logger.info("Usando Homebrew...")
                result = subprocess.run([
                    "brew", "install", "swi-prolog"
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ SWI-Prolog instalado exitosamente")
                    return True
                else:
                    logger.error(f"Error con Homebrew: {result.stderr}")
                    return False
            else:
                logger.error("Homebrew no encontrado. Instale Homebrew primero.")
                return False
                
        except Exception as e:
            logger.error(f"Error en instalación macOS: {e}")
            return False
    
    def _command_exists(self, command):
        """Verifica si un comando existe"""
        try:
            subprocess.run([command, "--version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _add_to_path_windows(self, path):
        """Agrega directorio al PATH en Windows"""
        try:
            # Agregar al PATH del usuario actual
            current_path = os.environ.get("PATH", "")
            if path not in current_path:
                new_path = f"{current_path};{path}"
                subprocess.run([
                    "setx", "PATH", new_path
                ], capture_output=True)
                logger.info(f"✅ Agregado al PATH: {path}")
        except Exception as e:
            logger.warning(f"No se pudo agregar al PATH automáticamente: {e}")
    
    def verify_installation(self):
        """Verifica que SWI-Prolog esté instalado correctamente"""
        logger.info("🔍 Verificando instalación...")
        
        try:
            # Probar comando swipl
            result = subprocess.run([
                "swipl", "--version"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                logger.info(f"✅ SWI-Prolog verificado: {version}")
                return True
            else:
                logger.error("❌ SWI-Prolog no responde correctamente")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Timeout verificando SWI-Prolog")
            return False
        except FileNotFoundError:
            logger.error("❌ Comando 'swipl' no encontrado")
            return False
        except Exception as e:
            logger.error(f"❌ Error verificando: {e}")
            return False
    
    def test_pyswip_integration(self):
        """Prueba la integración con PySwip"""
        logger.info("🧪 Probando integración PySwip...")
        
        try:
            from pyswip import Prolog
            prolog = Prolog()
            
            # Prueba simple
            prolog.assertz("test_fact(hamilton)")
            results = list(prolog.query("test_fact(X)"))
            
            if results and results[0]['X'] == 'hamilton':
                logger.info("✅ PySwip integración exitosa")
                return True
            else:
                logger.error("❌ PySwip no funciona correctamente")
                return False
                
        except ImportError:
            logger.warning("⚠️ PySwip no instalado, instalando...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "pyswip"
                ], check=True)
                logger.info("✅ PySwip instalado")
                return self.test_pyswip_integration()  # Reintentar
            except Exception as e:
                logger.error(f"❌ Error instalando PySwip: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Error probando PySwip: {e}")
            return False

def main():
    """Función principal"""
    print("🚀 HAMILTON - INSTALADOR DE SWI-PROLOG")
    print("=" * 50)
    
    installer = SWIPrologInstaller()
    
    # Verificar si ya está instalado
    if installer.verify_installation():
        print("✅ SWI-Prolog ya está instalado")
        if installer.test_pyswip_integration():
            print("🎉 Todo listo para usar razonamiento Prolog completo!")
            return True
    
    # Instalar
    print("📦 Iniciando instalación...")
    if installer.install():
        print("⏳ Esperando que la instalación se complete...")
        
        # Verificar instalación
        if installer.verify_installation():
            if installer.test_pyswip_integration():
                print("\n🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!")
                print("Hamilton ahora tiene capacidades de razonamiento Prolog completas.")
                return True
            else:
                print("⚠️ SWI-Prolog instalado pero PySwip tiene problemas")
                return False
        else:
            print("❌ Instalación falló o no se puede verificar")
            return False
    else:
        print("❌ Error durante la instalación")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
