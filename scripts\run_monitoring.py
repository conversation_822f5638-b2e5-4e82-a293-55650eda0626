#!/usr/bin/env python3
"""
Script para ejecutar el monitoreo del sistema.
"""

import os
import sys
import json
import time
import psutil
import logging
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/monitoring.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'monitoring']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivo de métricas
        metrics_file = Path('logs/metrics.json')
        if not metrics_file.exists():
            with open(metrics_file, 'w') as f:
                f.write('[]')
            logger.info("Archivo de métricas creado")

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def get_system_metrics():
    """Obtiene las métricas del sistema."""
    try:
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # Memoria
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disco
        disk = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # Red
        net_io = psutil.net_io_counters()
        
        # Procesos
        processes = len(psutil.pids())
        
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count,
                'freq': {
                    'current': cpu_freq.current,
                    'min': cpu_freq.min,
                    'max': cpu_freq.max
                }
            },
            'memory': {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used,
                'free': memory.free
            },
            'swap': {
                'total': swap.total,
                'used': swap.used,
                'free': swap.free,
                'percent': swap.percent
            },
            'disk': {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': disk.percent
            },
            'disk_io': {
                'read_bytes': disk_io.read_bytes,
                'write_bytes': disk_io.write_bytes,
                'read_count': disk_io.read_count,
                'write_count': disk_io.write_count
            },
            'net_io': {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            },
            'processes': processes
        }
    except Exception as e:
        logger.error(f"Error al obtener métricas del sistema: {str(e)}")
        return None

def get_process_metrics():
    """Obtiene las métricas de los procesos."""
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent']):
            try:
                pinfo = proc.info
                processes.append({
                    'pid': pinfo['pid'],
                    'name': pinfo['name'],
                    'username': pinfo['username'],
                    'cpu_percent': pinfo['cpu_percent'],
                    'memory_percent': pinfo['memory_percent']
                })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
                
        return processes
    except Exception as e:
        logger.error(f"Error al obtener métricas de procesos: {str(e)}")
        return None

def save_metrics(metrics):
    """Guarda las métricas en el archivo."""
    try:
        metrics_file = Path('logs/metrics.json')
        
        # Leer métricas existentes
        if metrics_file.exists():
            with open(metrics_file, 'r') as f:
                existing_metrics = json.load(f)
        else:
            existing_metrics = []
            
        # Agregar nuevas métricas
        existing_metrics.append(metrics)
        
        # Mantener solo las últimas 1000 métricas
        if len(existing_metrics) > 1000:
            existing_metrics = existing_metrics[-1000:]
            
        # Guardar métricas actualizadas
        with open(metrics_file, 'w') as f:
            json.dump(existing_metrics, f, indent=2)
            
        return True
    except Exception as e:
        logger.error(f"Error al guardar métricas: {str(e)}")
        return False

def monitor_system(interval=60):
    """Monitorea el sistema."""
    try:
        logger.info(f"Iniciando monitoreo del sistema (intervalo: {interval}s)...")
        
        while True:
            # Obtener métricas
            system_metrics = get_system_metrics()
            process_metrics = get_process_metrics()
            
            if system_metrics and process_metrics:
                # Combinar métricas
                metrics = {
                    **system_metrics,
                    'processes': process_metrics
                }
                
                # Guardar métricas
                if save_metrics(metrics):
                    logger.info("Métricas guardadas exitosamente")
                else:
                    logger.error("Error al guardar métricas")
                    
            # Esperar el intervalo
            time.sleep(interval)
            
    except KeyboardInterrupt:
        logger.info("Monitoreo detenido por el usuario")
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")

def main():
    """Función principal."""
    try:
        logger.info("Iniciando monitoreo del sistema...")
        
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            sys.exit(1)
            
        # Obtener intervalo de los argumentos
        interval = 60  # 1 minuto por defecto
        if len(sys.argv) > 1:
            try:
                interval = int(sys.argv[1])
            except ValueError:
                print("Error: El intervalo debe ser un número entero de segundos")
                sys.exit(1)
                
        monitor_system(interval)
        
    except KeyboardInterrupt:
        logger.info("Monitoreo detenido por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 