#!/usr/bin/env python3
"""
Hamilton AI Assistant - Prolog Engine Test
Script de pruebas específico para el motor de razonamiento Prolog
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Agregar el directorio actual al path
sys.path.append(str(Path(__file__).parent))

from logic.prolog_engine import hamilton_prolog
from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_prolog_initialization():
    """Prueba la inicialización del motor Prolog"""
    print("\n🧠 PRUEBA DE INICIALIZACIÓN PROLOG")
    print("-" * 50)
    
    info = hamilton_prolog.get_system_info()
    print(f"Prolog disponible: {'✅' if info['prolog_available'] else '❌'}")
    print(f"Base de conocimientos: {'✅' if info['knowledge_base_loaded'] else '❌'}")
    print(f"Hechos cargados: {info['facts_count']}")
    print(f"Preferencias de usuario: {'✅' if info['user_preferences_loaded'] else '❌'}")
    
    return info['prolog_available']

def test_basic_queries():
    """Prueba consultas básicas de Prolog"""
    print("\n🔍 PRUEBA DE CONSULTAS BÁSICAS")
    print("-" * 50)
    
    # Consultas de prueba
    test_queries = [
        ("usuario_autorizado(senor_ibero)", "Usuario autorizado"),
        ("capacidad(hamilton, reconocimiento_facial)", "Capacidad de reconocimiento facial"),
        ("estado_valido(activo)", "Estado válido 'activo'"),
        ("personalidad(hamilton, profesional)", "Personalidad profesional"),
        ("es_ciencia(matematicas)", "Matemáticas es ciencia")
    ]
    
    results = {}
    
    for query, description in test_queries:
        try:
            result = hamilton_prolog.query(query)
            success = len(result) > 0
            results[description] = success
            status = "✅" if success else "❌"
            print(f"{description}: {status}")
            if success and result:
                print(f"  Resultado: {result[0] if result else 'True'}")
        except Exception as e:
            results[description] = False
            print(f"{description}: ❌ Error: {e}")
    
    return results

def test_reasoning_capabilities():
    """Prueba capacidades de razonamiento"""
    print("\n🤔 PRUEBA DE CAPACIDADES DE RAZONAMIENTO")
    print("-" * 50)
    
    # Prueba de autenticación
    print("1. Prueba de autorización de usuario:")
    can_activate = hamilton_prolog.can_user_activate("senor_ibero")
    print(f"   ¿Puede señor_ibero activar Hamilton? {'✅ Sí' if can_activate else '❌ No'}")
    
    # Prueba de saludo apropiado
    print("\n2. Prueba de saludo apropiado:")
    current_hour = datetime.now().hour
    greeting = hamilton_prolog.get_appropriate_greeting(current_hour)
    print(f"   Hora actual: {current_hour}")
    print(f"   Saludo apropiado: {greeting}")
    
    # Prueba de análisis de intención
    print("\n3. Prueba de análisis de intención:")
    test_commands = [
        "¿Qué hora es?",
        "Hola Hamilton",
        "Hamilton, ejecuta el análisis"
    ]
    
    for command in test_commands:
        intention = hamilton_prolog.analyze_user_intention("senor_ibero", command)
        print(f"   Comando: '{command}'")
        print(f"   Intención: {intention}")
    
    # Prueba de capacidades científicas
    print("\n4. Prueba de capacidades científicas:")
    science_topics = ["matematicas", "fisica", "quimica", "historia"]
    
    for topic in science_topics:
        query = f"puede_ayudar_con(hamilton, {topic})"
        result = hamilton_prolog.query(query)
        can_help = len(result) > 0
        print(f"   ¿Puede ayudar con {topic}? {'✅ Sí' if can_help else '❌ No'}")
    
    return True

def test_dynamic_facts():
    """Prueba manejo de hechos dinámicos"""
    print("\n⚡ PRUEBA DE HECHOS DINÁMICOS")
    print("-" * 50)
    
    # Actualizar estado del sistema
    print("1. Actualizando estado del sistema:")
    hamilton_prolog.update_system_state("hamilton", "activo")
    
    # Verificar estado
    query_result = hamilton_prolog.query("estado_actual(hamilton, Estado)")
    if query_result and 'Estado' in query_result[0]:
        current_state = query_result[0]['Estado']
        print(f"   Estado actual de Hamilton: {current_state}")
    
    # Agregar hecho dinámico
    print("\n2. Agregando hecho dinámico:")
    test_fact = "preferencia_usuario(senor_ibero, tema_favorito, tecnologia)"
    hamilton_prolog.assert_fact(test_fact)
    print(f"   Hecho agregado: {test_fact}")
    
    # Verificar hecho agregado
    verify_query = "preferencia_usuario(senor_ibero, tema_favorito, Tema)"
    verify_result = hamilton_prolog.query(verify_query)
    if verify_result and 'Tema' in verify_result[0]:
        tema = verify_result[0]['Tema']
        print(f"   Verificación: Tema favorito = {tema}")
    
    # Agregar comando al historial
    print("\n3. Agregando comando al historial:")
    hamilton_prolog.add_command_to_history("senor_ibero", "prueba de comando")
    
    # Verificar historial
    history_query = "historial_comando(senor_ibero, Comando, _, _)"
    history_result = hamilton_prolog.query(history_query)
    print(f"   Comandos en historial: {len(history_result)}")
    
    return True

def test_advanced_reasoning():
    """Prueba razonamiento avanzado"""
    print("\n🎯 PRUEBA DE RAZONAMIENTO AVANZADO")
    print("-" * 50)
    
    # Prueba de inferencia de personalidad
    print("1. Inferencia de personalidad:")
    personality_query = "personalidad(hamilton, Trait)"
    personality_results = hamilton_prolog.query(personality_query)
    
    if personality_results:
        traits = [result.get('Trait', 'unknown') for result in personality_results]
        print(f"   Rasgos de personalidad de Hamilton: {', '.join(traits)}")
    
    # Prueba de reglas de tiempo
    print("\n2. Reglas de tiempo y contexto:")
    current_hour = datetime.now().hour
    
    # Verificar si es momento apropiado para trabajo
    work_query = f"momento_apropiado(trabajo, {current_hour})"
    work_result = hamilton_prolog.query(work_query)
    is_work_time = len(work_result) > 0
    print(f"   ¿Es momento apropiado para trabajo? {'✅ Sí' if is_work_time else '❌ No'}")
    
    # Verificar si es momento de descanso
    rest_query = f"momento_apropiado(descanso, {current_hour})"
    rest_result = hamilton_prolog.query(rest_query)
    is_rest_time = len(rest_result) > 0
    print(f"   ¿Es momento de descanso? {'✅ Sí' if is_rest_time else '❌ No'}")
    
    # Prueba de reglas de prioridad
    print("\n3. Análisis de prioridades:")
    priority_tests = [
        "comando_emergencia",
        "comando_informacion", 
        "comando_conversacion"
    ]
    
    for command_type in priority_tests:
        priority = hamilton_prolog.get_command_priority(command_type)
        print(f"   Prioridad de '{command_type}': {priority}")
    
    return True

def test_knowledge_base_content():
    """Prueba contenido de la base de conocimientos"""
    print("\n📚 PRUEBA DE CONTENIDO DE BASE DE CONOCIMIENTOS")
    print("-" * 50)
    
    # Contar diferentes tipos de hechos
    fact_types = [
        ("usuario_autorizado(_)", "Usuarios autorizados"),
        ("capacidad(hamilton, _)", "Capacidades de Hamilton"),
        ("estado_valido(_)", "Estados válidos"),
        ("es_ciencia(_)", "Temas científicos"),
        ("personalidad(hamilton, _)", "Rasgos de personalidad")
    ]
    
    for query, description in fact_types:
        try:
            results = hamilton_prolog.query(query)
            count = len(results)
            print(f"{description}: {count} elemento(s)")
            
            if count > 0 and count <= 5:  # Mostrar algunos ejemplos
                examples = [str(list(result.values())[0]) if result else "True" for result in results[:3]]
                print(f"   Ejemplos: {', '.join(examples)}")
        except Exception as e:
            print(f"{description}: Error - {e}")
    
    return True

def main():
    """Función principal de pruebas Prolog"""
    print("="*60)
    print("🧠 HAMILTON AI ASSISTANT - PRUEBAS DEL MOTOR PROLOG")
    print("="*60)
    
    tests = [
        ("Inicialización", test_prolog_initialization),
        ("Consultas Básicas", test_basic_queries),
        ("Capacidades de Razonamiento", test_reasoning_capabilities),
        ("Hechos Dinámicos", test_dynamic_facts),
        ("Razonamiento Avanzado", test_advanced_reasoning),
        ("Contenido de Base de Conocimientos", test_knowledge_base_content)
    ]
    
    results = {}
    prolog_available = False
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name.upper()} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            
            if test_name == "Inicialización":
                prolog_available = result
            
            print(f"✅ {test_name}: {'EXITOSO' if result else 'FALLIDO'}")
        except Exception as e:
            logger.error(f"Error en {test_name}: {e}")
            results[test_name] = False
            print(f"❌ {test_name}: ERROR - {e}")
    
    # Resumen de resultados
    print("\n" + "="*60)
    print("📊 RESUMEN DE PRUEBAS PROLOG")
    print("="*60)
    
    if not prolog_available:
        print("⚠️ PROLOG NO DISPONIBLE")
        print("Para habilitar todas las funcionalidades:")
        print("1. Instalar SWI-Prolog: https://www.swi-prolog.org/download/stable")
        print("2. Instalar PySwip: pip install pyswip")
        print("3. Configurar variables de entorno si es necesario")
        print("\nHamilton funcionará con capacidades limitadas sin Prolog.")
    else:
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_name, result in results.items():
            status = "✅ EXITOSO" if result else "❌ FALLIDO"
            print(f"{test_name:.<40} {status}")
        
        print(f"\nTotal: {passed_tests}/{total_tests} pruebas exitosas")
        
        if passed_tests == total_tests:
            print("\n🎉 ¡Todas las pruebas de Prolog pasaron!")
            print("Hamilton está listo con capacidades de razonamiento avanzado.")
        else:
            print(f"\n⚠️ {total_tests - passed_tests} prueba(s) fallaron.")
    
    print("\n💡 Para probar Hamilton completo:")
    print("1. Ejecute: python test_hamilton.py")
    print("2. Registre su cara: python register_face.py")
    print("3. Ejecute Hamilton: python main.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nPruebas canceladas por el usuario")
    except Exception as e:
        logger.error(f"Error en pruebas Prolog: {e}")
        print(f"❌ Error inesperado: {e}")
    finally:
        print("\nCerrando pruebas Prolog...")
