"""
Hamilton AI Assistant - GPT Integration
Integración con OpenAI GPT para capacidades avanzadas de lenguaje
"""

import os
import logging
from typing import Dict, Any, Optional, List
from openai import OpenAI
from datetime import datetime
import json

from config.settings import settings
from security.encryption import security_log

# Configurar logging
logger = logging.getLogger(__name__)

class HamiltonGPTEngine:
    """Motor de GPT para Hamilton"""
    
    def __init__(self):
        self.api_key = settings.OPENAI_API_KEY
        self.model = settings.OPENAI_MODEL
        self.client = None
        self._initialize_openai()
        self.conversation_history: List[Dict[str, str]] = []
        self.max_history = 10
    
    def _initialize_openai(self):
        """Inicializa la conexión con OpenAI"""
        if not self.api_key:
            logger.warning("OpenAI API Key no configurada")
            return False
        
        try:
            self.client = OpenAI(api_key=self.api_key)
            # Verificar conexión
            self.client.models.list()
            logger.info(f"OpenAI inicializado con modelo: {self.model}")
            return True
        except Exception as e:
            logger.error(f"Error inicializando OpenAI: {e}")
            return False
    
    def process_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Procesa una consulta usando GPT
        
        Args:
            query: Consulta del usuario
            context: Contexto adicional (opcional)
            
        Returns:
            Respuesta generada por GPT
        """
        if not self.api_key or not self.client:
            return "Lo siento, la integración con GPT no está configurada."
        
        try:
            # Preparar historial de conversación
            messages = self._prepare_messages(query, context)
            
            # Llamar a la API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=150
            )
            
            # Extraer respuesta
            answer = response.choices[0].message.content
            
            # Actualizar historial
            self._update_conversation_history(query, answer)
            
            # Registrar acceso
            security_log.log_data_access(
                user=settings.AUTHORIZED_USER,
                data_type="gpt_query",
                action="process"
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Error procesando consulta GPT: {e}")
            return "Lo siento, hubo un error procesando su consulta."
    
    def _prepare_messages(self, query: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """Prepara los mensajes para la API de GPT"""
        messages = [
            {"role": "system", "content": "Eres Hamilton, un asistente personal inteligente y servicial. Tu objetivo es ayudar al usuario de manera eficiente y amigable."}
        ]
        
        # Agregar contexto si está disponible
        if context:
            context_str = f"Contexto: {json.dumps(context)}"
            messages.append({"role": "system", "content": context_str})
        
        # Agregar historial de conversación
        for msg in self.conversation_history[-self.max_history:]:
            messages.append(msg)
        
        # Agregar consulta actual
        messages.append({"role": "user", "content": query})
        
        return messages
    
    def _update_conversation_history(self, query: str, answer: str):
        """Actualiza el historial de conversación"""
        self.conversation_history.append({"role": "user", "content": query})
        self.conversation_history.append({"role": "assistant", "content": answer})
        
        # Mantener historial dentro del límite
        if len(self.conversation_history) > self.max_history * 2:
            self.conversation_history = self.conversation_history[-(self.max_history * 2):]
    
    def clear_history(self):
        """Limpia el historial de conversación"""
        self.conversation_history = []
        logger.info("Historial de conversación GPT limpiado")

# Instancia global
hamilton_gpt = HamiltonGPTEngine() 