"""
Hamilton AI Assistant - Core Module
<PERSON>úcle<PERSON> principal del asistente personal Hamilton
"""

import asyncio
import logging
import threading
import time
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import json

from recognition.voice_recognition import hamilton_voice
from recognition.face_recognition import hamilton_face
from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HamiltonCore:
    """
    Núcleo principal de Hamilton - Coordina todos los módulos del asistente
    """
    
    def __init__(self):
        self.is_active = False
        self.is_authenticated = False
        self.current_user = None
        self.session_start_time = None
        self.conversation_history = []
        self.learning_data = {}
        self.voice_engine = hamilton_voice
        self.face_engine = hamilton_face
        self.status = "dormido"  # dormido, escuchando, procesando, hablando
        
        # Callbacks para eventos
        self.on_wake_callback = None
        self.on_sleep_callback = None
        self.on_authentication_callback = None
        self.on_command_callback = None
        
        logger.info("Hamilton Core inicializado")
    
    def greet_user(self):
        """Hamilton saluda al usuario autenticado"""
        if not self.current_user:
            return
            
        current_time = datetime.now()
        hour = current_time.hour
        
        if 5 <= hour < 12:
            greeting = f"Buenos días, señor Ibero. Soy Hamilton, su asistente personal."
        elif 12 <= hour < 18:
            greeting = f"Buenas tardes, señor Ibero. Hamilton a su servicio."
        else:
            greeting = f"Buenas noches, señor Ibero. Hamilton está aquí para ayudarle."
        
        # Agregar información contextual
        if self.session_start_time:
            greeting += f" ¿En qué puedo asistirle hoy?"
        else:
            greeting += f" Estoy listo para ayudarle con cualquier tarea."
        
        self.speak(greeting)
        logger.info(f"Hamilton saludó al usuario: {self.current_user}")
    
    def speak(self, text: str, priority: bool = False):
        """
        Hamilton habla con voz masculina
        
        Args:
            text: Texto a pronunciar
            priority: Si es prioritario (interrumpe habla actual)
        """
        self.status = "hablando"
        self.voice_engine.speak(text, interrupt_current=priority)
        self.status = "escuchando" if self.is_active else "dormido"
        
        # Registrar en historial de conversación
        self._add_to_conversation("hamilton", text)
    
    def authenticate_user(self) -> bool:
        """
        Autentica al usuario usando reconocimiento facial
        
        Returns:
            True si la autenticación fue exitosa
        """
        logger.info("Iniciando autenticación facial...")
        self.speak("Iniciando reconocimiento facial. Por favor, mire a la cámara.")
        
        success, user_name, confidence = self.face_engine.authenticate_user()
        
        if success and user_name == settings.AUTHORIZED_USER:
            self.is_authenticated = True
            self.current_user = user_name
            self.session_start_time = datetime.now()
            
            logger.info(f"Usuario autenticado: {user_name} (confianza: {confidence:.2f})")
            
            if self.on_authentication_callback:
                self.on_authentication_callback(True, user_name, confidence)
            
            return True
        else:
            self.is_authenticated = False
            self.current_user = None
            
            if success:
                self.speak("Usuario reconocido pero no autorizado. Acceso denegado.")
                logger.warning(f"Usuario no autorizado: {user_name}")
            else:
                self.speak("No se pudo autenticar. Acceso denegado.")
                logger.warning("Fallo en autenticación facial")
            
            if self.on_authentication_callback:
                self.on_authentication_callback(False, user_name, confidence)
            
            return False
    
    def wake_up(self):
        """Despierta a Hamilton y lo pone en modo activo"""
        if self.is_active:
            logger.info("Hamilton ya está activo")
            return
        
        logger.info("Hamilton despertando...")
        self.status = "despertando"
        
        # Autenticar usuario
        if not self.authenticate_user():
            self.speak("Autenticación fallida. Hamilton permanecerá inactivo.")
            self.status = "dormido"
            return
        
        # Activar Hamilton
        self.is_active = True
        self.status = "escuchando"
        
        # Saludar al usuario
        self.greet_user()
        
        # Iniciar escucha continua
        self.voice_engine.start_continuous_listening(self._process_voice_input)
        
        if self.on_wake_callback:
            self.on_wake_callback()
        
        logger.info("Hamilton está ahora activo y escuchando")
    
    def sleep(self):
        """Pone a Hamilton en modo dormido"""
        if not self.is_active:
            logger.info("Hamilton ya está dormido")
            return
        
        logger.info("Hamilton entrando en modo dormido...")
        
        # Despedirse del usuario
        self.speak("Hasta luego, señor Ibero. Hamilton entrando en modo dormido.")
        
        # Desactivar
        self.is_active = False
        self.is_authenticated = False
        self.status = "dormido"
        
        # Detener escucha continua
        self.voice_engine.stop_continuous_listening()
        
        # Guardar datos de la sesión
        self._save_session_data()
        
        # Limpiar datos de sesión
        self.current_user = None
        self.session_start_time = None
        
        if self.on_sleep_callback:
            self.on_sleep_callback()
        
        logger.info("Hamilton está ahora dormido")
    
    def _process_voice_input(self, text: str):
        """
        Procesa la entrada de voz del usuario
        
        Args:
            text: Texto reconocido de la voz
        """
        if not self.is_active or not self.is_authenticated:
            return
        
        logger.info(f"Procesando comando de voz: {text}")
        self.status = "procesando"
        
        # Registrar en historial
        self._add_to_conversation("user", text)
        
        # Verificar comandos especiales
        if self._is_sleep_command(text):
            self.sleep()
            return
        
        # Procesar comando general
        response = self._process_command(text)
        
        if response:
            self.speak(response)
        
        if self.on_command_callback:
            self.on_command_callback(text, response)
        
        self.status = "escuchando"
    
    def _is_sleep_command(self, text: str) -> bool:
        """Verifica si el comando es para dormir a Hamilton"""
        sleep_commands = [
            "hamilton duerme",
            "hamilton descansa",
            "hamilton modo dormido",
            "hamilton apágate",
            "hamilton hasta luego",
            "hamilton adiós"
        ]
        
        text_lower = text.lower().strip()
        return any(cmd in text_lower for cmd in sleep_commands)
    
    def _process_command(self, text: str) -> str:
        """
        Procesa un comando general del usuario
        
        Args:
            text: Comando del usuario
            
        Returns:
            Respuesta de Hamilton
        """
        text_lower = text.lower().strip()
        
        # Comandos básicos
        if any(greeting in text_lower for greeting in ["hola", "buenos días", "buenas tardes", "buenas noches"]):
            return "Hola señor Ibero, ¿en qué puedo ayudarle?"
        
        elif "cómo estás" in text_lower or "como estas" in text_lower:
            return "Estoy funcionando perfectamente, señor Ibero. Todos mis sistemas están operativos."
        
        elif "qué hora es" in text_lower or "que hora es" in text_lower:
            current_time = datetime.now().strftime("%H:%M")
            return f"Son las {current_time}, señor Ibero."
        
        elif "qué día es" in text_lower or "que dia es" in text_lower:
            current_date = datetime.now().strftime("%A, %d de %B de %Y")
            return f"Hoy es {current_date}, señor Ibero."
        
        elif "hamilton ayuda" in text_lower:
            return self._get_help_message()
        
        elif "hamilton información" in text_lower or "hamilton info" in text_lower:
            return self._get_system_info()
        
        else:
            # Comando no reconocido
            return f"Entiendo que me dice: {text}. Sin embargo, aún estoy aprendiendo a procesar este tipo de solicitudes. ¿Podría ser más específico?"
    
    def _get_help_message(self) -> str:
        """Retorna mensaje de ayuda"""
        return """Soy Hamilton, su asistente personal. Puedo ayudarle con:
        - Información de fecha y hora
        - Conversación básica
        - Comandos de sistema
        Para dormir, diga 'Hamilton duerme' o 'Hamilton adiós'."""
    
    def _get_system_info(self) -> str:
        """Retorna información del sistema"""
        uptime = ""
        if self.session_start_time:
            delta = datetime.now() - self.session_start_time
            uptime = f"Tiempo activo: {delta.seconds // 60} minutos. "
        
        return f"Hamilton versión {settings.VERSION}. {uptime}Todos los sistemas funcionando correctamente."
    
    def _add_to_conversation(self, speaker: str, text: str):
        """Agrega entrada al historial de conversación"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "speaker": speaker,
            "text": text
        }
        self.conversation_history.append(entry)
        
        # Mantener solo las últimas 100 entradas
        if len(self.conversation_history) > 100:
            self.conversation_history = self.conversation_history[-100:]
    
    def _save_session_data(self):
        """Guarda datos de la sesión actual"""
        if not self.session_start_time:
            return
        
        try:
            session_data = {
                "user": self.current_user,
                "start_time": self.session_start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "conversation_history": self.conversation_history,
                "learning_data": self.learning_data
            }
            
            # Guardar en archivo (implementar base de datos más adelante)
            session_file = f"./storage/conversations/session_{int(time.time())}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Datos de sesión guardados: {session_file}")
            
        except Exception as e:
            logger.error(f"Error guardando datos de sesión: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Retorna el estado actual de Hamilton"""
        return {
            "is_active": self.is_active,
            "is_authenticated": self.is_authenticated,
            "current_user": self.current_user,
            "status": self.status,
            "session_start_time": self.session_start_time.isoformat() if self.session_start_time else None,
            "conversation_entries": len(self.conversation_history)
        }

# Instancia global de Hamilton
hamilton = HamiltonCore()
