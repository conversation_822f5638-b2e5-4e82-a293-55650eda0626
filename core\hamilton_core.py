"""
Hamilton AI Assistant - Core Module
<PERSON>ú<PERSON> principal del asistente personal Hamilton
"""

import asyncio
import logging
import threading
import time
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import json

from recognition.voice_recognition import hamilton_voice
from recognition.face_recognition import hamilton_face
from logic.prolog_engine import hamilton_prolog
from storage.database_manager import hamilton_db
from ai.learning_engine import hamilton_learning
from security.encryption import security_log, data_cleaner
from config.settings import settings

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HamiltonCore:
    """
    Núcleo principal de Hamilton - Coordina todos los módulos del asistente
    """
    
    def __init__(self):
        """Inicializa el núcleo de Hamilton"""
        self.logger = logging.getLogger(__name__)
        self.is_active = False
        self.is_authenticated = False
        self.current_user = None
        self.session_start_time = None
        self.conversation_history = []
        self.learning_data = {}
        self.voice_engine = hamilton_voice
        self.face_engine = hamilton_face
        self.prolog_engine = hamilton_prolog
        self.database = hamilton_db
        self.learning_engine = hamilton_learning
        self.current_session_id = None
        self.status = "dormido"  # dormido, escuchando, procesando, hablando
        
        # Callbacks para eventos
        self.on_wake_callback = None
        self.on_sleep_callback = None
        self.on_authentication_callback = None
        self.on_command_callback = None
        
        logger.info("Hamilton Core inicializado")
    
    def greet_user(self):
        """Hamilton saluda al usuario autenticado usando razonamiento Prolog"""
        if not self.current_user:
            return

        current_time = datetime.now()
        hour = current_time.hour

        # Usar Prolog para determinar saludo apropiado
        prolog_greeting = self.prolog_engine.get_appropriate_greeting(hour)

        if prolog_greeting:
            greeting = f"{prolog_greeting}, señor Ibero. Soy Hamilton, su asistente personal."
        else:
            # Fallback si Prolog no está disponible
            if 5 <= hour < 12:
                greeting = f"Buenos días, señor Ibero. Soy Hamilton, su asistente personal."
            elif 12 <= hour < 18:
                greeting = f"Buenas tardes, señor Ibero. Hamilton a su servicio."
            else:
                greeting = f"Buenas noches, señor Ibero. Hamilton está aquí para ayudarle."

        # Agregar información contextual
        if self.session_start_time:
            greeting += f" ¿En qué puedo asistirle hoy?"
        else:
            greeting += f" Estoy listo para ayudarle con cualquier tarea."

        self.speak(greeting)
        logger.info(f"Hamilton saludó al usuario: {self.current_user}")
    
    def speak(self, text: str, priority: bool = False):
        """
        Hamilton habla con voz masculina
        
        Args:
            text: Texto a pronunciar
            priority: Si es prioritario (interrumpe habla actual)
        """
        self.status = "hablando"
        self.voice_engine.speak(text, interrupt_current=priority)
        self.status = "escuchando" if self.is_active else "dormido"
        
        # Registrar en historial de conversación
        self._add_to_conversation("hamilton", text)
    
    def authenticate_user(self):
        """Autentica al usuario usando reconocimiento facial"""
        try:
            # Intentar autenticación facial
            success, user, confidence = self.face_engine.authenticate_user()
            
            if success and self.prolog_engine.can_user_activate(user):
                self.is_authenticated = True
                self.current_user = user
                self.logger.info(f"Usuario autenticado: {user} (confianza: {confidence:.2f})")
                return True
            else:
                self.is_authenticated = False
                self.current_user = None
                self.logger.warning(f"Autenticación fallida para {user} (confianza: {confidence:.2f})")
                return False
            
        except Exception as e:
            self.logger.error(f"Error en autenticación: {str(e)}")
            self.is_authenticated = False
            self.current_user = None
            return False
    
    def wake_up(self):
        """Activa Hamilton y comienza a escuchar"""
        try:
            # Autenticar usuario
            if self.authenticate_user():
                self.is_active = True
                self.status = "escuchando"
                
                # Obtener saludo apropiado
                greeting = self.prolog_engine.get_appropriate_greeting()
                self.speak(greeting)
                
                # Iniciar escucha continua
                self.voice_engine.start_continuous_listening()
                
                self.logger.info("Hamilton activado y escuchando")
                return True
            else:
                self.logger.warning("No se pudo activar Hamilton: autenticación fallida")
                return False
            
        except Exception as e:
            self.logger.error(f"Error al activar Hamilton: {str(e)}")
            return False
    
    def sleep(self):
        """Pone a Hamilton en modo dormido"""
        if not self.is_active:
            logger.info("Hamilton ya está dormido")
            return
        
        logger.info("Hamilton entrando en modo dormido...")
        
        # Despedirse del usuario
        self.speak("Hasta luego, señor Ibero. Hamilton entrando en modo dormido.")
        
        # Desactivar
        self.is_active = False
        self.is_authenticated = False
        self.status = "dormido"
        
        # Detener escucha continua
        self.voice_engine.stop_continuous_listening()
        
        # Finalizar sesión en base de datos
        if self.current_session_id:
            self.database.end_session(self.current_session_id)

        # Guardar datos de la sesión (método legacy)
        self._save_session_data()

        # Ejecutar limpieza automática de datos antiguos
        data_cleaner.clean_old_conversations()

        # Limpiar datos de sesión
        self.current_user = None
        self.session_start_time = None
        self.current_session_id = None
        
        if self.on_sleep_callback:
            self.on_sleep_callback()
        
        logger.info("Hamilton está ahora dormido")
    
    def _process_voice_input(self, text: str):
        """
        Procesa la entrada de voz del usuario
        
        Args:
            text: Texto reconocido de la voz
        """
        if not self.is_active or not self.is_authenticated:
            return
        
        logger.info(f"Procesando comando de voz: {text}")
        self.status = "procesando"
        
        # Registrar en historial y base de datos
        self._add_to_conversation("user", text)
        if self.current_session_id:
            self.database.save_conversation(
                session_id=self.current_session_id,
                speaker="user",
                message=text
            )

        # Verificar comandos especiales
        if self._is_sleep_command(text):
            self.sleep()
            return

        # Procesar comando general
        start_time = datetime.now()
        response = self._process_command(text)
        response_time = (datetime.now() - start_time).total_seconds() * 1000

        if response:
            self.speak(response)

            # Registrar respuesta en base de datos
            if self.current_session_id:
                self.database.save_conversation(
                    session_id=self.current_session_id,
                    speaker="hamilton",
                    message=response,
                    response_time_ms=int(response_time)
                )

            # Aprender de la interacción
            self.learning_engine.learn_from_interaction(
                username=self.current_user,
                user_message=text,
                hamilton_response=response
            )

        if self.on_command_callback:
            self.on_command_callback(text, response)

        self.status = "escuchando"
    
    def _is_sleep_command(self, text: str) -> bool:
        """Verifica si el comando es para dormir a Hamilton"""
        sleep_commands = [
            "hamilton duerme",
            "hamilton descansa",
            "hamilton modo dormido",
            "hamilton apágate",
            "hamilton hasta luego",
            "hamilton adiós"
        ]
        
        text_lower = text.lower().strip()
        return any(cmd in text_lower for cmd in sleep_commands)
    
    def _process_command(self, text: str) -> str:
        """
        Procesa un comando general del usuario usando razonamiento Prolog

        Args:
            text: Comando del usuario

        Returns:
            Respuesta de Hamilton
        """
        text_lower = text.lower().strip()

        # Analizar intención del usuario con Prolog
        intention = self.prolog_engine.analyze_user_intention(self.current_user, text)
        logger.info(f"Intención detectada: {intention}")

        # Verificar si Hamilton debe responder
        user_for_check = self.current_user if self.current_user else "senor_ibero"
        should_respond = self.prolog_engine.should_respond(user_for_check)
        if not should_respond:
            return "Lo siento, no puedo procesar comandos en este momento."

        # Procesar según intención
        if intention == "obtener_informacion":
            return self._process_information_request(text_lower)
        elif intention == "conversacion_casual":
            return self._process_casual_conversation(text_lower)
        elif intention == "solicitar_accion":
            return self._process_action_request(text_lower)
        else:
            return self._process_general_command(text_lower)

    def _process_information_request(self, text: str) -> str:
        """Procesa solicitudes de información"""
        if "qué hora es" in text or "que hora es" in text:
            current_time = datetime.now().strftime("%H:%M")
            return f"Son las {current_time}, señor Ibero."

        elif "qué día es" in text or "que dia es" in text:
            current_date = datetime.now().strftime("%A, %d de %B de %Y")
            return f"Hoy es {current_date}, señor Ibero."

        elif "hamilton información" in text or "hamilton info" in text:
            return self._get_system_info_with_prolog()

        elif any(science in text for science in ["matemáticas", "física", "química", "historia"]):
            return self._process_science_query(text)

        else:
            return "¿Qué información específica necesita, señor Ibero?"

    def _process_casual_conversation(self, text: str) -> str:
        """Procesa conversación casual"""
        if any(greeting in text for greeting in ["hola", "buenos días", "buenas tardes", "buenas noches"]):
            hour = datetime.now().hour
            prolog_greeting = self.prolog_engine.get_appropriate_greeting(hour)
            return f"{prolog_greeting}, señor Ibero. ¿En qué puedo ayudarle?"

        elif "cómo estás" in text or "como estas" in text:
            prolog_info = self.prolog_engine.get_system_info()
            if prolog_info["prolog_available"]:
                return "Estoy funcionando perfectamente, señor Ibero. Todos mis sistemas, incluyendo mi motor de razonamiento lógico, están operativos."
            else:
                return "Estoy funcionando bien, señor Ibero, aunque mi motor de razonamiento avanzado está limitado."

        else:
            return "Es un placer conversar con usted, señor Ibero."

    def _process_action_request(self, text: str) -> str:
        """Procesa solicitudes de acción"""
        if "hamilton ayuda" in text:
            return self._get_help_message_with_prolog()

        elif "analiza" in text or "razona" in text:
            return "Estoy analizando su solicitud usando mi motor de razonamiento lógico..."

        else:
            return "¿Qué acción específica desea que realice, señor Ibero?"

    def _process_general_command(self, text: str) -> str:
        """Procesa comandos generales"""
        # Determinar si la información debe ser recordada
        should_remember = self.prolog_engine.should_remember_information(text)
        if should_remember:
            logger.info(f"Información marcada para recordar: {text}")

        return f"Entiendo que me dice: {text}. Estoy procesando su solicitud con mi motor de razonamiento avanzado."

    def _process_science_query(self, text: str) -> str:
        """Procesa consultas científicas usando conocimiento de Prolog"""
        # Verificar si Hamilton puede ayudar con el tema
        science_topics = ["matematicas", "fisica", "quimica", "historia"]
        topic = None

        for science in science_topics:
            if science in text:
                topic = science
                break

        if topic:
            # Consultar Prolog sobre capacidades
            query = f"puede_ayudar_con(hamilton, {topic})"
            results = self.prolog_engine.query(query)

            if results:
                return f"Por supuesto, señor Ibero. Puedo ayudarle con {topic}. ¿Qué aspecto específico le interesa?"
            else:
                return f"Puedo intentar ayudarle con {topic}, aunque mis conocimientos en esta área están en desarrollo."

        return "¿Sobre qué tema científico específico desea información?"
    
    def _get_help_message(self) -> str:
        """Retorna mensaje de ayuda básico"""
        return """Soy Hamilton, su asistente personal. Puedo ayudarle con:
        - Información de fecha y hora
        - Conversación básica
        - Comandos de sistema
        Para dormir, diga 'Hamilton duerme' o 'Hamilton adiós'."""

    def _get_help_message_with_prolog(self) -> str:
        """Retorna mensaje de ayuda avanzado con capacidades Prolog"""
        prolog_info = self.prolog_engine.get_system_info()

        basic_help = """Soy Hamilton, su asistente personal con razonamiento avanzado. Puedo ayudarle con:
        - Información de fecha y hora
        - Conversación inteligente y contextual
        - Razonamiento lógico y toma de decisiones
        - Análisis de patrones y preferencias"""

        if prolog_info["prolog_available"]:
            advanced_help = """
        - Consultas científicas (matemáticas, física, química, historia)
        - Inferencia lógica y deducción
        - Aprendizaje de preferencias personales
        - Análisis de contexto conversacional"""
            basic_help += advanced_help

        basic_help += "\nPara dormir, diga 'Hamilton duerme' o 'Hamilton adiós'."
        return basic_help

    def _get_system_info(self) -> str:
        """Retorna información básica del sistema"""
        uptime = ""
        if self.session_start_time:
            delta = datetime.now() - self.session_start_time
            uptime = f"Tiempo activo: {delta.seconds // 60} minutos. "

        return f"Hamilton versión {settings.VERSION}. {uptime}Todos los sistemas funcionando correctamente."

    def _get_system_info_with_prolog(self) -> str:
        """Retorna información completa del sistema incluyendo Prolog"""
        uptime = ""
        if self.session_start_time:
            delta = datetime.now() - self.session_start_time
            uptime = f"Tiempo activo: {delta.seconds // 60} minutos. "

        prolog_info = self.prolog_engine.get_system_info()

        base_info = f"Hamilton versión {settings.VERSION}. {uptime}"

        if prolog_info["prolog_available"]:
            prolog_status = f"""
Motor de razonamiento: Activo
Base de conocimientos: {'Cargada' if prolog_info['knowledge_base_loaded'] else 'No cargada'}
Hechos en memoria: ~{prolog_info['facts_count']}
Preferencias de usuario: {'Cargadas' if prolog_info['user_preferences_loaded'] else 'No cargadas'}"""
            base_info += prolog_status
        else:
            base_info += "\nMotor de razonamiento: Limitado (Prolog no disponible)"

        # Agregar información de aprendizaje
        if self.current_user:
            try:
                preferences = self.database.get_user_preferences(self.current_user)
                patterns = self.learning_engine.analyze_conversation_patterns(self.current_user, days_back=7)

                learning_info = f"""
Sistema de aprendizaje: Activo
Preferencias aprendidas: {len(preferences)}
Patrones identificados: {len(patterns.get('topic_patterns', {}).get('favorite_topics', {}))} temas favoritos"""
                base_info += learning_info
            except Exception as e:
                logger.warning(f"Error obteniendo información de aprendizaje: {e}")

        base_info += "\nTodos los sistemas principales funcionando correctamente."
        return base_info
    
    def _add_to_conversation(self, speaker: str, text: str):
        """Agrega entrada al historial de conversación"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "speaker": speaker,
            "text": text
        }
        self.conversation_history.append(entry)
        
        # Mantener solo las últimas 100 entradas
        if len(self.conversation_history) > 100:
            self.conversation_history = self.conversation_history[-100:]
    
    def _save_session_data(self):
        """Guarda datos de la sesión actual"""
        if not self.session_start_time:
            return
        
        try:
            session_data = {
                "user": self.current_user,
                "start_time": self.session_start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "conversation_history": self.conversation_history,
                "learning_data": self.learning_data
            }
            
            # Guardar en archivo (implementar base de datos más adelante)
            session_file = f"./storage/conversations/session_{int(time.time())}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Datos de sesión guardados: {session_file}")
            
        except Exception as e:
            logger.error(f"Error guardando datos de sesión: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Retorna el estado actual de Hamilton incluyendo información de Prolog"""
        prolog_info = self.prolog_engine.get_system_info()

        return {
            "is_active": self.is_active,
            "is_authenticated": self.is_authenticated,
            "current_user": self.current_user,
            "status": self.status,
            "session_start_time": self.session_start_time.isoformat() if self.session_start_time else None,
            "conversation_entries": len(self.conversation_history),
            "prolog_engine": {
                "available": prolog_info["prolog_available"],
                "knowledge_base_loaded": prolog_info["knowledge_base_loaded"],
                "facts_count": prolog_info["facts_count"],
                "user_preferences_loaded": prolog_info["user_preferences_loaded"]
            }
        }

# Instancia global de Hamilton
hamilton = HamiltonCore()
