#!/usr/bin/env python3
"""
Hamilton AI Assistant - OpenAI API Setup Script
Script para configurar y verificar la API de OpenAI
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any
import subprocess

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenAISetup:
    """Configurador de OpenAI API para Hamilton"""
    
    def __init__(self):
        self.env_file = Path(".env")
        self.config_file = Path("config/openai_config.json")
        
    def setup_api_key(self):
        """Configura la API key de OpenAI"""
        print("🔑 CONFIGURACIÓN DE OPENAI API")
        print("=" * 40)
        
        # Verificar si ya existe
        current_key = self._get_current_api_key()
        if current_key and self._verify_api_key(current_key):
            print("✅ API key de OpenAI ya configurada y válida")
            return True
        
        print("\n📝 Para obtener tu API key:")
        print("1. Ve a: https://platform.openai.com/api-keys")
        print("2. Inicia sesión en tu cuenta OpenAI")
        print("3. Crea una nueva API key")
        print("4. Copia la key (empieza con 'sk-')")
        
        while True:
            api_key = input("\n🔑 Ingresa tu OpenAI API key: ").strip()
            
            if not api_key:
                print("❌ API key no puede estar vacía")
                continue
            
            if not api_key.startswith("sk-"):
                print("❌ API key debe empezar con 'sk-'")
                continue
            
            # Verificar API key
            print("🔍 Verificando API key...")
            if self._verify_api_key(api_key):
                self._save_api_key(api_key)
                print("✅ API key configurada exitosamente")
                return True
            else:
                print("❌ API key inválida o sin acceso")
                retry = input("¿Intentar con otra key? (s/n): ").lower()
                if retry != 's':
                    return False
    
    def _get_current_api_key(self) -> str:
        """Obtiene la API key actual"""
        # Verificar variable de entorno
        env_key = os.getenv("OPENAI_API_KEY")
        if env_key:
            return env_key
        
        # Verificar archivo .env
        if self.env_file.exists():
            with open(self.env_file, 'r') as f:
                for line in f:
                    if line.startswith("OPENAI_API_KEY="):
                        return line.split("=", 1)[1].strip().strip('"\'')
        
        return ""
    
    def _verify_api_key(self, api_key: str) -> bool:
        """Verifica que la API key sea válida"""
        try:
            import openai
            client = openai.OpenAI(api_key=api_key)
            
            # Hacer una llamada simple para verificar
            response = client.models.list()
            return True
            
        except ImportError:
            logger.warning("OpenAI library no instalada, instalando...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "openai>=1.3.0"
                ], check=True, capture_output=True)
                return self._verify_api_key(api_key)  # Reintentar
            except Exception as e:
                logger.error(f"Error instalando OpenAI: {e}")
                return False
        except Exception as e:
            logger.error(f"Error verificando API key: {e}")
            return False
    
    def _save_api_key(self, api_key: str):
        """Guarda la API key en .env"""
        env_content = []
        
        # Leer contenido existente
        if self.env_file.exists():
            with open(self.env_file, 'r') as f:
                env_content = f.readlines()
        
        # Actualizar o agregar OPENAI_API_KEY
        key_found = False
        for i, line in enumerate(env_content):
            if line.startswith("OPENAI_API_KEY="):
                env_content[i] = f"OPENAI_API_KEY={api_key}\n"
                key_found = True
                break
        
        if not key_found:
            env_content.append(f"OPENAI_API_KEY={api_key}\n")
        
        # Escribir archivo
        with open(self.env_file, 'w') as f:
            f.writelines(env_content)
    
    def configure_models(self):
        """Configura los modelos de OpenAI a usar"""
        print("\n🤖 CONFIGURACIÓN DE MODELOS")
        print("=" * 30)
        
        models_config = {
            "primary_model": "gpt-4",
            "fallback_model": "gpt-3.5-turbo",
            "embedding_model": "text-embedding-ada-002",
            "max_tokens": 1000,
            "temperature": 0.7,
            "hamilton_personality": {
                "role": "assistant",
                "personality": "Eres Hamilton, un asistente personal de IA masculino, formal pero amigable, diseñado específicamente para ayudar al señor Ibero. Respondes de manera precisa, útil y con un tono profesional pero cálido.",
                "language": "es-ES",
                "voice_style": "masculine, professional"
            }
        }
        
        print("📋 Modelos disponibles:")
        print("1. GPT-4 (Recomendado - Más inteligente)")
        print("2. GPT-3.5-turbo (Más rápido y económico)")
        
        choice = input("\n¿Qué modelo prefieres como principal? (1/2): ").strip()
        
        if choice == "2":
            models_config["primary_model"] = "gpt-3.5-turbo"
            models_config["fallback_model"] = "gpt-4"
        
        # Guardar configuración
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(models_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Configuración guardada en {self.config_file}")
        return True
    
    def test_integration(self):
        """Prueba la integración completa"""
        print("\n🧪 PROBANDO INTEGRACIÓN")
        print("=" * 25)
        
        try:
            # Importar motor GPT de Hamilton
            sys.path.insert(0, str(Path.cwd()))
            from ai.gpt_engine import hamilton_gpt
            
            # Probar consulta simple
            print("🔄 Enviando consulta de prueba...")
            response = hamilton_gpt.process_query(
                "Hola Hamilton, ¿puedes confirmar que la integración GPT funciona?"
            )
            
            if response and "error" not in response.lower():
                print("✅ Integración GPT funcionando correctamente")
                print(f"📝 Respuesta: {response[:100]}...")
                return True
            else:
                print(f"❌ Error en respuesta: {response}")
                return False
                
        except Exception as e:
            print(f"❌ Error probando integración: {e}")
            return False
    
    def setup_usage_monitoring(self):
        """Configura monitoreo de uso de API"""
        print("\n📊 CONFIGURANDO MONITOREO DE USO")
        print("=" * 35)
        
        monitoring_config = {
            "daily_limit_usd": 10.0,
            "monthly_limit_usd": 100.0,
            "alert_threshold": 0.8,
            "log_all_requests": True,
            "cost_tracking": True
        }
        
        print("💰 Límites de gasto recomendados:")
        print(f"- Diario: ${monitoring_config['daily_limit_usd']}")
        print(f"- Mensual: ${monitoring_config['monthly_limit_usd']}")
        
        custom = input("\n¿Quieres personalizar los límites? (s/n): ").lower()
        
        if custom == 's':
            try:
                daily = float(input("Límite diario en USD: "))
                monthly = float(input("Límite mensual en USD: "))
                monitoring_config["daily_limit_usd"] = daily
                monitoring_config["monthly_limit_usd"] = monthly
            except ValueError:
                print("⚠️ Valores inválidos, usando valores por defecto")
        
        # Guardar configuración de monitoreo
        monitoring_file = Path("config/openai_monitoring.json")
        with open(monitoring_file, 'w') as f:
            json.dump(monitoring_config, f, indent=2)
        
        print(f"✅ Monitoreo configurado en {monitoring_file}")
        return True

def main():
    """Función principal"""
    print("🚀 HAMILTON - CONFIGURADOR DE OPENAI API")
    print("=" * 50)
    
    setup = OpenAISetup()
    
    # Paso 1: Configurar API key
    if not setup.setup_api_key():
        print("❌ No se pudo configurar la API key")
        return False
    
    # Paso 2: Configurar modelos
    if not setup.configure_models():
        print("❌ Error configurando modelos")
        return False
    
    # Paso 3: Configurar monitoreo
    if not setup.setup_usage_monitoring():
        print("❌ Error configurando monitoreo")
        return False
    
    # Paso 4: Probar integración
    if not setup.test_integration():
        print("⚠️ Configuración guardada pero hay problemas con la integración")
        return False
    
    print("\n🎉 ¡CONFIGURACIÓN DE OPENAI COMPLETADA!")
    print("Hamilton ahora tiene capacidades GPT avanzadas.")
    print("\n📋 Próximos pasos:")
    print("1. Reinicia Hamilton para cargar la nueva configuración")
    print("2. Prueba comandos como: 'Hamilton, explícame algo complejo'")
    print("3. Monitorea el uso en: https://platform.openai.com/usage")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
