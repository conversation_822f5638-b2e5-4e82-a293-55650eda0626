"""
Hamilton AI Assistant - Home Automation System
Sistema de automatización inteligente del hogar
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, time, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from integrations.iot_controller import hamilton_iot, IoTDevice
from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class TriggerType(Enum):
    """Tipos de disparadores para automatización"""
    TIME = "time"
    VOICE_COMMAND = "voice_command"
    DEVICE_STATE = "device_state"
    USER_PRESENCE = "user_presence"
    WEATHER = "weather"
    MANUAL = "manual"

class ActionType(Enum):
    """Tipos de acciones de automatización"""
    DEVICE_CONTROL = "device_control"
    SCENE_ACTIVATION = "scene_activation"
    NOTIFICATION = "notification"
    VOICE_RESPONSE = "voice_response"

@dataclass
class AutomationTrigger:
    """Disparador de automatización"""
    type: TriggerType
    conditions: Dict[str, Any]
    enabled: bool = True

@dataclass
class AutomationAction:
    """Acción de automatización"""
    type: ActionType
    target: str
    parameters: Dict[str, Any]
    delay_seconds: int = 0

@dataclass
class AutomationRule:
    """Regla de automatización completa"""
    id: str
    name: str
    description: str
    triggers: List[AutomationTrigger]
    actions: List[AutomationAction]
    enabled: bool = True
    created_at: datetime = None
    last_executed: Optional[datetime] = None
    execution_count: int = 0

class HomeAutomationSystem:
    """Sistema principal de automatización del hogar"""
    
    def __init__(self):
        self.rules: Dict[str, AutomationRule] = {}
        self.scenes: Dict[str, Dict[str, Any]] = {}
        self.schedules: Dict[str, Any] = {}
        self.config_file = Path("config/automation_config.json")
        self.running_tasks: List[asyncio.Task] = []
        self.load_configuration()
        self._setup_default_automations()
    
    def load_configuration(self):
        """Carga configuración de automatización"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # self._load_rules_from_config(config)  # Comentado temporalmente
                    # self._load_scenes_from_config(config)  # Comentado temporalmente
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de automatización: {e}")
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        default_config = {
            "rules": [],
            "scenes": {
                "modo_noche": {
                    "name": "Modo Noche",
                    "description": "Configuración para dormir",
                    "actions": [
                        {"type": "device_control", "target": "all_lights", "command": "turn_off"},
                        {"type": "device_control", "target": "thermostat", "command": "set_temperature", "value": 20},
                        {"type": "voice_response", "message": "Modo noche activado. Buenas noches, señor Ibero."}
                    ]
                },
                "modo_trabajo": {
                    "name": "Modo Trabajo",
                    "description": "Configuración para trabajar",
                    "actions": [
                        {"type": "device_control", "target": "estudio_lights", "command": "turn_on", "brightness": 90},
                        {"type": "device_control", "target": "thermostat", "command": "set_temperature", "value": 22},
                        {"type": "voice_response", "message": "Modo trabajo activado. Ambiente optimizado para productividad."}
                    ]
                },
                "llegada_casa": {
                    "name": "Llegada a Casa",
                    "description": "Bienvenida automática",
                    "actions": [
                        {"type": "device_control", "target": "sala_lights", "command": "turn_on", "brightness": 70},
                        {"type": "device_control", "target": "thermostat", "command": "set_temperature", "value": 23},
                        {"type": "voice_response", "message": "Bienvenido a casa, señor Ibero. Hamilton a su servicio."}
                    ]
                }
            },
            "schedules": {
                "rutina_matutina": {
                    "time": "07:00",
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                    "scene": "modo_trabajo"
                },
                "rutina_nocturna": {
                    "time": "22:30",
                    "days": ["sunday", "monday", "tuesday", "wednesday", "thursday"],
                    "scene": "modo_noche"
                }
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        self.scenes = default_config["scenes"]
        self.schedules = default_config["schedules"]
    
    def _setup_default_automations(self):
        """Configura automatizaciones por defecto"""
        # Automatización de rutina matutina
        morning_rule = AutomationRule(
            id="morning_routine",
            name="Rutina Matutina",
            description="Activación automática por las mañanas",
            triggers=[
                AutomationTrigger(
                    type=TriggerType.TIME,
                    conditions={"time": "07:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}
                )
            ],
            actions=[
                AutomationAction(
                    type=ActionType.SCENE_ACTIVATION,
                    target="modo_trabajo",
                    parameters={}
                )
            ],
            created_at=datetime.now()
        )
        
        # Automatización de rutina nocturna
        night_rule = AutomationRule(
            id="night_routine",
            name="Rutina Nocturna",
            description="Activación automática por las noches",
            triggers=[
                AutomationTrigger(
                    type=TriggerType.TIME,
                    conditions={"time": "22:30", "days": ["sunday", "monday", "tuesday", "wednesday", "thursday"]}
                )
            ],
            actions=[
                AutomationAction(
                    type=ActionType.SCENE_ACTIVATION,
                    target="modo_noche",
                    parameters={}
                )
            ],
            created_at=datetime.now()
        )
        
        # Automatización por comando de voz
        voice_automation_rule = AutomationRule(
            id="voice_commands",
            name="Comandos de Voz",
            description="Respuesta a comandos de voz específicos",
            triggers=[
                AutomationTrigger(
                    type=TriggerType.VOICE_COMMAND,
                    conditions={"patterns": ["modo noche", "modo trabajo", "llegué a casa", "me voy"]}
                )
            ],
            actions=[
                AutomationAction(
                    type=ActionType.SCENE_ACTIVATION,
                    target="dynamic",  # Se determina según el comando
                    parameters={}
                )
            ],
            created_at=datetime.now()
        )
        
        self.rules.update({
            "morning_routine": morning_rule,
            "night_routine": night_rule,
            "voice_commands": voice_automation_rule
        })
    
    async def process_voice_command(self, command: str, user: str = "senor_ibero") -> Optional[str]:
        """Procesa comando de voz para automatización"""
        command_lower = command.lower()
        
        # Mapeo de comandos a escenas
        command_scene_map = {
            "modo noche": "modo_noche",
            "modo trabajo": "modo_trabajo",
            "llegué a casa": "llegada_casa",
            "llegue a casa": "llegada_casa",
            "estoy en casa": "llegada_casa",
            "modo dormir": "modo_noche",
            "modo descanso": "modo_noche",
            "modo productivo": "modo_trabajo",
            "modo oficina": "modo_trabajo"
        }
        
        # Comandos directos de dispositivos
        device_commands = {
            "enciende las luces": ("all_lights", "turn_on", {}),
            "apaga las luces": ("all_lights", "turn_off", {}),
            "enciende la luz de la sala": ("sala_lights", "turn_on", {}),
            "apaga la luz de la sala": ("sala_lights", "turn_off", {}),
            "sube la temperatura": ("thermostat", "set_temperature", {"value": 24}),
            "baja la temperatura": ("thermostat", "set_temperature", {"value": 20}),
            "enciende la calefacción": ("thermostat", "set_mode", {"value": "heat"}),
            "apaga la calefacción": ("thermostat", "set_mode", {"value": "off"})
        }
        
        # Buscar comando de escena
        for pattern, scene_id in command_scene_map.items():
            if pattern in command_lower:
                success = await self.activate_scene(scene_id)
                if success:
                    scene = self.scenes.get(scene_id, {})
                    return f"{scene.get('name', 'Escena')} activada correctamente."
                else:
                    return f"Error activando {scene.get('name', 'escena')}."
        
        # Buscar comando directo de dispositivo
        for pattern, (target, command_type, params) in device_commands.items():
            if pattern in command_lower:
                success = await self._execute_device_command(target, command_type, params)
                if success:
                    return f"Comando ejecutado: {pattern}"
                else:
                    return f"Error ejecutando: {pattern}"
        
        # Comandos de información
        if any(word in command_lower for word in ["estado", "status", "información"]):
            if "casa" in command_lower or "hogar" in command_lower:
                return await self._get_home_status()
        
        return None  # No se reconoció el comando
    
    async def activate_scene(self, scene_id: str) -> bool:
        """Activa una escena específica"""
        if scene_id not in self.scenes:
            logger.error(f"Escena no encontrada: {scene_id}")
            # Crear escena básica si no existe
            if scene_id == "modo_noche":
                self.scenes[scene_id] = {
                    "name": "Modo Noche",
                    "actions": [
                        {"type": "voice_response", "message": "Modo noche activado"}
                    ]
                }
            else:
                return False

        scene = self.scenes[scene_id]
        logger.info(f"🎬 Activando escena: {scene.get('name', scene_id)}")

        try:
            for action in scene.get("actions", []):
                await self._execute_scene_action(action)

            logger.info(f"✅ Escena {scene_id} activada exitosamente")
            return True

        except Exception as e:
            logger.error(f"Error activando escena {scene_id}: {e}")
            return False
    
    async def _execute_scene_action(self, action: Dict[str, Any]):
        """Ejecuta una acción de escena"""
        action_type = action.get("type")
        
        if action_type == "device_control":
            target = action.get("target")
            command = action.get("command")
            params = {k: v for k, v in action.items() if k not in ["type", "target", "command"]}
            
            await self._execute_device_command(target, command, params)
            
        elif action_type == "voice_response":
            message = action.get("message", "Acción completada")
            logger.info(f"🔊 Respuesta de voz: {message}")
            # Aquí se integraría con el sistema de síntesis de voz de Hamilton
            
        elif action_type == "notification":
            message = action.get("message", "Notificación de automatización")
            logger.info(f"📱 Notificación: {message}")
    
    async def _execute_device_command(self, target: str, command: str, parameters: Dict) -> bool:
        """Ejecuta comando en dispositivo(s)"""
        try:
            if target == "all_lights":
                # Controlar todas las luces
                light_devices = [d for d in hamilton_iot.devices.values() if d.type == "light"]
                results = []
                for device in light_devices:
                    result = await hamilton_iot.control_device(device.id, command, parameters)
                    results.append(result)
                return all(results)
                
            elif target.endswith("_lights"):
                # Controlar luces de una habitación específica
                room = target.replace("_lights", "")
                results = await hamilton_iot.control_room(room, command, parameters)
                return all(results.values())
                
            elif target == "thermostat":
                # Controlar termostato
                thermostat_devices = [d for d in hamilton_iot.devices.values() if d.type == "thermostat"]
                if thermostat_devices:
                    return await hamilton_iot.control_device(thermostat_devices[0].id, command, parameters)
                
            else:
                # Controlar dispositivo específico por ID
                return await hamilton_iot.control_device(target, command, parameters)
                
        except Exception as e:
            logger.error(f"Error ejecutando comando de dispositivo: {e}")
            return False
        
        return False
    
    async def _get_home_status(self) -> str:
        """Obtiene estado general de la casa"""
        try:
            total_devices = len(hamilton_iot.devices)
            online_devices = len([d for d in hamilton_iot.devices.values() if d.status == "online"])
            
            lights_on = len([d for d in hamilton_iot.devices.values() 
                           if d.type == "light" and d.current_state.get("power", False)])
            total_lights = len([d for d in hamilton_iot.devices.values() if d.type == "light"])
            
            # Obtener temperatura si hay termostato
            temperature = None
            thermostat_devices = [d for d in hamilton_iot.devices.values() if d.type == "thermostat"]
            if thermostat_devices:
                temperature = thermostat_devices[0].current_state.get("temperature")
            
            status_parts = [
                f"Estado de la casa: {online_devices}/{total_devices} dispositivos en línea",
                f"Iluminación: {lights_on}/{total_lights} luces encendidas"
            ]
            
            if temperature:
                status_parts.append(f"Temperatura: {temperature}°C")
            
            return ". ".join(status_parts) + "."
            
        except Exception as e:
            logger.error(f"Error obteniendo estado de la casa: {e}")
            return "Error obteniendo estado de la casa."
    
    async def create_custom_scene(self, scene_id: str, name: str, actions: List[Dict]) -> bool:
        """Crea una escena personalizada"""
        try:
            self.scenes[scene_id] = {
                "name": name,
                "description": f"Escena personalizada creada por {settings.AUTHORIZED_USER}",
                "actions": actions,
                "created_at": datetime.now().isoformat()
            }
            
            await self._save_configuration()
            logger.info(f"✅ Escena personalizada creada: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creando escena personalizada: {e}")
            return False
    
    async def _save_configuration(self):
        """Guarda configuración actual"""
        try:
            config = {
                "rules": [asdict(rule) for rule in self.rules.values()],
                "scenes": self.scenes,
                "schedules": self.schedules
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"Error guardando configuración: {e}")
    
    def get_available_scenes(self) -> Dict[str, str]:
        """Obtiene lista de escenas disponibles"""
        return {scene_id: scene.get("name", scene_id) for scene_id, scene in self.scenes.items()}
    
    def get_automation_status(self) -> Dict[str, Any]:
        """Obtiene estado del sistema de automatización"""
        return {
            "total_rules": len(self.rules),
            "active_rules": len([r for r in self.rules.values() if r.enabled]),
            "total_scenes": len(self.scenes),
            "available_scenes": self.get_available_scenes(),
            "last_execution": max([r.last_executed for r in self.rules.values() if r.last_executed], default=None)
        }

# Instancia global
hamilton_automation = HomeAutomationSystem()
