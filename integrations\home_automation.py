"""
Hamilton AI Assistant - Home Automation System
Sistema de automatización inteligente del hogar
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, time, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from integrations.iot_controller import hamilton_iot, IoTDevice
from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class TriggerType(Enum):
    """Tipos de disparadores para automatización"""
    TIME = "time"
    VOICE_COMMAND = "voice_command"
    DEVICE_STATE = "device_state"
    USER_PRESENCE = "user_presence"
    WEATHER = "weather"
    MANUAL = "manual"

class ActionType(Enum):
    """Tipos de acciones de automatización"""
    DEVICE_CONTROL = "device_control"
    SCENE_ACTIVATION = "scene_activation"
    NOTIFICATION = "notification"
    VOICE_RESPONSE = "voice_response"

@dataclass
class AutomationTrigger:
    """Disparador de automatización"""
    type: TriggerType
    conditions: Dict[str, Any]
    enabled: bool = True

@dataclass
class AutomationAction:
    """Acción de automatización"""
    type: ActionType
    target: str
    parameters: Dict[str, Any]
    delay_seconds: int = 0

@dataclass
class AutomationRule:
    """Regla de automatización completa"""
    id: str
    name: str
    description: str
    triggers: List[AutomationTrigger]
    actions: List[AutomationAction]
    enabled: bool = True
    created_at: datetime = None
    last_executed: Optional[datetime] = None
    execution_count: int = 0

class HomeAutomationSystem:
    """Sistema principal de automatización del hogar"""
    
    def __init__(self):
        self.rules: Dict[str, AutomationRule] = {}
        self.scenes: Dict[str, Dict[str, Any]] = {}
        self.schedules: Dict[str, Any] = {}
        self.config_file = Path("config/automation_config.json")
        self.running_tasks: List[asyncio.Task] = []
        self.load_configuration()
        self._setup_default_automations()
    
    def load_configuration(self):
        """Carga configuración de automatización"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # self._load_rules_from_config(config)  # Comentado temporalmente
                    # self._load_scenes_from_config(config)  # Comentado temporalmente
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración de automatización: {e}")
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        default_config = {
            "rules": [],
            "scenes": {
                "modo_noche": {
                    "name": "Modo Noche",
                    "description": "Configuración para dormir",
                    "actions": [
                        {"type": "device_control", "target": "all_lights", "command": "turn_off"},
                        {"type": "device_control", "target": "thermostat", "command": "set_temperature", "value": 20},
                        {"type": "voice_response", "message": "Modo noche activado. Buenas noches, señor Ibero."}
                    ]
                },
                "modo_trabajo": {
                    "name": "Modo Trabajo",
                    "description": "Configuración para trabajar",
                    "actions": [
                        {"type": "device_control", "target": "estudio_lights", "command": "turn_on", "brightness": 90},
                        {"type": "device_control", "target": "thermostat", "command": "set_temperature", "value": 22},
                        {"type": "voice_response", "message": "Modo trabajo activado. Ambiente optimizado para productividad."}
                    ]
                },
                "llegada_casa": {
                    "name": "Llegada a Casa",
                    "description": "Bienvenida automática",
                    "actions": [
                        {"type": "device_control", "target": "sala_lights", "command": "turn_on", "brightness": 70},
                        {"type": "device_control", "target": "thermostat", "command": "set_temperature", "value": 23},
                        {"type": "voice_response", "message": "Bienvenido a casa, señor Ibero. Hamilton a su servicio."}
                    ]
                }
            },
            "schedules": {
                "rutina_matutina": {
                    "time": "07:00",
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                    "scene": "modo_trabajo"
                },
                "rutina_nocturna": {
                    "time": "22:30",
                    "days": ["sunday", "monday", "tuesday", "wednesday", "thursday"],
                    "scene": "modo_noche"
                }
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        self.scenes = default_config["scenes"]
        self.schedules = default_config["schedules"]
    
    def _setup_default_automations(self):
        """Configura automatizaciones por defecto"""
        # Automatización de rutina matutina
        morning_rule = AutomationRule(
            id="morning_routine",
            name="Rutina Matutina",
            description="Activación automática por las mañanas",
            triggers=[
                AutomationTrigger(
                    type=TriggerType.TIME,
                    conditions={"time": "07:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}
                )
            ],
            actions=[
                AutomationAction(
                    type=ActionType.SCENE_ACTIVATION,
                    target="modo_trabajo",
                    parameters={}
                )
            ],
            created_at=datetime.now()
        )
        
        # Automatización de rutina nocturna
        night_rule = AutomationRule(
            id="night_routine",
            name="Rutina Nocturna",
            description="Activación automática por las noches",
            triggers=[
                AutomationTrigger(
                    type=TriggerType.TIME,
                    conditions={"time": "22:30", "days": ["sunday", "monday", "tuesday", "wednesday", "thursday"]}
                )
            ],
            actions=[
                AutomationAction(
                    type=ActionType.SCENE_ACTIVATION,
                    target="modo_noche",
                    parameters={}
                )
            ],
            created_at=datetime.now()
        )
        
        # Automatización por comando de voz
        voice_automation_rule = AutomationRule(
            id="voice_commands",
            name="Comandos de Voz",
            description="Respuesta a comandos de voz específicos",
            triggers=[
                AutomationTrigger(
                    type=TriggerType.VOICE_COMMAND,
                    conditions={"patterns": ["modo noche", "modo trabajo", "llegué a casa", "me voy"]}
                )
            ],
            actions=[
                AutomationAction(
                    type=ActionType.SCENE_ACTIVATION,
                    target="dynamic",  # Se determina según el comando
                    parameters={}
                )
            ],
            created_at=datetime.now()
        )
        
        self.rules.update({
            "morning_routine": morning_rule,
            "night_routine": night_rule,
            "voice_commands": voice_automation_rule
        })
    
    async def process_voice_command(self, command: str, user: str = "senor_ibero") -> Optional[str]:
        """Procesa comando de voz para automatización REAL"""
        command_lower = command.lower()

        # Análisis inteligente de comandos
        analysis = self._analyze_command_intent(command_lower)

        # Procesar según tipo de comando
        if analysis['type'] == 'scene_activation':
            return await self._handle_scene_command(analysis, command_lower)
        elif analysis['type'] == 'device_control':
            return await self._handle_device_command(analysis, command_lower)
        elif analysis['type'] == 'room_control':
            return await self._handle_room_command(analysis, command_lower)
        elif analysis['type'] == 'information_request':
            return await self._handle_information_request(analysis, command_lower)
        elif analysis['type'] == 'schedule_command':
            return await self._handle_schedule_command(analysis, command_lower)
        else:
            return await self._handle_unknown_command(command_lower)

    def _analyze_command_intent(self, command: str) -> Dict[str, Any]:
        """Analiza la intención del comando de manera inteligente"""
        analysis = {
            'type': 'unknown',
            'confidence': 0.0,
            'entities': {},
            'parameters': {}
        }

        # Patrones de escenas
        scene_patterns = {
            'modo_noche': ['modo noche', 'modo dormir', 'modo descanso', 'hora de dormir', 'buenas noches'],
            'modo_trabajo': ['modo trabajo', 'modo productivo', 'modo oficina', 'empezar a trabajar'],
            'llegada_casa': ['llegué a casa', 'llegue a casa', 'estoy en casa', 'ya llegué'],
            'modo_lectura': ['modo lectura', 'modo leer', 'quiero leer'],
            'modo_entretenimiento': ['modo entretenimiento', 'modo tv', 'ver televisión', 'modo película'],
            'modo_relajacion': ['modo relajación', 'modo relax', 'quiero relajarme']
        }

        # Detectar escenas
        for scene_id, patterns in scene_patterns.items():
            for pattern in patterns:
                if pattern in command:
                    analysis['type'] = 'scene_activation'
                    analysis['entities']['scene'] = scene_id
                    analysis['confidence'] = 0.9
                    return analysis

        # Patrones de control de dispositivos
        device_patterns = {
            'lights': ['luz', 'luces', 'lámpara', 'iluminación'],
            'thermostat': ['temperatura', 'calefacción', 'aire', 'termostato'],
            'tv': ['televisión', 'tv', 'tele'],
            'music': ['música', 'audio', 'sonido']
        }

        action_patterns = {
            'turn_on': ['enciende', 'prende', 'activa', 'abre'],
            'turn_off': ['apaga', 'cierra', 'desactiva'],
            'increase': ['sube', 'aumenta', 'más'],
            'decrease': ['baja', 'disminuye', 'menos']
        }

        # Detectar control de dispositivos
        for device_type, device_words in device_patterns.items():
            if any(word in command for word in device_words):
                for action, action_words in action_patterns.items():
                    if any(word in command for word in action_words):
                        analysis['type'] = 'device_control'
                        analysis['entities']['device'] = device_type
                        analysis['entities']['action'] = action
                        analysis['confidence'] = 0.8

                        # Extraer parámetros adicionales
                        if device_type == 'thermostat':
                            temp_match = self._extract_temperature(command)
                            if temp_match:
                                analysis['parameters']['temperature'] = temp_match

                        return analysis

        # Patrones de control por habitación
        room_patterns = {
            'sala': ['sala', 'living', 'salón'],
            'dormitorio': ['dormitorio', 'cuarto', 'habitación', 'recámara'],
            'cocina': ['cocina'],
            'baño': ['baño'],
            'estudio': ['estudio', 'oficina']
        }

        for room, room_words in room_patterns.items():
            if any(word in command for word in room_words):
                for action, action_words in action_patterns.items():
                    if any(word in command for word in action_words):
                        analysis['type'] = 'room_control'
                        analysis['entities']['room'] = room
                        analysis['entities']['action'] = action
                        analysis['confidence'] = 0.7
                        return analysis

        # Patrones de información
        info_patterns = ['estado', 'status', 'información', 'cómo está', 'qué tal']
        if any(pattern in command for pattern in info_patterns):
            analysis['type'] = 'information_request'
            analysis['confidence'] = 0.6

            if any(word in command for word in ['casa', 'hogar', 'dispositivos']):
                analysis['entities']['scope'] = 'home'
            elif any(word in command for word in ['temperatura', 'clima']):
                analysis['entities']['scope'] = 'climate'

        # Patrones de programación
        schedule_patterns = ['programa', 'configura', 'establece', 'cada', 'todos los días']
        if any(pattern in command for pattern in schedule_patterns):
            analysis['type'] = 'schedule_command'
            analysis['confidence'] = 0.5

        return analysis

    def _extract_temperature(self, command: str) -> Optional[int]:
        """Extrae temperatura del comando"""
        import re
        temp_match = re.search(r'(\d+)\s*grados?', command)
        if temp_match:
            return int(temp_match.group(1))

        # Temperaturas comunes
        temp_words = {
            'fría': 18, 'frío': 18,
            'fresca': 20, 'fresco': 20,
            'normal': 22, 'cómoda': 22,
            'cálida': 24, 'caliente': 26
        }

        for word, temp in temp_words.items():
            if word in command:
                return temp

        return None

    async def _handle_scene_command(self, analysis: Dict, command: str) -> str:
        """Maneja comandos de activación de escenas"""
        scene_id = analysis['entities']['scene']
        success = await self.activate_scene(scene_id)

        if success:
            scene = self.scenes.get(scene_id, {})
            scene_name = scene.get('name', scene_id.replace('_', ' ').title())
            return f"✅ {scene_name} activada correctamente."
        else:
            return f"❌ Error activando la escena {scene_id}."

    async def _handle_device_command(self, analysis: Dict, command: str) -> str:
        """Maneja comandos de control de dispositivos"""
        device_type = analysis['entities']['device']
        action = analysis['entities']['action']
        parameters = analysis.get('parameters', {})

        # Mapear tipos de dispositivos a targets
        device_targets = {
            'lights': 'all_lights',
            'thermostat': 'thermostat',
            'tv': 'television',
            'music': 'audio_system'
        }

        target = device_targets.get(device_type, device_type)

        # Mapear acciones a comandos
        action_commands = {
            'turn_on': 'turn_on',
            'turn_off': 'turn_off',
            'increase': 'increase',
            'decrease': 'decrease'
        }

        command_type = action_commands.get(action, action)

        # Parámetros específicos por tipo de dispositivo
        if device_type == 'thermostat' and 'temperature' in parameters:
            command_type = 'set_temperature'
            parameters = {'value': parameters['temperature']}

        success = await self._execute_device_command(target, command_type, parameters)

        if success:
            device_name = device_type.replace('_', ' ').title()
            action_name = action.replace('_', ' ')
            return f"✅ {device_name}: {action_name} ejecutado correctamente."
        else:
            return f"❌ Error ejecutando comando en {device_type}."

    async def _handle_room_command(self, analysis: Dict, command: str) -> str:
        """Maneja comandos de control por habitación"""
        room = analysis['entities']['room']
        action = analysis['entities']['action']

        # Obtener dispositivos de la habitación
        room_devices = await self._get_room_devices(room)

        if not room_devices:
            return f"❌ No se encontraron dispositivos en {room}."

        results = []
        for device_id in room_devices:
            success = await hamilton_iot.control_device(device_id, action, {})
            results.append(success)

        successful = sum(results)
        total = len(results)

        if successful == total:
            return f"✅ Todos los dispositivos de {room} controlados correctamente."
        elif successful > 0:
            return f"⚠️ {successful}/{total} dispositivos de {room} controlados."
        else:
            return f"❌ Error controlando dispositivos de {room}."

    async def _handle_information_request(self, analysis: Dict, command: str) -> str:
        """Maneja solicitudes de información"""
        scope = analysis['entities'].get('scope', 'general')

        if scope == 'home':
            return await self._get_home_status()
        elif scope == 'climate':
            return await self._get_climate_status()
        else:
            return await self._get_general_status()

    async def _handle_schedule_command(self, analysis: Dict, command: str) -> str:
        """Maneja comandos de programación"""
        return "🕒 Función de programación en desarrollo. Por ahora puede usar las escenas predefinidas."

    async def _handle_unknown_command(self, command: str) -> str:
        """Maneja comandos no reconocidos"""
        # Intentar sugerir comandos similares
        suggestions = self._get_command_suggestions(command)

        if suggestions:
            return f"❓ No reconocí ese comando. ¿Quiso decir: {', '.join(suggestions)}?"
        else:
            return "❓ No reconocí ese comando de automatización. Intente con 'modo noche', 'enciende las luces' o 'estado de la casa'."

    def _get_command_suggestions(self, command: str) -> List[str]:
        """Sugiere comandos similares"""
        common_commands = [
            "modo noche", "modo trabajo", "llegué a casa",
            "enciende las luces", "apaga las luces",
            "sube la temperatura", "estado de la casa"
        ]

        # Buscar comandos similares (implementación simple)
        suggestions = []
        for cmd in common_commands:
            if any(word in command for word in cmd.split() if len(word) > 3):
                suggestions.append(cmd)

        return suggestions[:3]  # Máximo 3 sugerencias

    async def _get_room_devices(self, room: str) -> List[str]:
        """Obtiene dispositivos de una habitación específica"""
        room_devices = []

        for device_id, device in hamilton_iot.devices.items():
            if device.room and device.room.lower() == room.lower():
                room_devices.append(device_id)

        return room_devices

    async def _get_climate_status(self) -> str:
        """Obtiene estado del clima/temperatura"""
        try:
            thermostat_devices = [d for d in hamilton_iot.devices.values() if d.type == "thermostat"]

            if thermostat_devices:
                device = thermostat_devices[0]
                temp = device.current_state.get("temperature", "N/A")
                mode = device.current_state.get("mode", "N/A")
                return f"🌡️ Temperatura actual: {temp}°C, Modo: {mode}"
            else:
                return "🌡️ No hay dispositivos de clima configurados."

        except Exception as e:
            logger.error(f"Error obteniendo estado del clima: {e}")
            return "❌ Error obteniendo información del clima."

    async def _get_general_status(self) -> str:
        """Obtiene estado general del sistema"""
        try:
            total_devices = len(hamilton_iot.devices)
            online_devices = len([d for d in hamilton_iot.devices.values() if d.status == "online"])
            active_scenes = len([s for s in self.scenes.values() if s.get("active", False)])

            return f"🏠 Sistema: {online_devices}/{total_devices} dispositivos en línea, {active_scenes} escenas activas."

        except Exception as e:
            logger.error(f"Error obteniendo estado general: {e}")
            return "❌ Error obteniendo estado del sistema."
    
    async def activate_scene(self, scene_id: str) -> bool:
        """Activa una escena específica"""
        if scene_id not in self.scenes:
            logger.error(f"Escena no encontrada: {scene_id}")
            # Crear escena básica si no existe
            if scene_id == "modo_noche":
                self.scenes[scene_id] = {
                    "name": "Modo Noche",
                    "actions": [
                        {"type": "voice_response", "message": "Modo noche activado"}
                    ]
                }
            else:
                return False

        scene = self.scenes[scene_id]
        logger.info(f"🎬 Activando escena: {scene.get('name', scene_id)}")

        try:
            for action in scene.get("actions", []):
                await self._execute_scene_action(action)

            logger.info(f"✅ Escena {scene_id} activada exitosamente")
            return True

        except Exception as e:
            logger.error(f"Error activando escena {scene_id}: {e}")
            return False
    
    async def _execute_scene_action(self, action: Dict[str, Any]):
        """Ejecuta una acción de escena"""
        action_type = action.get("type")
        
        if action_type == "device_control":
            target = action.get("target")
            command = action.get("command")
            params = {k: v for k, v in action.items() if k not in ["type", "target", "command"]}
            
            await self._execute_device_command(target, command, params)
            
        elif action_type == "voice_response":
            message = action.get("message", "Acción completada")
            logger.info(f"🔊 Respuesta de voz: {message}")
            # Aquí se integraría con el sistema de síntesis de voz de Hamilton
            
        elif action_type == "notification":
            message = action.get("message", "Notificación de automatización")
            logger.info(f"📱 Notificación: {message}")
    
    async def _execute_device_command(self, target: str, command: str, parameters: Dict) -> bool:
        """Ejecuta comando en dispositivo(s)"""
        try:
            if target == "all_lights":
                # Controlar todas las luces
                light_devices = [d for d in hamilton_iot.devices.values() if d.type == "light"]
                results = []
                for device in light_devices:
                    result = await hamilton_iot.control_device(device.id, command, parameters)
                    results.append(result)
                return all(results)
                
            elif target.endswith("_lights"):
                # Controlar luces de una habitación específica
                room = target.replace("_lights", "")
                results = await hamilton_iot.control_room(room, command, parameters)
                return all(results.values())
                
            elif target == "thermostat":
                # Controlar termostato
                thermostat_devices = [d for d in hamilton_iot.devices.values() if d.type == "thermostat"]
                if thermostat_devices:
                    return await hamilton_iot.control_device(thermostat_devices[0].id, command, parameters)
                
            else:
                # Controlar dispositivo específico por ID
                return await hamilton_iot.control_device(target, command, parameters)
                
        except Exception as e:
            logger.error(f"Error ejecutando comando de dispositivo: {e}")
            return False
        
        return False
    
    async def _get_home_status(self) -> str:
        """Obtiene estado general de la casa"""
        try:
            total_devices = len(hamilton_iot.devices)
            online_devices = len([d for d in hamilton_iot.devices.values() if d.status == "online"])
            
            lights_on = len([d for d in hamilton_iot.devices.values() 
                           if d.type == "light" and d.current_state.get("power", False)])
            total_lights = len([d for d in hamilton_iot.devices.values() if d.type == "light"])
            
            # Obtener temperatura si hay termostato
            temperature = None
            thermostat_devices = [d for d in hamilton_iot.devices.values() if d.type == "thermostat"]
            if thermostat_devices:
                temperature = thermostat_devices[0].current_state.get("temperature")
            
            status_parts = [
                f"Estado de la casa: {online_devices}/{total_devices} dispositivos en línea",
                f"Iluminación: {lights_on}/{total_lights} luces encendidas"
            ]
            
            if temperature:
                status_parts.append(f"Temperatura: {temperature}°C")
            
            return ". ".join(status_parts) + "."
            
        except Exception as e:
            logger.error(f"Error obteniendo estado de la casa: {e}")
            return "Error obteniendo estado de la casa."
    
    async def create_custom_scene(self, scene_id: str, name: str, actions: List[Dict]) -> bool:
        """Crea una escena personalizada"""
        try:
            self.scenes[scene_id] = {
                "name": name,
                "description": f"Escena personalizada creada por {settings.AUTHORIZED_USER}",
                "actions": actions,
                "created_at": datetime.now().isoformat()
            }
            
            await self._save_configuration()
            logger.info(f"✅ Escena personalizada creada: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creando escena personalizada: {e}")
            return False
    
    async def _save_configuration(self):
        """Guarda configuración actual"""
        try:
            config = {
                "rules": [asdict(rule) for rule in self.rules.values()],
                "scenes": self.scenes,
                "schedules": self.schedules
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"Error guardando configuración: {e}")
    
    def get_available_scenes(self) -> Dict[str, str]:
        """Obtiene lista de escenas disponibles"""
        return {scene_id: scene.get("name", scene_id) for scene_id, scene in self.scenes.items()}
    
    def get_automation_status(self) -> Dict[str, Any]:
        """Obtiene estado del sistema de automatización"""
        return {
            "total_rules": len(self.rules),
            "active_rules": len([r for r in self.rules.values() if r.enabled]),
            "total_scenes": len(self.scenes),
            "available_scenes": self.get_available_scenes(),
            "last_execution": max([r.last_executed for r in self.rules.values() if r.last_executed], default=None)
        }

# Instancia global
hamilton_automation = HomeAutomationSystem()
