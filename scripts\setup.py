import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

def setup_logging():
    """Configura el sistema de logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/setup.log')
        ]
    )
    return logging.getLogger(__name__)

def create_directories():
    """Crea los directorios necesarios para el sistema"""
    directories = [
        'logs',
        'models',
        'models/local_model',
        'storage',
        'storage/conversations',
        'storage/learning_data',
        'uploads',
        'uploads/temp',
        'config',
        'tests',
        'tests/data',
        'interfaces/templates',
        'interfaces/static',
        'interfaces/static/css',
        'interfaces/static/js',
        'interfaces/static/img'
    ]
    
    logger = setup_logging()
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            try:
                path.mkdir(parents=True, exist_ok=True)
                logger.info(f"Directorio creado: {directory}")
            except Exception as e:
                logger.error(f"Error creando directorio {directory}: {e}")
        else:
            logger.info(f"Directorio ya existe: {directory}")

def verify_environment():
    """Verifica la configuración del entorno"""
    logger = setup_logging()
    
    # Cargar variables de entorno
    load_dotenv()
    
    # Verificar API keys
    required_keys = {
        'OPENAI_API_KEY': 'OpenAI',
        'ANTHROPIC_API_KEY': 'Anthropic',
        'HUGGINGFACE_TOKEN': 'Hugging Face'
    }
    
    missing_keys = []
    for key, service in required_keys.items():
        if not os.getenv(key):
            missing_keys.append(f"{service} ({key})")
    
    if missing_keys:
        logger.warning("API keys faltantes:")
        for key in missing_keys:
            logger.warning(f"- {key}")
    else:
        logger.info("Todas las API keys están configuradas")
    
    # Verificar directorios
    create_directories()
    
    # Verificar permisos
    try:
        test_file = Path('logs/test.log')
        test_file.touch()
        test_file.unlink()
        logger.info("Permisos de escritura verificados correctamente")
    except Exception as e:
        logger.error(f"Error verificando permisos: {e}")

def main():
    """Función principal de configuración"""
    logger = setup_logging()
    logger.info("Iniciando configuración del sistema...")
    
    try:
        verify_environment()
        logger.info("Configuración completada exitosamente")
    except Exception as e:
        logger.error(f"Error durante la configuración: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 