{"openai": {"api_key": "YOUR_OPENAI_API_KEY_HERE", "model": "gpt-4", "max_tokens": 1000, "temperature": 0.7, "enabled": false}, "google_translate": {"api_key": "YOUR_GOOGLE_TRANSLATE_API_KEY_HERE", "project_id": "YOUR_PROJECT_ID", "enabled": false}, "deepl": {"api_key": "YOUR_DEEPL_API_KEY_HERE", "enabled": false}, "azure_translator": {"api_key": "YOUR_AZURE_TRANSLATOR_KEY_HERE", "region": "YOUR_AZURE_REGION", "endpoint": "https://api.cognitive.microsofttranslator.com", "enabled": false}, "elevenlabs": {"api_key": "YOUR_ELEVENLABS_API_KEY_HERE", "voice_id": "YOUR_PREFERRED_VOICE_ID", "enabled": false}, "weather": {"openweathermap_api_key": "YOUR_OPENWEATHERMAP_API_KEY_HERE", "enabled": false}, "calendar_integrations": {"google_calendar": {"client_id": "YOUR_GOOGLE_CLIENT_ID", "client_secret": "YOUR_GOOGLE_CLIENT_SECRET", "enabled": false}, "outlook": {"client_id": "YOUR_OUTLOOK_CLIENT_ID", "client_secret": "YOUR_OUTLOOK_CLIENT_SECRET", "enabled": false}}, "iot_integrations": {"philips_hue": {"bridge_ip": "YOUR_HUE_BRIDGE_IP", "username": "YOUR_HUE_USERNAME", "enabled": false}, "lifx": {"api_token": "YOUR_LIFX_API_TOKEN", "enabled": false}, "nest": {"client_id": "YOUR_NEST_CLIENT_ID", "client_secret": "YOUR_NEST_CLIENT_SECRET", "enabled": false}}}