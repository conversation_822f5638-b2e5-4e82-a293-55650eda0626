"""
Hamilton AI Assistant - API Manager
Gestor centralizado de APIs externas
"""

import logging
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import requests

# Configurar logging
logger = logging.getLogger(__name__)

class APIManager:
    """Gestor centralizado de APIs externas"""
    
    def __init__(self):
        self.config_file = Path("config/api_keys.json")
        self.apis = {}
        self.load_api_configuration()
    
    def load_api_configuration(self):
        """Carga configuración de APIs"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.apis = json.load(f)
                logger.info("✅ Configuración de APIs cargada")
            else:
                logger.warning("⚠️ Archivo de configuración de APIs no encontrado")
                self.apis = {}
        except Exception as e:
            logger.error(f"Error cargando configuración de APIs: {e}")
            self.apis = {}
    
    def is_api_enabled(self, api_name: str) -> bool:
        """Verifica si una API está habilitada"""
        return self.apis.get(api_name, {}).get("enabled", False)
    
    def get_api_config(self, api_name: str) -> Dict[str, Any]:
        """Obtiene configuración de una API específica"""
        return self.apis.get(api_name, {})
    
    async def call_openai_api(self, prompt: str, model: str = None) -> Optional[str]:
        """Llama a la API de OpenAI"""
        if not self.is_api_enabled("openai"):
            logger.warning("OpenAI API no está habilitada")
            return None
        
        config = self.get_api_config("openai")
        api_key = config.get("api_key")
        
        if not api_key or api_key == "YOUR_OPENAI_API_KEY_HERE":
            logger.warning("OpenAI API key no configurada")
            return None
        
        try:
            import openai
            openai.api_key = api_key
            
            response = openai.ChatCompletion.create(
                model=model or config.get("model", "gpt-3.5-turbo"),
                messages=[
                    {"role": "system", "content": "Eres Hamilton, un asistente personal de IA masculino, formal pero amigable."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=config.get("max_tokens", 1000),
                temperature=config.get("temperature", 0.7)
            )
            
            return response.choices[0].message.content
            
        except ImportError:
            logger.warning("Librería openai no instalada. Instalar con: pip install openai")
            return None
        except Exception as e:
            logger.error(f"Error llamando OpenAI API: {e}")
            return None
    
    async def translate_text(self, text: str, target_language: str, source_language: str = "auto") -> Optional[str]:
        """Traduce texto usando servicios disponibles"""
        # Intentar con Google Translate
        if self.is_api_enabled("google_translate"):
            result = await self._translate_with_google(text, target_language, source_language)
            if result:
                return result
        
        # Intentar con DeepL
        if self.is_api_enabled("deepl"):
            result = await self._translate_with_deepl(text, target_language, source_language)
            if result:
                return result
        
        # Intentar con Azure Translator
        if self.is_api_enabled("azure_translator"):
            result = await self._translate_with_azure(text, target_language, source_language)
            if result:
                return result
        
        logger.warning("No hay servicios de traducción disponibles")
        return None
    
    async def _translate_with_google(self, text: str, target_lang: str, source_lang: str) -> Optional[str]:
        """Traduce usando Google Translate API"""
        try:
            from google.cloud import translate_v2 as translate
            
            config = self.get_api_config("google_translate")
            
            # Configurar credenciales
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = config.get("credentials_path", "")
            
            translate_client = translate.Client()
            
            result = translate_client.translate(
                text,
                target_language=target_lang,
                source_language=source_lang if source_lang != "auto" else None
            )
            
            return result['translatedText']
            
        except ImportError:
            logger.warning("Google Cloud Translate no instalado")
            return None
        except Exception as e:
            logger.error(f"Error con Google Translate: {e}")
            return None
    
    async def _translate_with_deepl(self, text: str, target_lang: str, source_lang: str) -> Optional[str]:
        """Traduce usando DeepL API"""
        try:
            config = self.get_api_config("deepl")
            api_key = config.get("api_key")
            
            if not api_key or api_key == "YOUR_DEEPL_API_KEY_HERE":
                return None
            
            url = "https://api-free.deepl.com/v2/translate"
            
            data = {
                'auth_key': api_key,
                'text': text,
                'target_lang': target_lang.upper()
            }
            
            if source_lang != "auto":
                data['source_lang'] = source_lang.upper()
            
            response = requests.post(url, data=data)
            
            if response.status_code == 200:
                result = response.json()
                return result['translations'][0]['text']
            
            return None
            
        except Exception as e:
            logger.error(f"Error con DeepL: {e}")
            return None
    
    async def _translate_with_azure(self, text: str, target_lang: str, source_lang: str) -> Optional[str]:
        """Traduce usando Azure Translator"""
        try:
            config = self.get_api_config("azure_translator")
            api_key = config.get("api_key")
            region = config.get("region")
            endpoint = config.get("endpoint")
            
            if not api_key or api_key == "YOUR_AZURE_TRANSLATOR_KEY_HERE":
                return None
            
            url = f"{endpoint}/translate"
            
            headers = {
                'Ocp-Apim-Subscription-Key': api_key,
                'Ocp-Apim-Subscription-Region': region,
                'Content-type': 'application/json'
            }
            
            params = {
                'api-version': '3.0',
                'to': target_lang
            }
            
            if source_lang != "auto":
                params['from'] = source_lang
            
            body = [{'text': text}]
            
            response = requests.post(url, params=params, headers=headers, json=body)
            
            if response.status_code == 200:
                result = response.json()
                return result[0]['translations'][0]['text']
            
            return None
            
        except Exception as e:
            logger.error(f"Error con Azure Translator: {e}")
            return None
    
    async def get_weather_info(self, location: str) -> Optional[Dict[str, Any]]:
        """Obtiene información del clima"""
        if not self.is_api_enabled("weather"):
            logger.warning("API de clima no habilitada")
            return None
        
        config = self.get_api_config("weather")
        api_key = config.get("openweathermap_api_key")
        
        if not api_key or api_key == "YOUR_OPENWEATHERMAP_API_KEY_HERE":
            logger.warning("API key de OpenWeatherMap no configurada")
            return None
        
        try:
            url = f"http://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': location,
                'appid': api_key,
                'units': 'metric',
                'lang': 'es'
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'temperature': data['main']['temp'],
                    'description': data['weather'][0]['description'],
                    'humidity': data['main']['humidity'],
                    'pressure': data['main']['pressure'],
                    'wind_speed': data['wind']['speed']
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error obteniendo información del clima: {e}")
            return None
    
    async def generate_voice_with_elevenlabs(self, text: str) -> Optional[bytes]:
        """Genera voz usando ElevenLabs"""
        if not self.is_api_enabled("elevenlabs"):
            logger.warning("ElevenLabs API no habilitada")
            return None
        
        config = self.get_api_config("elevenlabs")
        api_key = config.get("api_key")
        voice_id = config.get("voice_id")
        
        if not api_key or api_key == "YOUR_ELEVENLABS_API_KEY_HERE":
            logger.warning("ElevenLabs API key no configurada")
            return None
        
        try:
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5
                }
            }
            
            response = requests.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                return response.content
            
            return None
            
        except Exception as e:
            logger.error(f"Error con ElevenLabs: {e}")
            return None
    
    def setup_api_key(self, api_name: str, api_key: str, **additional_config):
        """Configura una API key"""
        if api_name not in self.apis:
            self.apis[api_name] = {}
        
        self.apis[api_name]["api_key"] = api_key
        self.apis[api_name]["enabled"] = True
        
        # Agregar configuración adicional
        for key, value in additional_config.items():
            self.apis[api_name][key] = value
        
        # Guardar configuración
        self._save_configuration()
        
        logger.info(f"✅ API {api_name} configurada exitosamente")
    
    def _save_configuration(self):
        """Guarda configuración de APIs"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.apis, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error guardando configuración de APIs: {e}")
    
    def get_available_apis(self) -> Dict[str, bool]:
        """Obtiene lista de APIs disponibles"""
        return {
            api_name: config.get("enabled", False)
            for api_name, config in self.apis.items()
        }

# Instancia global
hamilton_api_manager = APIManager()
