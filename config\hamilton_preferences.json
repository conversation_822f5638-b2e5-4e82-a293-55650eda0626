{"user_profile": {"name": "<PERSON><PERSON><PERSON>", "preferred_name": "señor <PERSON>", "language": "es", "timezone": "America/Mexico_City", "location": "México", "formality_level": "high"}, "voice_settings": {"enabled": true, "gender": "masculine", "language": "es-MX", "rate": 180, "volume": 0.9, "voice_engine": "pyttsx3", "backup_voice_service": "elevenlabs", "wake_words": ["hamilton", "hey hamilton", "oye hamilton"]}, "response_preferences": {"style": "professional_warm", "verbosity": "detailed", "include_explanations": true, "use_examples": true, "proactive_suggestions": true, "context_awareness": "high"}, "automation_preferences": {"home_automation": {"enabled": true, "auto_discovery": true, "preferred_scenes": ["modo_trabajo", "modo_noche", "llegada_casa", "modo_lectura", "modo_entretenimiento"], "rooms": {"sala": {"devices": ["luces", "television", "aire_acondicionado"], "default_lighting": 70, "default_temperature": 23}, "dormitorio": {"devices": ["luces", "aire_acondicionado"], "default_lighting": 30, "default_temperature": 20}, "estudio": {"devices": ["luces", "computadora", "aire_acondicionado"], "default_lighting": 90, "default_temperature": 22}, "cocina": {"devices": ["luces"], "default_lighting": 80}}}, "calendar_automation": {"enabled": true, "auto_reminders": true, "reminder_times": [15, 60, 1440], "work_hours": {"start": "09:00", "end": "18:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}, "break_reminders": true, "break_interval_minutes": 90}}, "learning_preferences": {"adaptive_responses": true, "remember_preferences": true, "learn_patterns": true, "feedback_learning": true, "privacy_mode": false, "data_retention_days": 365}, "security_preferences": {"face_recognition": {"enabled": true, "confidence_threshold": 0.6, "require_for_sensitive": true}, "voice_recognition": {"enabled": true, "confidence_threshold": 0.7}, "session_timeout_minutes": 60, "auto_lock_sensitive": true}, "notification_preferences": {"voice_notifications": true, "visual_notifications": false, "email_notifications": false, "priority_levels": {"urgent": "immediate", "high": "within_5_minutes", "medium": "within_30_minutes", "low": "next_interaction"}}, "integration_preferences": {"weather": {"enabled": true, "default_location": "Ciudad de México", "units": "metric", "include_in_morning_briefing": true}, "news": {"enabled": false, "sources": ["bbc", "reuters"], "categories": ["technology", "science", "business"], "language": "es"}, "music": {"enabled": false, "preferred_service": "spotify", "default_volume": 0.6}}, "advanced_features": {"local_ai_models": {"enabled": true, "preferred_model": "llama2_7b", "fallback_to_cloud": true}, "emotion_recognition": {"enabled": true, "facial_analysis": true, "voice_analysis": true, "adapt_responses": true}, "predictive_assistance": {"enabled": true, "confidence_threshold": 0.7, "proactive_suggestions": true}, "multilingual_support": {"enabled": true, "auto_detect": true, "auto_translate": true, "supported_languages": ["es", "en", "fr", "pt"]}}, "development_settings": {"debug_mode": false, "verbose_logging": false, "experimental_features": false, "beta_testing": false}}