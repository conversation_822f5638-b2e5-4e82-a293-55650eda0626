# 🤖 Hamilton - Asistente Personal de IA

Hamilton es un asistente de inteligencia artificial altamente avanzado, diseñado específicamente para el señor Ibero. Cuenta con reconocimiento facial y de voz, síntesis de voz masculina, y capacidades de aprendizaje continuo.

## 🎯 Características Principales

- **Reconocimiento Facial**: Autenticación segura usando OpenCV y DeepFace
- **Reconocimiento de Voz**: Procesamiento de comandos de voz en español
- **Síntesis de Voz Masculina**: Hamilton habla con voz varonil distintiva
- **Autenticación Segura**: Solo responde al señor Ibero autenticado
- **Aprendizaje Continuo**: Mejora con cada interacción
- **Multiplataforma**: Compatible con Windows, macOS y Linux

## 🚀 Instalación Rápida

### Prerrequisitos

- Python 3.8 o superior
- Cámara web funcional
- Micrófono
- Altavoces o auriculares

### 1. <PERSON>lonar el Repositorio

```bash
git clone <repository-url>
cd Hamilton
```

### 2. <PERSON><PERSON><PERSON>

```bash
python -m venv hamilton_env
# Windows
hamilton_env\Scripts\activate
# macOS/Linux
source hamilton_env/bin/activate
```

### 3. Instalar Dependencias

```bash
pip install -r requirements.txt
```

### 4. Configurar Variables de Entorno

```bash
cp .env.example .env
# Editar .env con tus configuraciones
```

### 5. Registrar Cara del Usuario

```bash
python register_face.py
```

### 6. Ejecutar Hamilton

```bash
python main.py
```

## 🎮 Uso Básico

### Activación

1. **Ejecutar Hamilton**: `python main.py`
2. **Palabra de Activación**: Diga "Hamilton" o "Hey Hamilton"
3. **Autenticación**: Hamilton activará la cámara para reconocimiento facial
4. **Interacción**: Una vez autenticado, puede dar comandos de voz

### Comandos de Voz

- **Activación**: "Hamilton", "Hey Hamilton"
- **Saludo**: "Hola Hamilton"
- **Hora**: "¿Qué hora es?"
- **Fecha**: "¿Qué día es?"
- **Ayuda**: "Hamilton ayuda"
- **Información**: "Hamilton información"
- **Dormir**: "Hamilton duerme", "Hamilton adiós"

### Ejemplo de Interacción

```
Usuario: "Hamilton"
Hamilton: "Iniciando reconocimiento facial. Por favor, mire a la cámara."
[Autenticación exitosa]
Hamilton: "Buenos días, señor Ibero. Soy Hamilton, su asistente personal. ¿En qué puedo asistirle hoy?"

Usuario: "¿Qué hora es?"
Hamilton: "Son las 14:30, señor Ibero."

Usuario: "Hamilton duerme"
Hamilton: "Hasta luego, señor Ibero. Hamilton entrando en modo dormido."
```

## ⚙️ Configuración Avanzada

### Configuración de Voz

En `config/settings.py`, puede ajustar:

```python
# Configuración de síntesis de voz (TTS) - VOZ MASCULINA
TTS_ENGINE: str = "pyttsx3"
TTS_VOICE_GENDER: str = "male"
TTS_VOICE_RATE: int = 180
TTS_VOICE_VOLUME: float = 0.9
```

### Configuración de Reconocimiento Facial

```python
# Configuración de reconocimiento facial
FACE_RECOGNITION_TOLERANCE: float = 0.6
FACE_DETECTION_MODEL: str = "hog"  # hog o cnn
AUTHORIZED_USER: str = "señor_ibero"
```

### Variables de Entorno (.env)

```env
# OpenAI Configuration (opcional)
OPENAI_API_KEY=tu_openai_api_key_aqui

# Voice Configuration - IMPORTANTE: Voz masculina
TTS_ENGINE=pyttsx3
TTS_VOICE_GENDER=male
TTS_AZURE_VOICE=es-ES-AlvaroNeural
TTS_GOOGLE_VOICE=es-ES-Standard-B

# Security
SECRET_KEY=hamilton-secret-key-change-in-production

# Debug Mode
DEBUG=True
```

## 📁 Estructura del Proyecto

```
Hamilton/
├── config/
│   └── settings.py          # Configuraciones principales
├── core/
│   └── hamilton_core.py     # Núcleo principal de Hamilton
├── recognition/
│   ├── face_recognition.py  # Reconocimiento facial
│   └── voice_recognition.py # Reconocimiento y síntesis de voz
├── storage/
│   ├── faces/              # Datos de caras registradas
│   ├── voices/             # Datos de voz
│   └── conversations/      # Historial de conversaciones
├── logs/                   # Archivos de log
├── main.py                 # Punto de entrada principal
├── register_face.py        # Script de registro facial
├── requirements.txt        # Dependencias Python
└── README.md              # Este archivo
```

## 🔧 Solución de Problemas

### Error: "No se pudo abrir la cámara"

1. Verificar que la cámara esté conectada
2. Cerrar otras aplicaciones que usen la cámara
3. Verificar permisos de cámara en el sistema

### Error: "Motor TTS no disponible"

1. Reinstalar pyttsx3: `pip install --upgrade pyttsx3`
2. En Windows, instalar SAPI voices adicionales
3. Verificar configuración de audio del sistema

### Error: "No se encontró voz masculina"

1. Instalar voces adicionales del sistema
2. Configurar manualmente en `settings.py`
3. Usar motor TTS alternativo (Azure/Google)

### Problemas de Reconocimiento Facial

1. Mejorar iluminación
2. Registrar más muestras faciales
3. Ajustar `FACE_RECOGNITION_TOLERANCE`

## 🔒 Seguridad

- Solo el usuario registrado (señor Ibero) puede activar Hamilton
- Autenticación facial requerida en cada sesión
- Datos de conversación encriptados localmente
- Sin transmisión de datos biométricos a servidores externos

## 🚧 Desarrollo Futuro

### Próximas Características

- [ ] Integración con Prolog para razonamiento avanzado
- [ ] API REST para integración móvil
- [ ] Interfaz web moderna
- [ ] Procesamiento de lenguaje natural avanzado
- [ ] Integración con servicios de nube
- [ ] Soporte para múltiples idiomas
- [ ] Reconocimiento de emociones
- [ ] Integración con dispositivos IoT

### Tecnologías Planificadas

- **Backend**: Python, FastAPI, SQLAlchemy
- **Frontend**: React, TypeScript, WebRTC
- **IA**: OpenAI GPT-4, Transformers, LangChain
- **Base de Datos**: PostgreSQL, Redis, ChromaDB
- **Móvil**: React Native, Flutter
- **Lógica**: SWI-Prolog, PySwip

## 📞 Soporte

Para problemas técnicos o sugerencias:

1. Revisar la sección de solución de problemas
2. Verificar logs en `./logs/hamilton.log`
3. Contactar al desarrollador

## 📄 Licencia

Este proyecto es propiedad del señor Ibero y está desarrollado para uso personal.

---

**Hamilton v1.0.0** - "Su asistente personal de confianza"

🤖 *"Buenos días, señor Ibero. Hamilton a su servicio."*
