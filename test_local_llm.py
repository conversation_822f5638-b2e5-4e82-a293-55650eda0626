"""
Hamilton AI Assistant - Local LLM Test
Prueba del modelo de lenguaje local
"""

from ai.local_llm import HamiltonLocalLLM

def test_local_llm():
    """Prueba el modelo de lenguaje local"""
    # Crear instancia
    llm = HamiltonLocalLLM()
    
    # Probar saludo
    print("\nProbando saludo...")
    response = llm.process_query("<PERSON>la, ¿cómo estás?")
    print(f"Respuesta: {response}")
    
    # Probar despedida
    print("\nProbando despedida...")
    response = llm.process_query("Adiós, hasta luego")
    print(f"Respuesta: {response}")
    
    # Probar agradecimiento
    print("\nProbando agradecimiento...")
    response = llm.process_query("Muchas gracias por tu ayuda")
    print(f"Respuesta: {response}")
    
    # Probar consulta con contexto
    print("\nProbando consulta con contexto...")
    context = {
        "user_id": "test_user",
        "timestamp": "2024-03-20T12:00:00",
        "session_id": "test_session"
    }
    response = llm.process_query("¿Qué tiempo hace?", context)
    print(f"Respuesta con contexto: {response}")
    
    # Probar historial de conversación
    print("\nProbando historial de conversación...")
    print(f"Historial: {llm.conversation_history}")

if __name__ == "__main__":
    test_local_llm() 