#!/usr/bin/env python3
"""
<PERSON> AI Assistant - Voice Recognition Optimizer
<PERSON>ript para optimizar el reconocimiento de voz y configurar hardware
"""

import os
import sys
import json
import logging
import subprocess
import platform
from pathlib import Path
from typing import Dict, List, Any
import time

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceOptimizer:
    """Optimizador de reconocimiento de voz para Hamilton"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.config_file = Path("config/voice_optimization.json")
        self.audio_devices = []
        
    def detect_audio_hardware(self):
        """Detecta hardware de audio disponible"""
        print("🎤 DETECTANDO HARDWARE DE AUDIO")
        print("=" * 35)
        
        try:
            import pyaudio
            
            # Inicializar PyAudio
            p = pyaudio.PyAudio()
            
            print("📋 Dispositivos de audio detectados:")
            print("-" * 40)
            
            devices = []
            for i in range(p.get_device_count()):
                info = p.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:  # Solo dispositivos de entrada
                    devices.append({
                        'index': i,
                        'name': info['name'],
                        'channels': info['maxInputChannels'],
                        'sample_rate': int(info['defaultSampleRate']),
                        'api': p.get_host_api_info_by_index(info['hostApi'])['name']
                    })
                    
                    print(f"🎤 [{i}] {info['name']}")
                    print(f"    Canales: {info['maxInputChannels']}")
                    print(f"    Sample Rate: {int(info['defaultSampleRate'])} Hz")
                    print(f"    API: {p.get_host_api_info_by_index(info['hostApi'])['name']}")
                    print()
            
            p.terminate()
            self.audio_devices = devices
            
            if not devices:
                print("❌ No se encontraron dispositivos de entrada de audio")
                return False
            
            print(f"✅ {len(devices)} dispositivo(s) de audio encontrado(s)")
            return True
            
        except ImportError:
            print("⚠️ PyAudio no instalado, instalando...")
            return self._install_pyaudio()
        except Exception as e:
            print(f"❌ Error detectando hardware: {e}")
            return False
    
    def _install_pyaudio(self):
        """Instala PyAudio según el sistema operativo"""
        try:
            if self.system == "windows":
                # En Windows, usar pip
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "pyaudio"
                ], check=True)
            elif self.system == "linux":
                # En Linux, instalar dependencias primero
                subprocess.run([
                    "sudo", "apt", "install", "-y", "portaudio19-dev", "python3-pyaudio"
                ], check=True)
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "pyaudio"
                ], check=True)
            elif self.system == "darwin":  # macOS
                # En macOS, usar brew para portaudio
                subprocess.run(["brew", "install", "portaudio"], check=True)
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "pyaudio"
                ], check=True)
            
            print("✅ PyAudio instalado exitosamente")
            return self.detect_audio_hardware()  # Reintentar
            
        except Exception as e:
            print(f"❌ Error instalando PyAudio: {e}")
            return False
    
    def select_optimal_device(self):
        """Permite seleccionar el dispositivo óptimo"""
        if not self.audio_devices:
            print("❌ No hay dispositivos disponibles")
            return None
        
        print("\n🎯 SELECCIÓN DE DISPOSITIVO ÓPTIMO")
        print("=" * 35)
        
        # Recomendar dispositivo automáticamente
        recommended = self._recommend_device()
        
        if recommended:
            print(f"💡 Dispositivo recomendado: {recommended['name']}")
            print(f"   Razón: {recommended['reason']}")
            
            use_recommended = input("\n¿Usar dispositivo recomendado? (s/n): ").lower()
            if use_recommended == 's':
                return recommended['device']
        
        # Selección manual
        print("\n📋 Selecciona un dispositivo:")
        for i, device in enumerate(self.audio_devices):
            print(f"[{i+1}] {device['name']} ({device['channels']} canales, {device['sample_rate']} Hz)")
        
        while True:
            try:
                choice = int(input("\nSelecciona dispositivo (número): ")) - 1
                if 0 <= choice < len(self.audio_devices):
                    return self.audio_devices[choice]
                else:
                    print("❌ Selección inválida")
            except ValueError:
                print("❌ Ingresa un número válido")
    
    def _recommend_device(self):
        """Recomienda el mejor dispositivo basado en características"""
        if not self.audio_devices:
            return None
        
        # Criterios de recomendación
        best_device = None
        best_score = 0
        reason = ""
        
        for device in self.audio_devices:
            score = 0
            current_reason = []
            
            # Preferir dispositivos con mayor sample rate
            if device['sample_rate'] >= 44100:
                score += 3
                current_reason.append("alta calidad de audio")
            elif device['sample_rate'] >= 22050:
                score += 2
            
            # Preferir dispositivos con múltiples canales
            if device['channels'] >= 2:
                score += 2
                current_reason.append("estéreo")
            
            # Preferir ciertos tipos de dispositivos
            name_lower = device['name'].lower()
            if any(keyword in name_lower for keyword in ['usb', 'external', 'blue yeti', 'rode']):
                score += 3
                current_reason.append("micrófono profesional")
            elif 'headset' in name_lower or 'headphone' in name_lower:
                score += 2
                current_reason.append("headset")
            elif 'built-in' in name_lower or 'internal' in name_lower:
                score += 1
            
            if score > best_score:
                best_score = score
                best_device = device
                reason = ", ".join(current_reason) if current_reason else "configuración estándar"
        
        if best_device:
            return {
                'device': best_device,
                'reason': reason,
                'score': best_score
            }
        
        return None
    
    def test_audio_quality(self, device):
        """Prueba la calidad del audio del dispositivo seleccionado"""
        print(f"\n🧪 PROBANDO CALIDAD DE AUDIO")
        print(f"Dispositivo: {device['name']}")
        print("=" * 40)
        
        try:
            import pyaudio
            import numpy as np
            
            # Configuración de grabación
            chunk = 1024
            format = pyaudio.paInt16
            channels = min(device['channels'], 1)  # Usar mono para simplicidad
            rate = device['sample_rate']
            
            p = pyaudio.PyAudio()
            
            print("🎤 Grabando 3 segundos de audio para análisis...")
            print("   (Habla normalmente durante la grabación)")
            
            stream = p.open(
                format=format,
                channels=channels,
                rate=rate,
                input=True,
                input_device_index=device['index'],
                frames_per_buffer=chunk
            )
            
            frames = []
            for _ in range(0, int(rate / chunk * 3)):  # 3 segundos
                data = stream.read(chunk)
                frames.append(data)
            
            stream.stop_stream()
            stream.close()
            p.terminate()
            
            # Analizar calidad
            audio_data = b''.join(frames)
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Métricas básicas
            rms = np.sqrt(np.mean(audio_array**2))
            peak = np.max(np.abs(audio_array))
            snr_estimate = 20 * np.log10(rms / (np.std(audio_array) + 1e-10))
            
            print("\n📊 Análisis de calidad:")
            print(f"   RMS Level: {rms:.0f}")
            print(f"   Peak Level: {peak}")
            print(f"   SNR estimado: {snr_estimate:.1f} dB")
            
            # Evaluación
            quality_score = 0
            if rms > 1000:
                quality_score += 2
                print("   ✅ Nivel de señal bueno")
            elif rms > 500:
                quality_score += 1
                print("   ⚠️ Nivel de señal moderado")
            else:
                print("   ❌ Nivel de señal bajo")
            
            if snr_estimate > 20:
                quality_score += 2
                print("   ✅ Relación señal/ruido excelente")
            elif snr_estimate > 10:
                quality_score += 1
                print("   ⚠️ Relación señal/ruido aceptable")
            else:
                print("   ❌ Mucho ruido de fondo")
            
            return quality_score >= 2
            
        except Exception as e:
            print(f"❌ Error probando audio: {e}")
            return False
    
    def optimize_settings(self, device):
        """Optimiza configuraciones para el dispositivo seleccionado"""
        print("\n⚙️ OPTIMIZANDO CONFIGURACIONES")
        print("=" * 30)
        
        # Configuraciones optimizadas
        optimized_config = {
            "device_index": device['index'],
            "device_name": device['name'],
            "sample_rate": min(device['sample_rate'], 44100),  # Máximo 44.1kHz
            "channels": 1,  # Mono para reconocimiento de voz
            "chunk_size": 1024,
            "format": "int16",
            
            # Configuraciones de reconocimiento
            "energy_threshold": 4000,  # Ajustar según ruido ambiente
            "dynamic_energy_threshold": True,
            "pause_threshold": 0.8,
            "phrase_threshold": 0.3,
            "non_speaking_duration": 0.8,
            
            # Configuraciones avanzadas
            "noise_reduction": True,
            "auto_gain_control": True,
            "echo_cancellation": True,
            
            # Configuraciones específicas de Hamilton
            "wake_word_sensitivity": 0.7,
            "continuous_listening": True,
            "voice_activity_detection": True
        }
        
        # Ajustar según calidad detectada
        print("🔧 Aplicando optimizaciones específicas...")
        
        # Guardar configuración
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(optimized_config, f, indent=2)
        
        print(f"✅ Configuración optimizada guardada en {self.config_file}")
        
        # Actualizar configuración de Hamilton
        self._update_hamilton_voice_config(optimized_config)
        
        return True
    
    def _update_hamilton_voice_config(self, config):
        """Actualiza la configuración de voz de Hamilton"""
        try:
            # Actualizar archivo de configuración de voz
            voice_config_file = Path("config/voice_config.json")
            
            hamilton_voice_config = {
                "recognition": {
                    "device_index": config["device_index"],
                    "sample_rate": config["sample_rate"],
                    "chunk_size": config["chunk_size"],
                    "energy_threshold": config["energy_threshold"],
                    "pause_threshold": config["pause_threshold"],
                    "phrase_threshold": config["phrase_threshold"]
                },
                "synthesis": {
                    "engine": "pyttsx3",
                    "voice_gender": "male",
                    "rate": 180,
                    "volume": 0.9
                },
                "optimization": {
                    "noise_reduction": config["noise_reduction"],
                    "auto_gain_control": config["auto_gain_control"],
                    "echo_cancellation": config["echo_cancellation"]
                }
            }
            
            with open(voice_config_file, 'w') as f:
                json.dump(hamilton_voice_config, f, indent=2)
            
            print(f"✅ Configuración de Hamilton actualizada en {voice_config_file}")
            
        except Exception as e:
            print(f"⚠️ Error actualizando configuración de Hamilton: {e}")

def main():
    """Función principal"""
    print("🚀 HAMILTON - OPTIMIZADOR DE RECONOCIMIENTO DE VOZ")
    print("=" * 55)
    
    optimizer = VoiceOptimizer()
    
    # Paso 1: Detectar hardware
    if not optimizer.detect_audio_hardware():
        print("❌ No se pudo detectar hardware de audio")
        return False
    
    # Paso 2: Seleccionar dispositivo óptimo
    selected_device = optimizer.select_optimal_device()
    if not selected_device:
        print("❌ No se seleccionó ningún dispositivo")
        return False
    
    # Paso 3: Probar calidad
    print(f"\n🎤 Dispositivo seleccionado: {selected_device['name']}")
    if not optimizer.test_audio_quality(selected_device):
        print("⚠️ Calidad de audio subóptima, pero continuando...")
    
    # Paso 4: Optimizar configuraciones
    if not optimizer.optimize_settings(selected_device):
        print("❌ Error optimizando configuraciones")
        return False
    
    print("\n🎉 ¡OPTIMIZACIÓN DE VOZ COMPLETADA!")
    print("Hamilton ahora está optimizado para tu hardware de audio.")
    print("\n📋 Recomendaciones adicionales:")
    print("1. Usa un micrófono externo USB para mejor calidad")
    print("2. Minimiza ruido de fondo durante el uso")
    print("3. Habla claramente y a distancia constante del micrófono")
    print("4. Reinicia Hamilton para aplicar las nuevas configuraciones")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
