"""
Hamilton AI Assistant - Multilingual System
Sistema multiidioma avanzado con traducción y localización
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import re

from config.settings import settings

# Configurar logging
logger = logging.getLogger(__name__)

class Language(Enum):
    """Idiomas soportados"""
    SPANISH = "es"
    ENGLISH = "en"
    FRENCH = "fr"
    PORTUGUESE = "pt"
    ITALIAN = "it"
    GERMAN = "de"
    CHINESE = "zh"
    JAPANESE = "ja"

@dataclass
class LanguageProfile:
    """Perfil de idioma del usuario"""
    primary_language: Language
    secondary_languages: List[Language]
    voice_preference: str  # Voz específica para TTS
    formality_level: str  # formal, informal, neutral
    cultural_context: str  # País/región específica
    auto_translate: bool = True
    learning_mode: bool = False  # Para ayudar a aprender idiomas

class MultilingualSystem:
    """Sistema multiidioma principal"""
    
    def __init__(self):
        self.current_language = Language.SPANISH  # Idioma por defecto
        self.user_profiles: Dict[str, LanguageProfile] = {}
        self.translations: Dict[str, Dict[str, str]] = {}
        self.language_models = {}
        self.config_file = Path("config/multilingual_config.json")
        self.translations_dir = Path("data/translations")
        
        self.load_configuration()
        self.load_translations()
        self._setup_language_detection()
    
    def load_configuration(self):
        """Carga configuración multiidioma"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self._load_user_profiles(config)
            else:
                self._create_default_config()
        except Exception as e:
            logger.error(f"Error cargando configuración multiidioma: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        default_config = {
            "default_language": "es",
            "supported_languages": ["es", "en", "fr", "pt", "it", "de"],
            "translation_services": {
                "google_translate": {
                    "enabled": False,
                    "api_key": ""
                },
                "deepl": {
                    "enabled": False,
                    "api_key": ""
                },
                "azure_translator": {
                    "enabled": False,
                    "api_key": "",
                    "region": ""
                }
            },
            "voice_settings": {
                "es": {
                    "engine": "pyttsx3",
                    "voice_id": "spanish_male",
                    "rate": 180,
                    "volume": 0.9
                },
                "en": {
                    "engine": "pyttsx3",
                    "voice_id": "english_male",
                    "rate": 200,
                    "volume": 0.9
                },
                "fr": {
                    "engine": "pyttsx3",
                    "voice_id": "french_male",
                    "rate": 170,
                    "volume": 0.9
                }
            },
            "user_profiles": {
                "senor_ibero": {
                    "primary_language": "es",
                    "secondary_languages": ["en"],
                    "voice_preference": "spanish_male",
                    "formality_level": "formal",
                    "cultural_context": "mexico",
                    "auto_translate": True,
                    "learning_mode": False
                }
            },
            "cultural_adaptations": {
                "date_formats": {
                    "es": "DD/MM/YYYY",
                    "en": "MM/DD/YYYY",
                    "de": "DD.MM.YYYY"
                },
                "time_formats": {
                    "es": "HH:mm",
                    "en": "h:mm AM/PM",
                    "de": "HH:mm"
                },
                "currency_formats": {
                    "es": "$ {amount} MXN",
                    "en": "${amount} USD",
                    "de": "{amount} €"
                }
            }
        }
        
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        self.config = default_config
        self._load_user_profiles(default_config)
    
    def _load_user_profiles(self, config: Dict):
        """Carga perfiles de usuario"""
        for username, profile_data in config.get("user_profiles", {}).items():
            profile = LanguageProfile(
                primary_language=Language(profile_data["primary_language"]),
                secondary_languages=[Language(lang) for lang in profile_data.get("secondary_languages", [])],
                voice_preference=profile_data.get("voice_preference", "spanish_male"),
                formality_level=profile_data.get("formality_level", "formal"),
                cultural_context=profile_data.get("cultural_context", "mexico"),
                auto_translate=profile_data.get("auto_translate", True),
                learning_mode=profile_data.get("learning_mode", False)
            )
            self.user_profiles[username] = profile
    
    def load_translations(self):
        """Carga archivos de traducción"""
        self.translations_dir.mkdir(exist_ok=True)
        
        # Cargar traducciones básicas
        basic_translations = {
            "greetings": {
                "es": {
                    "good_morning": "Buenos días",
                    "good_afternoon": "Buenas tardes",
                    "good_evening": "Buenas noches",
                    "hello": "Hola",
                    "goodbye": "Adiós",
                    "welcome": "Bienvenido"
                },
                "en": {
                    "good_morning": "Good morning",
                    "good_afternoon": "Good afternoon",
                    "good_evening": "Good evening",
                    "hello": "Hello",
                    "goodbye": "Goodbye",
                    "welcome": "Welcome"
                },
                "fr": {
                    "good_morning": "Bonjour",
                    "good_afternoon": "Bon après-midi",
                    "good_evening": "Bonsoir",
                    "hello": "Salut",
                    "goodbye": "Au revoir",
                    "welcome": "Bienvenue"
                }
            },
            "responses": {
                "es": {
                    "understood": "Entendido",
                    "processing": "Procesando su solicitud",
                    "completed": "Completado",
                    "error": "Ha ocurrido un error",
                    "help": "¿En qué puedo ayudarle?",
                    "repeat": "¿Puede repetir, por favor?"
                },
                "en": {
                    "understood": "Understood",
                    "processing": "Processing your request",
                    "completed": "Completed",
                    "error": "An error has occurred",
                    "help": "How can I help you?",
                    "repeat": "Could you repeat, please?"
                },
                "fr": {
                    "understood": "Compris",
                    "processing": "Traitement de votre demande",
                    "completed": "Terminé",
                    "error": "Une erreur s'est produite",
                    "help": "Comment puis-je vous aider?",
                    "repeat": "Pouvez-vous répéter, s'il vous plaît?"
                }
            },
            "system": {
                "es": {
                    "hamilton_intro": "Soy Hamilton, su asistente personal de IA",
                    "authentication_required": "Autenticación requerida",
                    "authentication_success": "Autenticación exitosa",
                    "system_ready": "Sistema listo",
                    "going_to_sleep": "Entrando en modo dormido",
                    "wake_up": "Hamilton activado"
                },
                "en": {
                    "hamilton_intro": "I am Hamilton, your personal AI assistant",
                    "authentication_required": "Authentication required",
                    "authentication_success": "Authentication successful",
                    "system_ready": "System ready",
                    "going_to_sleep": "Going to sleep mode",
                    "wake_up": "Hamilton activated"
                },
                "fr": {
                    "hamilton_intro": "Je suis Hamilton, votre assistant IA personnel",
                    "authentication_required": "Authentification requise",
                    "authentication_success": "Authentification réussie",
                    "system_ready": "Système prêt",
                    "going_to_sleep": "Passage en mode veille",
                    "wake_up": "Hamilton activé"
                }
            }
        }
        
        # Guardar traducciones básicas
        for category, translations in basic_translations.items():
            category_file = self.translations_dir / f"{category}.json"
            with open(category_file, 'w', encoding='utf-8') as f:
                json.dump(translations, f, indent=2, ensure_ascii=False)
        
        self.translations = basic_translations
    
    def _setup_language_detection(self):
        """Configura detección automática de idioma"""
        # Patrones para detectar idiomas
        self.language_patterns = {
            Language.SPANISH: [
                r'\b(hola|buenos|buenas|gracias|por favor|disculpe|perdón)\b',
                r'\b(qué|cómo|cuándo|dónde|por qué)\b',
                r'\b(hamilton|ayuda|información)\b'
            ],
            Language.ENGLISH: [
                r'\b(hello|good|thank you|please|excuse me|sorry)\b',
                r'\b(what|how|when|where|why)\b',
                r'\b(hamilton|help|information)\b'
            ],
            Language.FRENCH: [
                r'\b(bonjour|bon|merci|s\'il vous plaît|excusez-moi|pardon)\b',
                r'\b(quoi|comment|quand|où|pourquoi)\b',
                r'\b(hamilton|aide|information)\b'
            ]
        }
    
    def detect_language(self, text: str) -> Language:
        """Detecta el idioma de un texto"""
        text_lower = text.lower()
        language_scores = {}
        
        for language, patterns in self.language_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                score += matches
            language_scores[language] = score
        
        # Retornar idioma con mayor puntuación
        if language_scores:
            detected_language = max(language_scores, key=language_scores.get)
            if language_scores[detected_language] > 0:
                return detected_language
        
        # Si no se detecta, usar idioma por defecto
        return self.current_language
    
    def translate_text(self, text: str, target_language: Language, source_language: Language = None) -> str:
        """Traduce texto a idioma objetivo REAL"""
        if not source_language:
            source_language = self.detect_language(text)

        if source_language == target_language:
            return text

        # 1. Buscar en traducciones predefinidas (rápido)
        translated = self._find_predefined_translation(text, target_language, source_language)
        if translated:
            return translated

        # 2. Usar servicio de traducción en línea
        online_translation = self._translate_with_service(text, target_language, source_language)
        if online_translation and online_translation != text:
            return online_translation

        # 3. Usar traducción local básica
        local_translation = self._translate_locally(text, target_language, source_language)
        if local_translation:
            return local_translation

        # 4. Fallback: retornar texto original
        logger.warning(f"No se pudo traducir: '{text}' de {source_language.value} a {target_language.value}")
        return text
    
    def _find_predefined_translation(self, text: str, target_lang: Language, source_lang: Language) -> Optional[str]:
        """Busca traducción en archivos predefinidos"""
        text_lower = text.lower().strip()
        
        for category, translations in self.translations.items():
            source_translations = translations.get(source_lang.value, {})
            target_translations = translations.get(target_lang.value, {})
            
            # Buscar coincidencia exacta
            for key, source_text in source_translations.items():
                if source_text.lower() == text_lower:
                    return target_translations.get(key)
            
            # Buscar coincidencia parcial
            for key, source_text in source_translations.items():
                if source_text.lower() in text_lower or text_lower in source_text.lower():
                    return target_translations.get(key)
        
        return None
    
    def _translate_with_service(self, text: str, target_lang: Language, source_lang: Language) -> str:
        """Traduce usando servicios externos REALES"""
        try:
            # Intentar con Google Translate (gratuito con límites)
            google_result = self._translate_with_google_free(text, target_lang.value, source_lang.value)
            if google_result and google_result != text:
                return google_result

            # Intentar con Microsoft Translator (gratuito con límites)
            microsoft_result = self._translate_with_microsoft_free(text, target_lang.value, source_lang.value)
            if microsoft_result and microsoft_result != text:
                return microsoft_result

        except Exception as e:
            logger.warning(f"Error en servicios de traducción: {e}")

        return text

    def _translate_with_google_free(self, text: str, target_lang: str, source_lang: str) -> Optional[str]:
        """Traduce usando Google Translate gratuito"""
        try:
            import requests
            from urllib.parse import quote

            # URL de Google Translate (método no oficial)
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': source_lang,
                'tl': target_lang,
                'dt': 't',
                'q': text
            }

            response = requests.get(url, params=params, timeout=5)

            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and len(result[0]) > 0:
                    translated = result[0][0][0]
                    logger.info(f"✅ Google Translate: '{text}' -> '{translated}'")
                    return translated

        except Exception as e:
            logger.debug(f"Error con Google Translate gratuito: {e}")

        return None

    def _translate_with_microsoft_free(self, text: str, target_lang: str, source_lang: str) -> Optional[str]:
        """Traduce usando Microsoft Translator gratuito"""
        try:
            import requests

            # Endpoint público de Microsoft (con límites)
            url = "https://api.cognitive.microsofttranslator.com/translate"
            params = {
                'api-version': '3.0',
                'from': source_lang,
                'to': target_lang
            }

            headers = {
                'Content-Type': 'application/json',
                'X-ClientTraceId': str(uuid.uuid4()) if 'uuid' in globals() else 'hamilton-ai'
            }

            body = [{'text': text}]

            response = requests.post(url, params=params, headers=headers, json=body, timeout=5)

            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0:
                    translated = result[0]['translations'][0]['text']
                    logger.info(f"✅ Microsoft Translator: '{text}' -> '{translated}'")
                    return translated

        except Exception as e:
            logger.debug(f"Error con Microsoft Translator: {e}")

        return None

    def _translate_locally(self, text: str, target_lang: Language, source_lang: Language) -> Optional[str]:
        """Traduce usando diccionario local expandido"""
        # Diccionario de traducción local expandido
        local_translations = {
            ("es", "en"): {
                # Saludos
                "hola": "hello", "buenos días": "good morning", "buenas tardes": "good afternoon",
                "buenas noches": "good night", "adiós": "goodbye", "hasta luego": "see you later",

                # Preguntas comunes
                "¿qué hora es?": "what time is it?", "¿cómo estás?": "how are you?",
                "¿dónde está?": "where is it?", "¿cuánto cuesta?": "how much does it cost?",

                # Hamilton específico
                "hamilton información": "hamilton information", "hamilton ayuda": "hamilton help",
                "hamilton estado": "hamilton status", "modo noche": "night mode",
                "modo trabajo": "work mode", "enciende las luces": "turn on the lights",
                "apaga las luces": "turn off the lights",

                # Palabras comunes
                "sí": "yes", "no": "no", "por favor": "please", "gracias": "thank you",
                "de nada": "you're welcome", "perdón": "sorry", "disculpe": "excuse me",
                "casa": "house", "trabajo": "work", "familia": "family", "tiempo": "time",
                "día": "day", "noche": "night", "mañana": "morning", "tarde": "afternoon"
            },
            ("en", "es"): {
                # Saludos
                "hello": "hola", "good morning": "buenos días", "good afternoon": "buenas tardes",
                "good night": "buenas noches", "goodbye": "adiós", "see you later": "hasta luego",

                # Preguntas comunes
                "what time is it?": "¿qué hora es?", "how are you?": "¿cómo estás?",
                "where is it?": "¿dónde está?", "how much does it cost?": "¿cuánto cuesta?",

                # Hamilton específico
                "hamilton information": "hamilton información", "hamilton help": "hamilton ayuda",
                "hamilton status": "hamilton estado", "night mode": "modo noche",
                "work mode": "modo trabajo", "turn on the lights": "enciende las luces",
                "turn off the lights": "apaga las luces",

                # Palabras comunes
                "yes": "sí", "no": "no", "please": "por favor", "thank you": "gracias",
                "you're welcome": "de nada", "sorry": "perdón", "excuse me": "disculpe",
                "house": "casa", "work": "trabajo", "family": "familia", "time": "tiempo",
                "day": "día", "night": "noche", "morning": "mañana", "afternoon": "tarde"
            },
            ("es", "fr"): {
                "hola": "bonjour", "gracias": "merci", "por favor": "s'il vous plaît",
                "adiós": "au revoir", "sí": "oui", "no": "non",
                "hamilton información": "hamilton information", "modo noche": "mode nuit"
            },
            ("fr", "es"): {
                "bonjour": "hola", "merci": "gracias", "s'il vous plaît": "por favor",
                "au revoir": "adiós", "oui": "sí", "non": "no"
            },
            ("es", "pt"): {
                "hola": "olá", "gracias": "obrigado", "por favor": "por favor",
                "adiós": "tchau", "sí": "sim", "no": "não"
            },
            ("pt", "es"): {
                "olá": "hola", "obrigado": "gracias", "por favor": "por favor",
                "tchau": "adiós", "sim": "sí", "não": "no"
            }
        }

        translation_key = (source_lang.value, target_lang.value)
        translations = local_translations.get(translation_key, {})

        # Buscar traducción exacta
        text_lower = text.lower().strip()
        if text_lower in translations:
            return translations[text_lower]

        # Buscar traducción parcial (palabras clave)
        for original, translated in translations.items():
            if original in text_lower:
                # Reemplazar la parte encontrada
                result = text_lower.replace(original, translated)
                logger.info(f"✅ Traducción local parcial: '{text}' -> '{result}'")
                return result

        return None
    
    def get_localized_response(self, key: str, language: Language = None, **kwargs) -> str:
        """Obtiene respuesta localizada"""
        if not language:
            language = self.current_language
        
        # Buscar en traducciones por categoría
        for category, translations in self.translations.items():
            lang_translations = translations.get(language.value, {})
            if key in lang_translations:
                response = lang_translations[key]
                # Formatear con parámetros si se proporcionan
                if kwargs:
                    try:
                        response = response.format(**kwargs)
                    except KeyError:
                        pass
                return response
        
        # Si no se encuentra, retornar clave
        return key
    
    def adapt_cultural_format(self, value: Any, format_type: str, language: Language = None) -> str:
        """Adapta formato según cultura"""
        if not language:
            language = self.current_language
        
        cultural_config = self.config.get("cultural_adaptations", {})
        
        if format_type == "date" and isinstance(value, datetime):
            date_format = cultural_config.get("date_formats", {}).get(language.value, "DD/MM/YYYY")
            if date_format == "DD/MM/YYYY":
                return value.strftime("%d/%m/%Y")
            elif date_format == "MM/DD/YYYY":
                return value.strftime("%m/%d/%Y")
            elif date_format == "DD.MM.YYYY":
                return value.strftime("%d.%m.%Y")
        
        elif format_type == "time" and isinstance(value, datetime):
            time_format = cultural_config.get("time_formats", {}).get(language.value, "HH:mm")
            if time_format == "HH:mm":
                return value.strftime("%H:%M")
            elif time_format == "h:mm AM/PM":
                return value.strftime("%I:%M %p")
        
        elif format_type == "currency" and isinstance(value, (int, float)):
            currency_format = cultural_config.get("currency_formats", {}).get(language.value, "$ {amount}")
            return currency_format.format(amount=value)
        
        return str(value)
    
    def set_user_language(self, username: str, language: Language):
        """Establece idioma para usuario específico"""
        if username in self.user_profiles:
            self.user_profiles[username].primary_language = language
        else:
            # Crear perfil básico
            self.user_profiles[username] = LanguageProfile(
                primary_language=language,
                secondary_languages=[],
                voice_preference=f"{language.value}_male",
                formality_level="formal",
                cultural_context="default"
            )
        
        # Actualizar idioma actual si es el usuario activo
        if username == settings.AUTHORIZED_USER:
            self.current_language = language
        
        logger.info(f"Idioma establecido para {username}: {language.value}")
    
    def get_user_language(self, username: str) -> Language:
        """Obtiene idioma del usuario"""
        if username in self.user_profiles:
            return self.user_profiles[username].primary_language
        return self.current_language
    
    def process_multilingual_command(self, command: str, username: str = None) -> Tuple[str, Language]:
        """Procesa comando multiidioma"""
        # Detectar idioma del comando
        detected_language = self.detect_language(command)
        
        # Obtener idioma preferido del usuario
        user_language = self.get_user_language(username or settings.AUTHORIZED_USER)
        
        # Si el comando está en idioma diferente al preferido, traducir
        if detected_language != user_language:
            if self.user_profiles.get(username, LanguageProfile(Language.SPANISH, [], "", "", "")).auto_translate:
                translated_command = self.translate_text(command, user_language, detected_language)
                logger.info(f"Comando traducido: '{command}' -> '{translated_command}'")
                return translated_command, detected_language
        
        return command, detected_language
    
    def get_voice_settings(self, language: Language = None) -> Dict[str, Any]:
        """Obtiene configuración de voz para idioma"""
        if not language:
            language = self.current_language
        
        voice_config = self.config.get("voice_settings", {})
        return voice_config.get(language.value, voice_config.get("es", {}))

# Instancia global
hamilton_multilingual = MultilingualSystem()
