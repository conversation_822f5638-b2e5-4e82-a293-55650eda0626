import logging
import time
from typing import Dict, Any, List
import anthropic
from config.settings import settings

class HamiltonAnthropic:
    """Motor de IA de Hamilton usando Anthropic Claude"""
    
    def __init__(self):
        """Inicializa el motor de Anthropic"""
        self.client = None
        self.model = "claude-3-opus-20240229"
        self.conversation_history = []
        self.learning_stats = {
            "accuracy": 0.0,
            "response_time": 0.0,
            "user_satisfaction": 0.0,
            "context_usage": 0.0
        }
        self.initialize_client()
        self.logger = logging.getLogger(__name__)

    def initialize_client(self):
        """Inicializa el cliente de Anthropic"""
        try:
            if settings.ANTHROPIC_API_KEY:
                self.client = anthropic.Anthropic(api_key=settings.ANTHROPIC_API_KEY)
                self.logger.info("Cliente de Anthropic inicializado correctamente")
            else:
                self.logger.warning("No se encontró API key de Anthropic")
        except Exception as e:
            self.logger.error(f"Error inicializando cliente de Anthropic: {e}")

    def process_query(self, query: str, context: Dict[str, Any] = None) -> str:
        """Procesa una consulta usando Claude"""
        try:
            if not self.client:
                return "Error: Cliente de Anthropic no inicializado"

            start_time = time.time()
            
            # Preparar mensajes con historial
            messages = self._prepare_messages(query, context)
            
            # Obtener respuesta de Claude
            response = self.client.messages.create(
                model=self.model,
                messages=messages,
                max_tokens=1000,
                temperature=0.7,
                system="Eres Hamilton, un asistente de IA avanzado diseñado para ayudar a los usuarios de manera precisa y amigable."
            )
            
            # Extraer respuesta
            answer = response.content[0].text
            
            # Actualizar estadísticas
            self._update_stats(time.time() - start_time)
            
            # Actualizar historial
            self._update_history(query, answer)
            
            return answer
            
        except Exception as e:
            self.logger.error(f"Error procesando consulta con Anthropic: {e}")
            return f"Lo siento, hubo un error procesando tu consulta: {str(e)}"

    def _prepare_messages(self, query: str, context: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """Prepara los mensajes para Claude incluyendo historial y contexto"""
        messages = []
        
        # Agregar historial de conversación
        for entry in self.conversation_history[-5:]:  # Últimas 5 interacciones
            messages.append({"role": "user", "content": entry["query"]})
            messages.append({"role": "assistant", "content": entry["response"]})
        
        # Agregar contexto si está disponible
        if context:
            context_str = "Contexto adicional:\n"
            for key, value in context.items():
                context_str += f"{key}: {value}\n"
            messages.append({"role": "user", "content": context_str})
        
        # Agregar consulta actual
        messages.append({"role": "user", "content": query})
        
        return messages

    def _update_stats(self, response_time: float):
        """Actualiza las estadísticas de aprendizaje"""
        self.learning_stats["response_time"] = (
            self.learning_stats["response_time"] * 0.9 + response_time * 0.1
        )

    def _update_history(self, query: str, response: str):
        """Actualiza el historial de conversación"""
        self.conversation_history.append({
            "query": query,
            "response": response,
            "timestamp": time.time()
        })
        
        # Mantener solo las últimas 10 interacciones
        if len(self.conversation_history) > 10:
            self.conversation_history.pop(0)

    def get_learning_stats(self) -> Dict[str, Any]:
        """Obtiene las estadísticas de aprendizaje actuales"""
        return self.learning_stats

    def clear_history(self):
        """Limpia el historial de conversación"""
        self.conversation_history = []

# Instancia global
hamilton_anthropic = HamiltonAnthropic() 