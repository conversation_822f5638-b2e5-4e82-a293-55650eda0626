#!/usr/bin/env python3
"""
Script para ejecutar el análisis predictivo.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/analysis.log')
    ]
)

logger = logging.getLogger(__name__)

def verify_environment():
    """Verifica que el entorno esté correctamente configurado."""
    try:
        # Verificar directorios necesarios
        required_dirs = ['logs', 'analysis']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Directorio creado: {dir_path}")

        # Verificar archivo de métricas
        metrics_file = Path('logs/metrics.json')
        if not metrics_file.exists():
            logger.error("Archivo de métricas no encontrado")
            return False

        return True
    except Exception as e:
        logger.error(f"Error al verificar el entorno: {str(e)}")
        return False

def save_analysis_results(results):
    """Guarda los resultados del análisis."""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = Path(f'analysis/results_{timestamp}.json')
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"Resultados guardados en {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error al guardar resultados: {str(e)}")
        return False

def run_analysis():
    """Ejecuta el análisis predictivo."""
    try:
        from ai.predictive_analysis import PredictiveAnalyzer
        
        # Inicializar el analizador
        analyzer = PredictiveAnalyzer()
        
        # Realizar análisis
        logger.info("Iniciando análisis predictivo...")
        results = analyzer.analyze()
        
        # Guardar resultados
        if save_analysis_results(results):
            logger.info("Análisis completado exitosamente")
            
            # Mostrar resumen
            print("\nResumen del Análisis:")
            print("-" * 50)
            
            if results['anomalies']:
                print("\nAnomalías detectadas:")
                for anomaly in results['anomalies']:
                    print(f"- {anomaly['metric']}: {anomaly['value']:.2f}")
            
            print("\nPredicciones para las próximas 24 horas:")
            for metric, value in results['predictions'].items():
                print(f"- {metric}: {value:.2f}")
            
            print("\nRecomendaciones:")
            for rec in results['recommendations']:
                print(f"- {rec}")
                
        else:
            logger.error("Error al guardar los resultados del análisis")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error al ejecutar el análisis: {str(e)}")
        sys.exit(1)

def main():
    """Función principal."""
    try:
        logger.info("Iniciando verificación del entorno...")
        if not verify_environment():
            logger.error("La verificación del entorno falló")
            sys.exit(1)
            
        logger.info("Iniciando análisis predictivo...")
        run_analysis()
        
    except KeyboardInterrupt:
        logger.info("Análisis detenido por el usuario")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main() 