"""
Hamilton AI Assistant - Learning Engine
Motor de aprendizaje y personalización
"""

import numpy as np
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score
import torch
import torch.nn as nn
import pandas as pd
from datetime import datetime
import logging
from typing import Dict, List, Any, Tuple
import json

from storage.database_manager import hamilton_db
from security.encryption import hamilton_encryption

logger = logging.getLogger(__name__)

class HamiltonLearningEngine:
    """Motor de aprendizaje avanzado para Hamilton"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.cluster_model = KMeans(n_clusters=5)
        self.classifier = RandomForestClassifier(n_estimators=100)
        self.sequence_model = self._build_sequence_model()
        self.metrics = {
            'accuracy': [],
            'precision': [],
            'recall': [],
            'response_time': [],
            'user_satisfaction': []
        }
    
    def _build_sequence_model(self) -> torch.nn.Module:
        """Construye modelo de secuencias para predicción"""
        model = torch.nn.Sequential(
            torch.nn.LSTM(32, 64, batch_first=True),
            torch.nn.Dropout(0.2),
            torch.nn.Linear(64, 16),
            torch.nn.Softmax(dim=1)
        )
        return model
    
    def analyze_behavior_patterns(self, username: str) -> Dict[str, Any]:
        """Analiza patrones de comportamiento usando múltiples algoritmos"""
        try:
            # Obtener datos históricos
            with hamilton_db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT command, timestamp, response_time_ms, user_satisfaction
                    FROM user_interactions
                    WHERE username = ?
                    ORDER BY timestamp DESC
                    LIMIT 1000
                """, (username,))
                data = cursor.fetchall()
            
            if not data:
                return {}
            
            # Convertir a DataFrame
            df = pd.DataFrame(data, columns=['command', 'timestamp', 'response_time', 'satisfaction'])
            
            # Análisis de clusters
            features = self._extract_features(df)
            clusters = self.cluster_model.fit_predict(features)
            
            # Análisis de secuencias
            sequences = self._prepare_sequences(df)
            sequence_predictions = self.sequence_model(sequences)
            
            # Análisis temporal
            temporal_patterns = self._analyze_temporal_patterns(df)
            
            # Calcular métricas
            self._update_metrics(df)
            
            patterns = {
                'clusters': clusters.tolist(),
                'sequence_patterns': sequence_predictions.tolist(),
                'temporal_patterns': temporal_patterns,
                'metrics': self.metrics
            }
            
            # Guardar patrones
            self._save_behavior_patterns(username, patterns)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analizando patrones: {e}")
            return {}
    
    def _extract_features(self, df: pd.DataFrame) -> np.ndarray:
        """Extrae características para clustering"""
        # Características básicas
        features = df[['response_time', 'satisfaction']].values
        
        # Características temporales
        df['hour'] = pd.to_datetime(df['timestamp']).dt.hour
        df['day_of_week'] = pd.to_datetime(df['timestamp']).dt.dayofweek
        
        # Agregar características temporales
        temporal_features = df[['hour', 'day_of_week']].values
        features = np.hstack([features, temporal_features])
        
        # Normalizar
        return self.scaler.fit_transform(features)
    
    def _prepare_sequences(self, df: pd.DataFrame) -> np.ndarray:
        """Prepara secuencias para el modelo LSTM"""
        # Convertir comandos a embeddings
        command_embeddings = self._get_command_embeddings(df['command'])
        
        # Crear secuencias de longitud 10
        sequences = []
        for i in range(len(command_embeddings) - 9):
            sequences.append(command_embeddings[i:i+10])
        
        return np.array(sequences)
    
    def _analyze_temporal_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analiza patrones temporales"""
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        patterns = {
            'hourly_activity': df.groupby(df['timestamp'].dt.hour).size().to_dict(),
            'daily_activity': df.groupby(df['timestamp'].dt.day_name()).size().to_dict(),
            'response_time_by_hour': df.groupby(df['timestamp'].dt.hour)['response_time'].mean().to_dict(),
            'satisfaction_by_hour': df.groupby(df['timestamp'].dt.hour)['satisfaction'].mean().to_dict()
        }
        
        return patterns
    
    def _update_metrics(self, df: pd.DataFrame):
        """Actualiza métricas de rendimiento"""
        self.metrics['accuracy'].append(accuracy_score(df['satisfaction'], df['satisfaction'] > 0.5))
        self.metrics['precision'].append(precision_score(df['satisfaction'], df['satisfaction'] > 0.5))
        self.metrics['recall'].append(recall_score(df['satisfaction'], df['satisfaction'] > 0.5))
        self.metrics['response_time'].append(df['response_time'].mean())
        self.metrics['user_satisfaction'].append(df['satisfaction'].mean())
    
    def _get_command_embeddings(self, commands: pd.Series) -> np.ndarray:
        """Genera embeddings para comandos"""
        # Implementación simple usando one-hot encoding
        unique_commands = commands.unique()
        embeddings = np.zeros((len(commands), len(unique_commands)))
        
        for i, cmd in enumerate(commands):
            idx = np.where(unique_commands == cmd)[0][0]
            embeddings[i, idx] = 1
        
        return embeddings
    
    def predict_next_action(self, username: str, current_context: Dict[str, Any]) -> str:
        """Predice la siguiente acción probable del usuario"""
        try:
            # Obtener patrones actuales
            patterns = self.analyze_behavior_patterns(username)
            
            if not patterns:
                return None
            
            # Preparar contexto actual
            context_features = self._extract_context_features(current_context)
            
            # Predecir usando el clasificador
            prediction = self.classifier.predict([context_features])[0]
            
            return prediction
            
        except Exception as e:
            logger.error(f"Error prediciendo siguiente acción: {e}")
            return None
    
    def _extract_context_features(self, context: Dict[str, Any]) -> np.ndarray:
        """Extrae características del contexto actual"""
        features = []
        
        # Características temporales
        current_time = datetime.now()
        features.extend([
            current_time.hour / 24,  # Hora normalizada
            current_time.weekday() / 7,  # Día normalizado
            current_time.month / 12  # Mes normalizado
        ])
        
        # Características de contexto
        if 'last_command' in context:
            features.extend(self._get_command_embeddings([context['last_command']])[0])
        
        if 'user_mood' in context:
            features.append(context['user_mood'])
        
        return np.array(features)
    
    def get_learning_metrics(self) -> Dict[str, List[float]]:
        """Obtiene métricas de aprendizaje actuales"""
        return self.metrics
    
    def _save_behavior_patterns(self, username: str, patterns: Dict[str, Any]):
        """Guarda los patrones de comportamiento en la base de datos"""
        try:
            user_id = hamilton_db.get_user_id(username)
            if not user_id:
                return
            
            encrypted_data = hamilton_encryption.encrypt_data(patterns)
            
            with hamilton_db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO behavior_patterns
                    (user_id, pattern_data, timestamp)
                    VALUES (?, ?, ?)
                """, (user_id, encrypted_data, datetime.now()))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error guardando patrones de comportamiento: {e}")

# Instancia global
hamilton_learning = HamiltonLearningEngine()
