import os
import sys
import time
import json
import logging
from datetime import datetime
from pathlib import Path
import psutil
import requests
from dotenv import load_dotenv

def setup_logging():
    """Configura el sistema de logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/monitoring.log')
        ]
    )
    return logging.getLogger(__name__)

class SystemMonitor:
    def __init__(self):
        self.logger = setup_logging()
        self.metrics_file = 'logs/metrics.json'
        self.alert_thresholds = {
            'cpu_percent': 80,
            'memory_percent': 80,
            'disk_percent': 90,
            'response_time': 2.0,  # segundos
            'quality_metrics': {
                'relevance': 0.7,
                'coherence': 0.7,
                'completeness': 0.7,
                'clarity': 0.7,
                'context_usage': 0.7
            },
            'error_rate': 0.1,  # 10% de errores máximo
            'consecutive_failures': 3  # Número máximo de fallos consecutivos
        }
        self.consecutive_failures = 0
        self.last_metrics = None
        
    def get_system_metrics(self):
        """Obtiene métricas del sistema"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent,
                'memory_used': memory.used,
                'memory_total': memory.total,
                'disk_used': disk.used,
                'disk_total': disk.total
            }
        except Exception as e:
            self.logger.error(f"Error obteniendo métricas del sistema: {e}")
            return None
            
    def get_ai_metrics(self):
        """Obtiene métricas de los motores de IA"""
        try:
            response = requests.get('http://localhost:8000/api/ai/status')
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Error obteniendo métricas de IA: {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"Error conectando con la API: {e}")
            return None
            
    def check_alerts(self, metrics):
        """Verifica si hay alertas basadas en las métricas"""
        alerts = []
        
        # Alertas del sistema
        if metrics['cpu_percent'] > self.alert_thresholds['cpu_percent']:
            alerts.append(f"CPU usage high: {metrics['cpu_percent']}%")
            
        if metrics['memory_percent'] > self.alert_thresholds['memory_percent']:
            alerts.append(f"Memory usage high: {metrics['memory_percent']}%")
            
        if metrics['disk_percent'] > self.alert_thresholds['disk_percent']:
            alerts.append(f"Disk usage high: {metrics['disk_percent']}%")
            
        # Alertas de calidad de IA
        if 'ai' in metrics:
            for engine, data in metrics['ai'].items():
                if 'quality_metrics' in data:
                    quality = data['quality_metrics']
                    for metric, threshold in self.alert_thresholds['quality_metrics'].items():
                        if metric in quality and quality[metric] < threshold:
                            alerts.append(
                                f"Low {metric} score for {engine}: {quality[metric]:.2f} "
                                f"(threshold: {threshold})"
                            )
                            
                # Verificar tasa de errores
                if 'error_count' in data and 'total_requests' in data:
                    error_rate = data['error_count'] / data['total_requests']
                    if error_rate > self.alert_thresholds['error_rate']:
                        alerts.append(
                            f"High error rate for {engine}: {error_rate:.2%} "
                            f"(threshold: {self.alert_thresholds['error_rate']:.2%})"
                        )
                        
        # Verificar fallos consecutivos
        if self.last_metrics:
            if metrics == self.last_metrics:
                self.consecutive_failures += 1
                if self.consecutive_failures >= self.alert_thresholds['consecutive_failures']:
                    alerts.append(
                        f"System appears to be stuck: {self.consecutive_failures} "
                        "consecutive identical metrics"
                    )
            else:
                self.consecutive_failures = 0
                
        self.last_metrics = metrics
        return alerts
        
    def save_metrics(self, system_metrics, ai_metrics):
        """Guarda las métricas en un archivo JSON"""
        try:
            metrics = {
                'system': system_metrics,
                'ai': ai_metrics,
                'timestamp': datetime.now().isoformat()
            }
            
            # Crear directorio de logs si no existe
            Path('logs').mkdir(exist_ok=True)
            
            # Guardar métricas
            with open(self.metrics_file, 'a') as f:
                f.write(json.dumps(metrics) + '\n')
                
        except Exception as e:
            self.logger.error(f"Error guardando métricas: {e}")
            
    def monitor(self, interval=60):
        """Monitorea el sistema continuamente"""
        self.logger.info("Iniciando monitoreo del sistema...")
        
        while True:
            try:
                # Obtener métricas
                system_metrics = self.get_system_metrics()
                ai_metrics = self.get_ai_metrics()
                
                if system_metrics and ai_metrics:
                    # Combinar métricas
                    metrics = {
                        **system_metrics,
                        'ai': ai_metrics
                    }
                    
                    # Verificar alertas
                    alerts = self.check_alerts(metrics)
                    if alerts:
                        for alert in alerts:
                            self.logger.warning(f"ALERTA: {alert}")
                            
                    # Guardar métricas
                    self.save_metrics(system_metrics, ai_metrics)
                    
                    # Mostrar resumen
                    self.logger.info(f"""
                    Resumen del sistema:
                    CPU: {system_metrics['cpu_percent']}%
                    Memoria: {system_metrics['memory_percent']}%
                    Disco: {system_metrics['disk_percent']}%
                    Motores IA activos: {len([m for m in ai_metrics.values() if m['active']])}
                    Alertas activas: {len(alerts)}
                    """)
                    
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("Monitoreo detenido por el usuario")
                break
            except Exception as e:
                self.logger.error(f"Error en el monitoreo: {e}")
                time.sleep(interval)

def main():
    """Función principal"""
    # Cargar variables de entorno
    load_dotenv()
    
    # Crear y ejecutar monitor
    monitor = SystemMonitor()
    monitor.monitor()

if __name__ == "__main__":
    main() 