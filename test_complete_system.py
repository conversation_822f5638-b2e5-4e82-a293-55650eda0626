#!/usr/bin/env python3
"""
Hamilton AI Assistant - Complete System Test
Prueba completa de todas las funcionalidades implementadas
"""

import sys
import asyncio
import logging
from pathlib import Path

# Agregar directorio actual al path
sys.path.insert(0, str(Path(__file__).parent))

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_hamilton_system():
    """Prueba completa del sistema Hamilton"""
    print("🚀 HAMILTON AI ASSISTANT - PRUEBA COMPLETA DEL SISTEMA")
    print("=" * 60)
    
    test_results = {}
    
    # 1. Probar núcleo principal
    print("\n1. 🧠 PROBANDO NÚCLEO PRINCIPAL")
    print("-" * 30)
    try:
        from core.hamilton_core import hamilton
        
        # Probar inicialización
        print("✅ Núcleo importado correctamente")
        
        # Probar procesamiento básico
        response = hamilton.process_text_input("<PERSON><PERSON> Hamilton")
        print(f"📝 Respuesta: {response}")
        
        test_results['core'] = True
        
    except Exception as e:
        print(f"❌ Error en núcleo: {e}")
        test_results['core'] = False
    
    # 2. Probar sistema IoT
    print("\n2. 🏠 PROBANDO SISTEMA IOT")
    print("-" * 30)
    try:
        from integrations.iot_controller import hamilton_iot
        
        # Probar descubrimiento de dispositivos
        devices = await hamilton_iot.discover_devices()
        print(f"✅ Dispositivos descubiertos: {len(devices)}")
        
        # Probar control de dispositivos simulados
        if devices:
            device_id = devices[0].id
            success = await hamilton_iot.control_device(device_id, "turn_on", {})
            print(f"✅ Control de dispositivo: {'Exitoso' if success else 'Fallido'}")
        
        test_results['iot'] = True
        
    except Exception as e:
        print(f"❌ Error en IoT: {e}")
        test_results['iot'] = False
    
    # 3. Probar automatización del hogar
    print("\n3. 🏡 PROBANDO AUTOMATIZACIÓN DEL HOGAR")
    print("-" * 30)
    try:
        from integrations.home_automation import hamilton_automation
        
        # Probar comando de voz
        response = await hamilton_automation.process_voice_command("modo noche")
        print(f"✅ Comando de automatización: {response}")
        
        # Probar activación de escena
        success = await hamilton_automation.activate_scene("modo_trabajo")
        print(f"✅ Activación de escena: {'Exitosa' if success else 'Fallida'}")
        
        test_results['automation'] = True
        
    except Exception as e:
        print(f"❌ Error en automatización: {e}")
        test_results['automation'] = False
    
    # 4. Probar gestión de calendario
    print("\n4. 📅 PROBANDO GESTIÓN DE CALENDARIO")
    print("-" * 30)
    try:
        from integrations.calendar_manager import hamilton_calendar
        from datetime import datetime, timedelta
        
        # Crear evento de prueba
        tomorrow = datetime.now() + timedelta(days=1)
        event_id = await hamilton_calendar.create_event(
            "Reunión de prueba",
            tomorrow.replace(hour=14, minute=0),
            tomorrow.replace(hour=15, minute=0),
            "Evento creado por prueba del sistema"
        )
        print(f"✅ Evento creado: {event_id}")
        
        # Probar consulta de agenda
        response = await hamilton_calendar.process_voice_command("agenda de hoy")
        print(f"✅ Consulta de agenda: {response}")
        
        test_results['calendar'] = True
        
    except Exception as e:
        print(f"❌ Error en calendario: {e}")
        test_results['calendar'] = False
    
    # 5. Probar sistema multiidioma
    print("\n5. 🌍 PROBANDO SISTEMA MULTIIDIOMA")
    print("-" * 30)
    try:
        from integrations.multilingual_system import hamilton_multilingual
        
        # Probar detección de idioma
        from integrations.multilingual_system import Language
        detected = hamilton_multilingual.detect_language("Hello Hamilton")
        print(f"✅ Detección de idioma: {detected.value}")
        
        # Probar traducción
        translated = hamilton_multilingual.translate_text("Hello", Language.SPANISH, Language.ENGLISH)
        print(f"✅ Traducción: {translated}")
        
        # Probar respuesta localizada
        response = hamilton_multilingual.get_localized_response("hamilton_intro")
        print(f"✅ Respuesta localizada: {response}")
        
        test_results['multilingual'] = True
        
    except Exception as e:
        print(f"❌ Error en multiidioma: {e}")
        test_results['multilingual'] = False
    
    # 6. Probar reconocimiento de emociones
    print("\n6. 😊 PROBANDO RECONOCIMIENTO DE EMOCIONES")
    print("-" * 30)
    try:
        from integrations.emotion_recognition import hamilton_emotion
        
        # Probar análisis de estado emocional
        summary = hamilton_emotion.get_emotion_summary()
        print(f"✅ Resumen emocional: {summary}")
        
        # Probar estilo de respuesta adaptativo
        style = hamilton_emotion.get_adaptive_response_style()
        print(f"✅ Estilo adaptativo: {style}")
        
        test_results['emotion'] = True
        
    except Exception as e:
        print(f"❌ Error en emociones: {e}")
        test_results['emotion'] = False
    
    # 7. Probar modelos locales
    print("\n7. 🤖 PROBANDO MODELOS LOCALES")
    print("-" * 30)
    try:
        from ai.local_models import hamilton_local_models
        
        # Probar estado de modelos
        status = hamilton_local_models.get_model_status()
        print(f"✅ Estado de modelos: {status}")
        
        # Intentar inicializar modelos (puede fallar si no están instalados)
        try:
            await hamilton_local_models.initialize_models()
            print("✅ Modelos locales inicializados")
        except Exception as e:
            print(f"⚠️ Modelos locales no disponibles: {e}")
        
        test_results['local_models'] = True
        
    except Exception as e:
        print(f"❌ Error en modelos locales: {e}")
        test_results['local_models'] = False
    
    # 8. Probar generador de respuestas naturales
    print("\n8. 💬 PROBANDO GENERADOR DE RESPUESTAS")
    print("-" * 30)
    try:
        from ai.response_generator import hamilton_response_generator, ResponseContext
        
        # Crear contexto de prueba
        context = ResponseContext(
            user_name="señor Ibero",
            time_of_day="afternoon"
        )
        
        # Generar respuesta natural
        response = hamilton_response_generator.generate_response(
            "greeting", "Hola", context
        )
        print(f"✅ Respuesta natural: {response}")
        
        test_results['response_generator'] = True
        
    except Exception as e:
        print(f"❌ Error en generador de respuestas: {e}")
        test_results['response_generator'] = False
    
    # 9. Probar aprendizaje por refuerzo
    print("\n9. 🧠 PROBANDO APRENDIZAJE POR REFUERZO")
    print("-" * 30)
    try:
        from ai.reinforcement_learning import hamilton_rl_agent
        
        # Probar estadísticas de aprendizaje
        stats = hamilton_rl_agent.get_learning_stats()
        print(f"✅ Estadísticas de RL: {stats}")
        
        test_results['reinforcement_learning'] = True
        
    except Exception as e:
        print(f"❌ Error en aprendizaje por refuerzo: {e}")
        test_results['reinforcement_learning'] = False
    
    # 10. Probar NLP avanzado
    print("\n10. 🔤 PROBANDO NLP AVANZADO")
    print("-" * 30)
    try:
        from ai.learning_engine_simple import hamilton_nlp
        
        # Probar análisis de intención
        analysis = hamilton_nlp.analyze_intent("Hamilton, ¿qué hora es?")
        print(f"✅ Análisis de intención: {analysis['primary_intent']}")
        
        # Probar extracción de entidades
        entities = hamilton_nlp.extract_entities("Enciende las luces de la sala")
        print(f"✅ Entidades extraídas: {entities}")
        
        test_results['nlp'] = True
        
    except Exception as e:
        print(f"❌ Error en NLP: {e}")
        test_results['nlp'] = False
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for component, result in test_results.items():
        status = "✅ PASÓ" if result else "❌ FALLÓ"
        print(f"{component:.<30} {status}")
    
    print(f"\nTotal: {passed_tests}/{total_tests} pruebas exitosas ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 ¡TODAS LAS FUNCIONALIDADES ESTÁN OPERATIVAS!")
        print("Hamilton está completamente funcional con todas las mejoras implementadas.")
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ ¡SISTEMA MAYORMENTE FUNCIONAL!")
        print("Hamilton está operativo con la mayoría de funcionalidades avanzadas.")
    else:
        print("\n⚠️ SISTEMA PARCIALMENTE FUNCIONAL")
        print("Algunas funcionalidades necesitan atención.")
    
    return test_results

if __name__ == "__main__":
    try:
        # Ejecutar pruebas
        results = asyncio.run(test_complete_hamilton_system())
        
        print("\n🔧 PRÓXIMOS PASOS RECOMENDADOS:")
        print("1. Ejecutar scripts de mejoras inmediatas si no se han ejecutado")
        print("2. Configurar APIs externas (OpenAI, servicios de traducción)")
        print("3. Instalar modelos locales opcionales")
        print("4. Configurar dispositivos IoT reales")
        print("5. Personalizar configuraciones según necesidades específicas")
        
    except KeyboardInterrupt:
        print("\n\n👋 Prueba interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
