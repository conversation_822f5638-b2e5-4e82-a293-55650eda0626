{"ollama": {"enabled": true, "host": "localhost", "port": 11434, "auto_start": true, "models_to_pull": ["llama2:7b", "mistral:7b", "codellama:7b"]}, "llamacpp": {"enabled": true, "executable_path": "", "default_threads": 4, "use_gpu": false}, "whisper": {"enabled": true, "model_size": "base", "language": "es", "device": "cpu"}, "models": {"hamilton_chat": {"type": "llama2_7b", "context_length": 4096, "temperature": 0.7, "max_tokens": 512, "system_prompt": "<PERSON><PERSON>, un asistente personal de IA masculino, formal pero amigable, diseñado específicamente para ayudar al señor Ibero. Respondes de manera precisa, útil y con un tono profesional pero cálido en español.", "enabled": true}, "hamilton_code": {"type": "codellama_7b", "context_length": 2048, "temperature": 0.3, "max_tokens": 1024, "system_prompt": "<PERSON><PERSON>, un asistente de programación experto. Ayudas con código, debugging y explicaciones técnicas de manera clara y precisa.", "enabled": false}, "hamilton_reasoning": {"type": "mistral_7b", "context_length": 8192, "temperature": 0.5, "max_tokens": 800, "system_prompt": "<PERSON><PERSON>, especializado en razonamiento lógico y análisis. Proporcionas explicaciones detalladas y análisis paso a paso.", "enabled": false}}, "performance": {"max_concurrent_requests": 2, "request_timeout_seconds": 30, "memory_limit_mb": 8192, "auto_unload_inactive_minutes": 15}}