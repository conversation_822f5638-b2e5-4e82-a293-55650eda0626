import os
import json
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path
import seaborn as sns

def setup_logging():
    """Configura el sistema de logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/visualization.log')
        ]
    )
    return logging.getLogger(__name__)

class MetricsVisualizer:
    def __init__(self):
        self.logger = setup_logging()
        self.metrics_file = 'logs/metrics.json'
        self.output_dir = 'visualizations'
        
        # Crear directorio de visualizaciones
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # Configurar estilo de gráficos
        plt.style.use('seaborn')
        sns.set_palette("husl")
        
    def load_metrics(self, hours=24):
        """Carga las métricas de las últimas N horas"""
        try:
            metrics = []
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            with open(self.metrics_file, 'r') as f:
                for line in f:
                    data = json.loads(line)
                    timestamp = datetime.fromisoformat(data['timestamp'])
                    
                    if timestamp >= cutoff_time:
                        metrics.append(data)
                        
            return metrics
        except Exception as e:
            self.logger.error(f"Error cargando métricas: {e}")
            return []
            
    def plot_trends(self, df, metric, title, ax):
        """Genera gráfico de tendencias con línea de regresión"""
        # Gráfico de dispersión
        sns.scatterplot(data=df, x='timestamp', y=metric, ax=ax, alpha=0.5)
        
        # Línea de tendencia
        sns.regplot(data=df, x='timestamp', y=metric, ax=ax, scatter=False, color='red')
        
        # Calcular estadísticas
        mean = df[metric].mean()
        std = df[metric].std()
        
        # Añadir líneas de media y desviación estándar
        ax.axhline(y=mean, color='green', linestyle='--', alpha=0.5, label=f'Media: {mean:.2f}')
        ax.axhline(y=mean + std, color='orange', linestyle=':', alpha=0.5, label=f'+1σ: {mean + std:.2f}')
        ax.axhline(y=mean - std, color='orange', linestyle=':', alpha=0.5, label=f'-1σ: {mean - std:.2f}')
        
        ax.set_title(title)
        ax.set_ylabel('Valor')
        ax.grid(True)
        ax.legend()
        
    def plot_system_metrics(self, metrics):
        """Genera gráficos de métricas del sistema"""
        try:
            # Convertir a DataFrame
            df = pd.DataFrame([
                {
                    'timestamp': datetime.fromisoformat(m['timestamp']),
                    'cpu': m['system']['cpu_percent'],
                    'memory': m['system']['memory_percent'],
                    'disk': m['system']['disk_percent']
                }
                for m in metrics
            ])
            
            # Crear figura con subplots
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))
            fig.suptitle('Métricas del Sistema con Análisis de Tendencias', fontsize=16)
            
            # Gráficos con tendencias
            self.plot_trends(df, 'cpu', 'Uso de CPU', ax1)
            self.plot_trends(df, 'memory', 'Uso de Memoria', ax2)
            self.plot_trends(df, 'disk', 'Uso de Disco', ax3)
            
            # Ajustar layout
            plt.tight_layout()
            
            # Guardar gráfico
            output_file = os.path.join(self.output_dir, 'system_metrics_trends.png')
            plt.savefig(output_file)
            plt.close()
            
            self.logger.info(f"Gráfico de métricas del sistema guardado en {output_file}")
            
        except Exception as e:
            self.logger.error(f"Error generando gráfico de métricas del sistema: {e}")
            
    def plot_ai_metrics(self, metrics):
        """Genera gráficos de métricas de IA"""
        try:
            # Extraer métricas de calidad
            quality_metrics = []
            for m in metrics:
                for engine, data in m['ai'].items():
                    if 'quality_metrics' in data:
                        quality_metrics.append({
                            'timestamp': datetime.fromisoformat(m['timestamp']),
                            'engine': engine,
                            **data['quality_metrics']
                        })
                        
            if not quality_metrics:
                self.logger.warning("No se encontraron métricas de calidad")
                return
                
            # Convertir a DataFrame
            df = pd.DataFrame(quality_metrics)
            
            # Crear figura con subplots
            fig, axes = plt.subplots(2, 3, figsize=(15, 12))
            fig.suptitle('Métricas de Calidad de IA con Análisis de Tendencias', fontsize=16)
            
            # Métricas a graficar
            metrics = ['relevance', 'coherence', 'completeness', 'clarity', 'context_usage']
            titles = ['Relevancia', 'Coherencia', 'Completitud', 'Claridad', 'Uso de Contexto']
            
            # Generar gráficos
            for i, (metric, title) in enumerate(zip(metrics, titles)):
                row = i // 3
                col = i % 3
                
                # Filtrar datos para cada motor
                for engine in df['engine'].unique():
                    engine_data = df[df['engine'] == engine]
                    self.plot_trends(engine_data, metric, f'{title} - {engine}', axes[row, col])
                    
            # Ajustar layout
            plt.tight_layout()
            
            # Guardar gráfico
            output_file = os.path.join(self.output_dir, 'ai_metrics_trends.png')
            plt.savefig(output_file)
            plt.close()
            
            self.logger.info(f"Gráfico de métricas de IA guardado en {output_file}")
            
        except Exception as e:
            self.logger.error(f"Error generando gráfico de métricas de IA: {e}")
            
    def generate_report(self, hours=24):
        """Genera un reporte completo de métricas"""
        self.logger.info(f"Generando reporte de las últimas {hours} horas...")
        
        # Cargar métricas
        metrics = self.load_metrics(hours)
        if not metrics:
            self.logger.error("No se encontraron métricas para generar el reporte")
            return
            
        # Generar gráficos
        self.plot_system_metrics(metrics)
        self.plot_ai_metrics(metrics)
        
        self.logger.info("Reporte generado exitosamente")

def main():
    """Función principal"""
    visualizer = MetricsVisualizer()
    visualizer.generate_report()

if __name__ == "__main__":
    main() 