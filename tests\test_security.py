"""
Hamilton AI Assistant - Security Tests
Pruebas unitarias para el sistema de seguridad
"""

import pytest
import unittest
import tempfile
import os
from pathlib import Path
import sys

# Agregar el directorio raíz al path
sys.path.insert(0, str(Path(__file__).parent.parent))

from security.encryption import HamiltonEncry<PERSON>, SecurityLogger
import json

class TestHamiltonEncryption(unittest.TestCase):
    """Pruebas para el sistema de cifrado"""
    
    def setUp(self):
        """Configuración antes de cada prueba"""
        # Crear directorio temporal para pruebas
        self.temp_dir = tempfile.mkdtemp()
        self.test_encryption = HamiltonEncryption()
        # Usar directorio temporal para archivos de prueba
        self.test_encryption.key_file = Path(self.temp_dir) / "test_master.key"
        self.test_encryption.salt_file = Path(self.temp_dir) / "test_salt.bin"
        self.test_encryption._initialize_encryption()
    
    def tearDown(self):
        """Limpieza después de cada prueba"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_encrypt_decrypt_string(self):
        """Prueba cifrado y descifrado de strings"""
        test_string = "Mensaje secreto de Hamilton"
        
        # Cifrar
        encrypted = self.test_encryption.encrypt_data(test_string)
        self.assertIsInstance(encrypted, bytes)
        self.assertNotEqual(encrypted, test_string.encode())
        
        # Descifrar
        decrypted = self.test_encryption.decrypt_data(encrypted, 'string')
        self.assertEqual(decrypted, test_string)
    
    def test_encrypt_decrypt_dict(self):
        """Prueba cifrado y descifrado de diccionarios"""
        test_dict = {
            "user": "senor_ibero",
            "preferences": {
                "voice": "male",
                "language": "es-ES"
            },
            "timestamp": "2024-01-01T00:00:00"
        }
        
        # Cifrar
        encrypted = self.test_encryption.encrypt_data(test_dict)
        self.assertIsInstance(encrypted, bytes)
        
        # Descifrar
        decrypted = self.test_encryption.decrypt_data(encrypted, 'json')
        self.assertEqual(decrypted, test_dict)
    
    def test_encrypt_decrypt_list(self):
        """Prueba cifrado y descifrado de listas"""
        test_list = ["comando1", "comando2", "comando3"]
        
        # Cifrar
        encrypted = self.test_encryption.encrypt_data(test_list)
        self.assertIsInstance(encrypted, bytes)
        
        # Descifrar
        decrypted = self.test_encryption.decrypt_data(encrypted, 'json')
        self.assertEqual(decrypted, test_list)
    
    def test_encrypt_decrypt_auto_detection(self):
        """Prueba detección automática de tipo de datos"""
        test_data = {"test": "data", "number": 42}
        
        # Cifrar
        encrypted = self.test_encryption.encrypt_data(test_data)
        
        # Descifrar con detección automática
        decrypted = self.test_encryption.decrypt_data(encrypted, 'auto')
        self.assertEqual(decrypted, test_data)
    
    def test_encrypt_decrypt_file(self):
        """Prueba cifrado y descifrado de archivos"""
        # Crear archivo de prueba
        test_file = Path(self.temp_dir) / "test_file.txt"
        test_content = "Contenido secreto del archivo"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Cifrar archivo
        encrypted_file = Path(self.temp_dir) / "test_file.encrypted"
        self.test_encryption.encrypt_file(str(test_file), str(encrypted_file))
        
        self.assertTrue(encrypted_file.exists())
        
        # Verificar que el archivo cifrado es diferente
        with open(encrypted_file, 'rb') as f:
            encrypted_content = f.read()
        
        self.assertNotEqual(encrypted_content, test_content.encode())
        
        # Descifrar archivo
        decrypted_file = Path(self.temp_dir) / "test_file_decrypted.txt"
        self.test_encryption.decrypt_file(str(encrypted_file), str(decrypted_file))
        
        # Verificar contenido descifrado
        with open(decrypted_file, 'r', encoding='utf-8') as f:
            decrypted_content = f.read()
        
        self.assertEqual(decrypted_content, test_content)
    
    def test_key_persistence(self):
        """Prueba persistencia de claves"""
        # Crear primera instancia
        encryption1 = HamiltonEncryption()
        encryption1.key_file = Path(self.temp_dir) / "persistent_master.key"
        encryption1.salt_file = Path(self.temp_dir) / "persistent_salt.bin"
        encryption1._initialize_encryption()
        
        test_data = "Datos de prueba"
        encrypted1 = encryption1.encrypt_data(test_data)
        
        # Crear segunda instancia (debería cargar las mismas claves)
        encryption2 = HamiltonEncryption()
        encryption2.key_file = Path(self.temp_dir) / "persistent_master.key"
        encryption2.salt_file = Path(self.temp_dir) / "persistent_salt.bin"
        encryption2._initialize_encryption()
        
        # Debería poder descifrar datos de la primera instancia
        decrypted = encryption2.decrypt_data(encrypted1, 'string')
        self.assertEqual(decrypted, test_data)
    
    def test_invalid_decryption(self):
        """Prueba manejo de datos inválidos para descifrado"""
        invalid_data = b"datos_invalidos_no_cifrados"
        
        with self.assertRaises(Exception):
            self.test_encryption.decrypt_data(invalid_data, 'string')

class TestSecurityLogger(unittest.TestCase):
    """Pruebas para el logger de seguridad"""
    
    def setUp(self):
        """Configuración antes de cada prueba"""
        # Crear directorio temporal para logs
        self.temp_dir = tempfile.mkdtemp()
        
        # Configurar logger temporal
        import logging
        self.test_logger = logging.getLogger('hamilton.security')
        self.test_logger.setLevel(logging.INFO)
        
        # Handler temporal
        self.log_file = Path(self.temp_dir) / "test_security.log"
        handler = logging.FileHandler(self.log_file)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        # Limpiar handlers previos
        if self.test_logger.hasHandlers():
            self.test_logger.handlers.clear()
        self.test_logger.addHandler(handler)
        
        # Patch del logger y path en SecurityLogger
        import security.encryption as encryption_mod
        encryption_mod.security_logger = self.test_logger
        encryption_mod.SecurityLogger.security_logger = self.test_logger
        encryption_mod.SecurityLogger.log_path = self.log_file

    def tearDown(self):
        """Limpieza después de cada prueba"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_log_authentication_success(self):
        """Prueba logging de autenticación exitosa"""
        SecurityLogger.log_authentication_attempt(
            user="senor_ibero",
            success=True,
            method="facial",
            confidence=0.95
        )
        
        # Verificar que se escribió al log
        self.assertTrue(self.log_file.exists())
        
        with open(self.log_file, 'r') as f:
            log_content = f.read()
        
        self.assertIn("authentication_attempt", log_content)
        self.assertIn("senor_ibero", log_content)
        self.assertIn("true", log_content.lower())
    
    def test_log_authentication_failure(self):
        """Prueba logging de fallo de autenticación"""
        SecurityLogger.log_authentication_attempt(
            user="unknown_user",
            success=False,
            method="facial",
            confidence=0.2
        )
        
        with open(self.log_file, 'r') as f:
            log_content = f.read()
        
        self.assertIn("authentication_attempt", log_content)
        self.assertIn("unknown_user", log_content)
        self.assertIn("false", log_content.lower())
    
    def test_log_unauthorized_access(self):
        """Prueba logging de acceso no autorizado"""
        SecurityLogger.log_unauthorized_access(
            user="malicious_user",
            attempted_action="system_access"
        )
        
        with open(self.log_file, 'r') as f:
            log_content = f.read()
        
        self.assertIn("unauthorized_access", log_content)
        self.assertIn("malicious_user", log_content)
        self.assertIn("system_access", log_content)
    
    def test_log_data_access(self):
        """Prueba logging de acceso a datos"""
        SecurityLogger.log_data_access(
            user="senor_ibero",
            data_type="face_encodings",
            action="load"
        )
        
        with open(self.log_file, 'r') as f:
            log_content = f.read()
        
        self.assertIn("data_access", log_content)
        self.assertIn("face_encodings", log_content)
        self.assertIn("load", log_content)

class TestSecurityIntegration(unittest.TestCase):
    """Pruebas de integración del sistema de seguridad"""
    
    def setUp(self):
        """Configuración antes de cada prueba"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Limpieza después de cada prueba"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_face_encodings_encryption_flow(self):
        """Prueba flujo completo de cifrado de encodings faciales"""
        # Simular datos de encodings faciales
        face_data = {
            'encodings': [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]],
            'names': ['senor_ibero', 'senor_ibero'],
            'timestamp': '2024-01-01T00:00:00',
            'version': '1.0'
        }
        
        # Cifrar datos
        encryption = HamiltonEncryption()
        encryption.key_file = Path(self.temp_dir) / "test_master.key"
        encryption.salt_file = Path(self.temp_dir) / "test_salt.bin"
        encryption._initialize_encryption()
        
        encrypted_data = encryption.encrypt_data(face_data)
        
        # Guardar datos cifrados
        encrypted_file = Path(self.temp_dir) / "face_encodings.encrypted"
        with open(encrypted_file, 'wb') as f:
            f.write(encrypted_data)
        
        # Cargar y descifrar datos
        with open(encrypted_file, 'rb') as f:
            loaded_encrypted = f.read()
        
        decrypted_data = encryption.decrypt_data(loaded_encrypted, 'auto')
        
        # Verificar integridad
        self.assertEqual(decrypted_data, face_data)
        self.assertEqual(len(decrypted_data['encodings']), 2)
        self.assertEqual(decrypted_data['names'][0], 'senor_ibero')

if __name__ == '__main__':
    unittest.main()
